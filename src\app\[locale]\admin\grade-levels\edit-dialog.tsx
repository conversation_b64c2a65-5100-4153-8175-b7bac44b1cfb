"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

import { updateGradeLevel } from "@/lib/api/admin/grade-levels";
import { updateGradeLevelSchema, UpdateGradeLevelInput } from "@/lib/schemas/admin/grade-levels";
import { GradeLevelDto } from "@/lib/dto/admin/grade-level.dto";
import { getErrorMessage } from "@/lib/utils";

interface EditGradeLevelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  gradeLevel: GradeLevelDto | null;
}

export function EditGradeLevelDialog({
  open,
  onOpenChange,
  onSuccess,
  gradeLevel,
}: EditGradeLevelDialogProps) {
  const t = useTranslations('AdminGradeLevelsPage.form');

  const form = useForm<UpdateGradeLevelInput>({
    resolver: zodResolver(updateGradeLevelSchema),
    defaultValues: {
      nameEn: "",
      nameAr: "",
      levelOrder: 1,
    },
  });

  // Update form values when gradeLevel changes
  useEffect(() => {
    if (gradeLevel && open) {
      form.reset({
        nameEn: gradeLevel.nameEn,
        nameAr: gradeLevel.nameAr,
        levelOrder: gradeLevel.levelOrder,
      });
    }
  }, [gradeLevel, open, form]);

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateGradeLevelInput }) =>
      updateGradeLevel(id, data),
    onSuccess: () => {
      toast.success(t('updateSuccess'));
      onSuccess();
    },
    onError: (error) => {
      const errorMessage = getErrorMessage(error);
      toast.error(t('updateError') + ': ' + errorMessage);
    },
  });

  const onSubmit = (data: UpdateGradeLevelInput) => {
    if (!gradeLevel) return;
    
    // Only send fields that have changed
    const changedData: UpdateGradeLevelInput = {};
    if (data.nameEn !== gradeLevel.nameEn) changedData.nameEn = data.nameEn;
    if (data.nameAr !== gradeLevel.nameAr) changedData.nameAr = data.nameAr;
    if (data.levelOrder !== gradeLevel.levelOrder) changedData.levelOrder = data.levelOrder;

    // Only proceed if there are changes
    if (Object.keys(changedData).length === 0) {
      toast.info("No changes detected");
      return;
    }

    updateMutation.mutate({ id: gradeLevel.id, data: changedData });
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && !updateMutation.isPending) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  if (!gradeLevel) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('editTitle')}</DialogTitle>
          <DialogDescription>
            Update the grade level information below.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="nameEn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('nameEnLabel')}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('nameEnPlaceholder')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="nameAr"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('nameArLabel')}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('nameArPlaceholder')}
                        dir="rtl"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="levelOrder"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('levelOrderLabel')}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      max="999"
                      placeholder={t('levelOrderPlaceholder')}
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={updateMutation.isPending}
              >
                {t('cancel')}
              </Button>
              <Button
                type="submit"
                disabled={updateMutation.isPending}
              >
                {updateMutation.isPending ? t('updating') : t('update')}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
