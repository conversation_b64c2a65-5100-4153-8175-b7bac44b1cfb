"use client";

import * as React from "react";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useMutation, useQueryClient } from "@tanstack/react-query"; // Import useMutation and useQueryClient

import { But<PERSON> } from "@/components/ui/button";
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { GetJournalEntriesParams } from "@/lib/dto/admin/accounting/journal-entries.dto";
import { Input } from "@/components/ui/input";
import { PlusCircle } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { findJournalEntries } from "@/lib/api/admin/accounting/journal-entries";
import { format } from 'date-fns';
import { getColumns } from "./columns";
import { getErrorMessage } from "@/lib/utils"; // Import error message helper
import { postJournalEntry } from "@/lib/api/admin/accounting/journal-entries"; // Import post API
import { toast } from "sonner"; // Import toast
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "@/i18n/navigation"; // Import useRouter
import { useTranslations } from "next-intl";

// Import AlertDialog components

const DEFAULT_PAGE_SIZE = 10;

export function JournalEntriesTable() {
    // Call hooks at the top level of the component
    const t = useTranslations("AdminJournalEntriesPage.table");
    const tShared = useTranslations("Shared.dataTable");
    const tDialogs = useTranslations("Shared.confirmationDialog"); // Translations for dialogs
    const router = useRouter(); // Initialize router
    const queryClient = useQueryClient(); // Get query client

    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});
    const [pagination, setPagination] = React.useState({
        pageIndex: 0,
        pageSize: DEFAULT_PAGE_SIZE,
    });
    const [dateRange, setDateRange] = React.useState<DateRange | undefined>(undefined);
    const [postedStatusFilter, setPostedStatusFilter] = React.useState<string>("all"); // 'all', 'true', 'false'
    const [isPostConfirmOpen, setIsPostConfirmOpen] = React.useState(false);
    const [entryToPostId, setEntryToPostId] = React.useState<string | null>(null);

    // TODO: State for managing Add/Edit Modals/Sheets
    // const [isSheetOpen, setIsSheetOpen] = React.useState(false);
    // const [editingEntry, setEditingEntry] = React.useState<JournalEntryDto | null>(null);

    // Handler to open the post confirmation dialog
    const handlePostClick = (entryId: string) => {
        setEntryToPostId(entryId);
        setIsPostConfirmOpen(true);
    };

    // Pass the translation functions and handlers into getColumns
    const columns = React.useMemo(() => getColumns(
        // Pass action handlers here
        // openEditModal: (entry) => { setEditingEntry(entry); setIsSheetOpen(true); }, // Edit
        // openDeleteConfirm: (entry) => { /* Open Delete Confirm */ }, // Delete
        handlePostClick, // Pass post handler
        router, // Pass router instance
        t, // Pass t
        tShared // Pass tShared
    ), [router, t, tShared]); // Add router, t and tShared to dependency array

    const queryParams: GetJournalEntriesParams = {
        page: pagination.pageIndex,
        size: pagination.pageSize,
        sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
        referenceNumber: columnFilters.find(f => f.id === 'referenceNumber')?.value as string | undefined,
        startDate: dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined,
        endDate: dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined,
        isPosted: postedStatusFilter === 'all' ? undefined : postedStatusFilter === 'true',
    };

    const { data: pageData, isLoading, isError, error } = useQuery({
        queryKey: ["journalEntries", queryParams],
        queryFn: () => findJournalEntries(queryParams),
    });

    // Mutation for posting an entry
    const postMutation = useMutation({
        mutationFn: postJournalEntry,
        onSuccess: (data) => {
            toast.success(t("postSuccess", { id: data.id.substring(0, 8) }));
            queryClient.invalidateQueries({ queryKey: ["journalEntries"] }); // Refetch list
            queryClient.invalidateQueries({ queryKey: ["journalEntry", data.id] }); // Refetch specific entry if cached
            setIsPostConfirmOpen(false); // Close dialog
            setEntryToPostId(null);
        },
        onError: (error) => {
            toast.error(t("postError"), { description: getErrorMessage(error) });
            setIsPostConfirmOpen(false); // Close dialog even on error
            setEntryToPostId(null);
        },
    });

    const table = useReactTable({
        data: pageData?.content ?? [],
        columns,
        pageCount: pageData?.totalPages ?? 0,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
            pagination,
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        manualPagination: true, // Server-side pagination
        manualSorting: true,    // Server-side sorting
        manualFiltering: true, // Server-side filtering
    });

    const handlePostedStatusChange = (value: string) => {
        setPostedStatusFilter(value);
        // Optionally trigger refetch or rely on query key change
        table.getColumn('postedDate')?.setFilterValue(value); // Update table filter state if needed
    };

    // Handle Add button click
    const handleAddClick = () => {
        // Navigate to the dedicated add page
        router.push("/admin/accounting/journal-entries/add");
    };

    return (
        <div className="w-full space-y-4">
            <div className="flex items-center justify-between gap-4">
                <div className="flex flex-wrap items-center gap-2"> {/* Use flex-wrap for responsiveness */}
                    <Input
                        placeholder={t("filterByReference")}
                        value={(table.getColumn("referenceNumber")?.getFilterValue() as string) ?? ""}
                        onChange={(event) =>
                            table.getColumn("referenceNumber")?.setFilterValue(event.target.value)
                        }
                        className="h-8 w-[150px] lg:w-[250px]" // Consistent sizing
                    />
                    <DateRangePicker
                        date={dateRange}
                        onDateChange={setDateRange}
                        placeholder={t("filterByDate")}
                        className="h-8 w-[200px] lg:w-[250px]" // Consistent sizing
                    />
                    <Select value={postedStatusFilter} onValueChange={handlePostedStatusChange}>
                        <SelectTrigger className="h-8 w-[150px] lg:w-[180px]"> {/* Consistent sizing */}
                            <SelectValue placeholder={t("filterByStatus")} />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">{t("statusAll")}</SelectItem>
                            <SelectItem value="true">{t("statusPosted")}</SelectItem>
                            <SelectItem value="false">{t("statusUnposted")}</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex items-center gap-2">
                    <DataTableViewOptions table={table} />
                    <Button
                        onClick={handleAddClick}
                    >
                        <PlusCircle className="mr-2 h-4 w-4" />
                        {t("addJournalEntry")}
                    </Button>
                </div>
            </div>
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            Array.from({ length: pagination.pageSize }).map((_, index) => (
                                <TableRow key={`skeleton-${index}`}>
                                    {columns.map((column, colIndex) => (
                                        <TableCell key={`skeleton-${index}-${(column as any).id || colIndex}`}>
                                            <Skeleton className="h-6 w-full" />
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && "selected"}
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className="h-24 text-center"
                                >
                                    {isError ? tShared("errorLoadingData", { error: error instanceof Error ? error.message : String(error) }) : tShared("noResults")}
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            <DataTablePagination table={table} />

            {/* TODO: Add/Edit Sheet/Modal Component */}
            {/* <JournalEntryFormSheet
                isOpen={isSheetOpen}
                setIsOpen={setIsSheetOpen}
                entry={editingEntry}
            /> */}
            {/* TODO: Delete Confirmation Dialog */}

            {/* Post Confirmation Dialog */}
            <AlertDialog open={isPostConfirmOpen} onOpenChange={setIsPostConfirmOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{tDialogs("postTitle")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {tDialogs("postMessage", { item: t("journalEntry"), id: entryToPostId?.substring(0, 8) ?? '' })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={postMutation.isPending}>
                            {tDialogs("cancel")}
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => {
                                if (entryToPostId) {
                                    postMutation.mutate(entryToPostId);
                                }
                            }}
                            disabled={postMutation.isPending}
                            className="bg-primary text-primary-foreground hover:bg-primary/90" // Style confirm button
                        >
                            {postMutation.isPending ? tDialogs("posting") : tDialogs("postConfirm")}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}
