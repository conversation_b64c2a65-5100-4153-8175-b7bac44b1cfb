"use client";

import {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  SortingState,
  VisibilityState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { DataTable } from "@/components/ui/data-table";
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar";
import { DateRange } from "react-day-picker";
import { AiReportDto, CreateAiReportRequest, GetAiReportsParams, ReportType } from "@/lib/dto/admin/accounting/ai-reports.dto";
import { Skeleton } from "@/components/ui/skeleton";
import { getColumns } from "./columns";
import { format } from "date-fns";
import { useAiReports, useGenerateAiReport } from "@/lib/api/admin/accounting/ai-reports";
import { useTranslations } from "next-intl";
import { useState, useMemo, useEffect } from "react";

export function AiReportsDataTable() {
  const t = useTranslations("AiReportsPage");
  const tTable = useTranslations("AiReportsPage.table");
  const tShared = useTranslations("Shared");
  
  // Table state
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  
  // Filter state
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [reportType, setReportType] = useState<ReportType | undefined>(undefined);
  
  // Create query parameters
  const queryParams: GetAiReportsParams = {
    page: pagination.pageIndex,
    size: pagination.pageSize,
    sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
    reportType: reportType,
    startDate: dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
    endDate: dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
  };
  
  // Fetch data
  const { data, isLoading, isError, refetch } = useAiReports(queryParams);
  
  // Generate report mutation
  const generateMutation = useGenerateAiReport((newReport) => {
    refetch(); // Refetch the list after generating a new report
  });
  
  // Memoize columns
  const columns = useMemo(() => getColumns(tTable, tShared), [tTable, tShared]);
  
  // Create table instance
  const table = useReactTable({
    data: data?.content || [],
    columns,
    pageCount: data?.totalPages || -1,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
  });
  
  // Reset page index when filters change
  useEffect(() => {
    table.resetPageIndex(true);
  }, [dateRange, reportType, table]);
  
  // Handle filter reset
  const handleResetFilters = () => {
    setDateRange(undefined);
    setReportType(undefined);
  };
  
  // Handle generate report
  const handleGenerateReport = () => {
    if (!dateRange?.from || !dateRange?.to || !reportType) return;
    
    const request: CreateAiReportRequest = {
      reportType: reportType,
      startDate: format(dateRange.from, "yyyy-MM-dd"),
      endDate: format(dateRange.to, "yyyy-MM-dd"),
    };
    
    generateMutation.mutate(request);
  };
  
  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <DataTableToolbar
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
          reportType={reportType}
          onReportTypeChange={setReportType}
          onGenerateReport={handleGenerateReport}
          isGenerating={generateMutation.isPending}
          onResetFilters={handleResetFilters}
        />
        <div className="rounded-md border p-4">
          <Skeleton className="h-8 w-full mb-4" />
          {[...Array(pagination.pageSize)].map((_, index) => (
            <Skeleton key={index} className="h-6 w-full mb-2" />
          ))}
        </div>
        <Skeleton className="h-8 w-full" />
      </div>
    );
  }
  
  // Error state
  if (isError) {
    return <div className="text-red-500">{t("errorFetching")}</div>;
  }
  
  return (
    <div className="space-y-4">
      <DataTableToolbar
        dateRange={dateRange}
        onDateRangeChange={setDateRange}
        reportType={reportType}
        onReportTypeChange={setReportType}
        onGenerateReport={handleGenerateReport}
        isGenerating={generateMutation.isPending}
        onResetFilters={handleResetFilters}
      />
      <div className="rounded-md border">
        <DataTable<AiReportDto> table={table} columns={columns} />
      </div>
      <DataTablePagination table={table} />
    </div>
  );
}
