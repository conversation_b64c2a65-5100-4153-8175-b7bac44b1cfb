import { API_BASE_URL } from "@/lib/constants";
import { type PermissionDto } from "@/lib/dto/admin/permission.dto";
import { fetchWithAuth } from "@/lib/fetch-with-auth";

export async function getAllPermissions(): Promise<PermissionDto[]> {
  const url = `${API_BASE_URL}/admin/permissions`;
  try {
    const response = await fetchWithAuth(url);
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData?.message || `Failed to fetch permissions: ${response.statusText}`);
    }
    const data: unknown = await response.json();
    // TODO: Add type validation if necessary
    return data as PermissionDto[];
  } catch (error) {
    console.error("Error fetching permissions:", error);
    throw error instanceof Error ? error : new Error("An unknown error occurred while fetching permissions.");
  }
}

// Add getPermissionById, createPermission, updatePermission, deletePermission if needed
