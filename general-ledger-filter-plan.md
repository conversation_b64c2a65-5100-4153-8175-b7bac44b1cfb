# Plan for Adding 'isPosted' Filter to General Ledger

## Objective
Add an optional filter by 'isPosted' status (Posted, Unposted, All) to the general ledger page.

## Plan

1.  **Modify `GetGeneralLedgerParams` interface:**
    *   Add an optional `isPosted` property of type `boolean | null | undefined` to the `GetGeneralLedgerParams` interface in `src/lib/dto/admin/accounting/general-ledger.dto.ts`.

2.  **Update `useGetGeneralLedger` hook:**
    *   In `src/lib/api/admin/accounting/general-ledger.ts`, modify the `useGetGeneralLedger` hook to include the `isPosted` parameter in the query string if it's provided in the `params` object. Handle `null` and `boolean` values correctly when appending to the URLSearchParams.

3.  **Add 'isPosted' filter to `data-table-toolbar.tsx`:**
    *   Import necessary components: `Select`, `SelectContent`, `SelectItem`, `SelectTrigger`, `SelectValue` from `@/components/ui/select`.
    *   Add a state variable to manage the selected 'isPosted' value (e.g., `isPostedFilter`, initialized to `null` for "All").
    *   Add a `Select` component to the toolbar with options for "All" (null), "Posted" (true), and "Unposted" (false).
    *   Implement an event handler for the Select component's value change that updates the `isPostedFilter` state and calls a new `onIsPostedChange` prop.
    *   Add `onIsPostedChange` to the `DataTableToolbarProps` interface.

4.  **Update `GeneralLedgerDataTable` (likely in `page.tsx` or a separate component):**
    *   Add a state variable to manage the `isPosted` filter value that will be passed to the `useGetGeneralLedger` hook.
    *   Pass the `onIsPostedChange` handler down to the `GeneralLedgerDataTableToolbar` component.
    *   Pass the `isPosted` filter value to the `useGetGeneralLedger` hook.

5.  **Add translations:**
    *   Add necessary translation keys for the new filter options ("All", "Posted", "Unposted") in the translation files (`messages/en.json` and `messages/ar.json`).

## Component Interaction Diagram

```mermaid
graph TD
    A[page.tsx] --> B[GeneralLedgerDataTable]
    B --> C[useGetGeneralLedger Hook]
    B --> D[GeneralLedgerDataTableToolbar]
    D --> E[Select for isPosted Filter]
    E --> D
    D --> B
    B --> C
    C --> F[API Endpoint]