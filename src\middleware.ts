import { NextRequest, NextResponse } from 'next/server';
import createIntlMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing'; // Your i18n routing config
import { AUTH_COOKIE_NAME } from './stores/auth'; // Import the cookie name from auth store
const ADMIN_PATH_PREFIX = '/admin';
const LOGIN_PATH_SUFFIX = '/auth/login';

// List of public paths within the admin scope (if any) that bypass auth check
// Typically, only auth-related pages might be here, but usually they are outside /admin
const PUBLIC_ADMIN_PATHS: string[] = [
  // Example: '/admin/public-info' - Add any specific admin paths that DON'T need login
];

export default async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const handleI18nRouting = createIntlMiddleware(routing);

  // --- Step 1: Basic Path Analysis ---
  const locale = routing.locales.find(loc => pathname.startsWith(`/${loc}/`)) || routing.defaultLocale;
  const pathWithoutLocale = pathname.startsWith(`/${locale}/`) ? pathname.substring(`/${locale}`.length) : pathname;
  const isAdminPath = pathWithoutLocale.startsWith(ADMIN_PATH_PREFIX);

  console.log(`[Middleware] Path: ${pathname}, Locale: ${locale}, Path w/o Locale: ${pathWithoutLocale}, IsAdmin: ${isAdminPath}`);

  // --- Step 2: Authentication Check (ONLY for non-public admin paths) ---
  if (isAdminPath) {
    const isPublicAdminPath = PUBLIC_ADMIN_PATHS.some(publicPath => pathWithoutLocale === publicPath || pathWithoutLocale.startsWith(publicPath + '/'));

    if (!isPublicAdminPath) {
      // This is a protected admin path, check if the Zustand storage cookie exists.
      // Note: We are only checking for the *existence* of the cookie here.
      // The actual token validity and permissions are checked client-side in the layout.
      // However, the cookie contains the whole state, including the token.
      const authStorageCookie = request.cookies.get(AUTH_COOKIE_NAME)?.value;
      const hasAuthCookie = !!authStorageCookie;
      console.log(`[Middleware] Protected admin path detected. Auth cookie ('${AUTH_COOKIE_NAME}') present: ${hasAuthCookie}`);

      // Check if we're already on the login page to prevent redirect loops
      const isLoginPage = pathWithoutLocale === LOGIN_PATH_SUFFIX;

      if (isLoginPage) {
        console.log(`[Middleware] Already on login page. Proceeding to i18n handling.`);
      } else {
        // Try to parse the cookie to check if it actually contains a token
        let hasToken = false;
        let isAuthenticated = false;

        try {
          if (authStorageCookie) {
            const parsedState = JSON.parse(authStorageCookie);
            hasToken = !!parsedState?.state?.accessToken;
            isAuthenticated = !!parsedState?.state?.isAuthenticated;
            console.log(`[Middleware] Parsed cookie - Token exists: ${hasToken}, isAuthenticated: ${isAuthenticated}`);
          }
        } catch (e) {
          console.error("[Middleware] Error parsing auth cookie:", e);
          hasToken = false;
          isAuthenticated = false;
        }

        // Check if user is authenticated
        if (!hasAuthCookie || !hasToken || !isAuthenticated) {
          // No cookie, no token, or not authenticated - redirect to login
          const loginUrl = new URL(`/${locale}${LOGIN_PATH_SUFFIX}`, request.url);
          console.log(`[Middleware] Auth check failed. Cookie: ${hasAuthCookie}, Token: ${hasToken}, Authenticated: ${isAuthenticated}`);
          console.log(`[Middleware] Redirecting from ${pathname} to ${loginUrl.toString()}`);
          return NextResponse.redirect(loginUrl); // Redirect and EXIT middleware
        }

        // User is authenticated, allow request to proceed
        console.log(`[Middleware] Authentication check passed. Proceeding to i18n handling for ${pathname}.`);
      }
    } else {
      console.log(`[Middleware] Public admin path ${pathname}. Skipping auth check, proceeding to i18n handling.`);
    }
  } else {
    // Not an admin path, proceed directly to Step 3 (i18n handling)
    console.log(`[Middleware] Non-admin path ${pathname}. Proceeding to i18n handling.`);
  }

  // --- Step 3: Delegate to next-intl middleware ---
  // This runs for ALL requests that were not redirected by the auth check.
  console.log(`[Middleware] Delegating ${pathname} to next-intl.`);
  const response = await handleI18nRouting(request);
  return response;
}

// --- Step 4: Configure the Matcher (Crucial for performance and correctness) ---
export const config = {
  // Matcher adapted from next-intl documentation:
  // https://next-intl.vercel.app/docs/routing/middleware#matcher
  matcher: [
    // Exclude specific files and folders commonly found in /public or /.next
    '/((?!api|_next/static|_next/image|images|logo.png|favicon.ico).*)',
    // Match all routes that include a locale prefix (adapt list to your supported locales)
    '/(ar|en)/:path*' // <-- *** IMPORTANT: Ensure 'ar' and 'en' are your actual locales ***
  ],
  // Optional: A simpler matcher if the above causes issues (might be less performant):
  // matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)']
};
