import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { API_BASE_URL } from "@/lib/constants";
import { PaymentMethodDto, PagePaymentMethodDto } from "@/lib/dto/admin/accounting/payment-methods.dto";
import { CreatePaymentMethodInput, UpdatePaymentMethodInput } from "@/lib/schemas/admin/accounting/payment-methods";

interface GetPaymentMethodsParams {
    page?: number;
    size?: number;
    sort?: string[];
    search?: string; // Combined search for nameEn and nameAr
}

// Function to get all payment methods (active or inactive)
export async function getAllPaymentMethods(params: GetPaymentMethodsParams): Promise<PagePaymentMethodDto> {
    const queryParams = new URLSearchParams();
    if (params.page !== undefined) queryParams.append("page", params.page.toString());
    if (params.size !== undefined) queryParams.append("size", params.size.toString());
    if (params.sort) params.sort.forEach(s => queryParams.append("sort", s));
    if (params.search) queryParams.append("search", params.search);

    const url = `${API_BASE_URL}/accounting/payment-methods?${queryParams.toString()}`;
    console.log("Fetching URL:", url); // Log the URL

    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("API Error:", response.status, errorData);
            throw new Error(errorData?.message || `Failed to fetch payment methods: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary (e.g., using Zod)
        return data as PagePaymentMethodDto;
    } catch (error) {
        console.error("Network or parsing error:", error);
        throw error; // Re-throw the error after logging
    }
}

// Function to get only active payment methods
export async function getAllActivePaymentMethods(params: GetPaymentMethodsParams): Promise<PagePaymentMethodDto> {
    const queryParams = new URLSearchParams();
    if (params.page !== undefined) queryParams.append("page", params.page.toString());
    if (params.size !== undefined) queryParams.append("size", params.size.toString());
    if (params.sort) params.sort.forEach(s => queryParams.append("sort", s));
    if (params.search) queryParams.append("search", params.search);

    const url = `${API_BASE_URL}/accounting/payment-methods/active?${queryParams.toString()}`;
    console.log("Fetching Active URL:", url); // Log the URL

    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("API Error (Active):", response.status, errorData);
            throw new Error(errorData?.message || `Failed to fetch active payment methods: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as PagePaymentMethodDto;
    } catch (error) {
        console.error("Network or parsing error (Active):", error);
        throw error; // Re-throw the error after logging
    }
}


export async function getPaymentMethodById(id: string): Promise<PaymentMethodDto> {
    const url = `${API_BASE_URL}/accounting/payment-methods/${id}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch payment method ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation
        return data as PaymentMethodDto;
    } catch (error) {
        console.error("Fetch by ID error:", error);
        throw error;
    }
}

export async function createPaymentMethod(data: CreatePaymentMethodInput): Promise<PaymentMethodDto> {
    const url = `${API_BASE_URL}/accounting/payment-methods`;
    try {
        const response = await fetchWithAuth(url, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data),
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to create payment method: ${response.statusText}`);
        }
        const responseData: unknown = await response.json();
        // TODO: Add type validation
        return responseData as PaymentMethodDto;
    } catch (error) {
        console.error("Create error:", error);
        throw error;
    }
}

export async function updatePaymentMethod(id: string, data: UpdatePaymentMethodInput): Promise<PaymentMethodDto> {
    const url = `${API_BASE_URL}/accounting/payment-methods/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data),
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to update payment method ${id}: ${response.statusText}`);
        }
        const responseData: unknown = await response.json();
        // TODO: Add type validation
        return responseData as PaymentMethodDto;
    } catch (error) {
        console.error("Update error:", error);
        throw error;
    }
}

export async function deletePaymentMethod(id: string): Promise<void> {
    const url = `${API_BASE_URL}/accounting/payment-methods/${id}`;
    try {
        const response = await fetchWithAuth(url, { method: "DELETE" });
        if (!response.ok && response.status !== 204) { // Handle 204 No Content
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to delete payment method ${id}: ${response.statusText}`);
        }
        // No content expected on successful delete (204)
    } catch (error) {
        console.error("Delete error:", error);
        throw error;
    }
}

export async function activatePaymentMethod(id: string): Promise<PaymentMethodDto> {
    const url = `${API_BASE_URL}/accounting/payment-methods/${id}/activate`;
    try {
        const response = await fetchWithAuth(url, { method: "PUT" });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to activate payment method ${id}: ${response.statusText}`);
        }
        const responseData: unknown = await response.json();
        // TODO: Add type validation
        return responseData as PaymentMethodDto;
    } catch (error) {
        console.error("Activate error:", error);
        throw error;
    }
}

export async function deactivatePaymentMethod(id: string): Promise<PaymentMethodDto> {
    const url = `${API_BASE_URL}/accounting/payment-methods/${id}/deactivate`;
    try {
        const response = await fetchWithAuth(url, { method: "PUT" });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to deactivate payment method ${id}: ${response.statusText}`);
        }
        const responseData: unknown = await response.json();
        // TODO: Add type validation
        return responseData as PaymentMethodDto;
    } catch (error) {
        console.error("Deactivate error:", error);
        throw error;
    }
}
