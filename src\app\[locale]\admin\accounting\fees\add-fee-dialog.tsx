"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea"; // Assuming Textarea component exists
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker"; // Assuming DatePicker exists
import { createFee } from "@/lib/api/admin/accounting/fees";
import { CreateFeeInput, createFeeSchema } from "@/lib/schemas/admin/accounting/fees";
import { getErrorMessage } from "@/lib/utils";
import { getAllFeeCategoriesList } from "@/lib/api/admin/accounting/fee-categories"; // Correct function name
import { getAllGradeLevelsList } from "@/lib/api/admin/grade-levels"; // Correct function name
import { getAllEducationalStagesList } from "@/lib/api/admin/educational-stages"; // Correct function name
import { getAllBranchesList } from "@/lib/api/admin/branches"; // Correct function name
import { FeeCategoryDto } from "@/lib/dto/admin/accounting/fee-categories.dto";
import { GradeLevelDto } from "@/lib/dto/admin/grade-level.dto"; // Correct DTO path
import { EducationalStageDto } from "@/lib/dto/admin/educational-stage.dto"; // Assuming DTO exists
import { BranchDto } from "@/lib/dto/admin/branch.dto"; // Assuming DTO exists

interface AddFeeDialogProps {
    onFeeAdded?: () => void; // Optional callback
}

// Helper functions to fetch dropdown data (replace with actual API calls)
async function fetchFeeCategories(): Promise<FeeCategoryDto[]> {
    try {
        return await getAllFeeCategoriesList();
    } catch (error) {
        console.error("Failed to fetch fee categories:", error);
        toast.error("Failed to load fee categories for selection.");
        return [];
    }
}
async function fetchGradeLevels(): Promise<GradeLevelDto[]> {
     try {
        // Assuming an API function exists to get all grade levels
        return await getAllGradeLevelsList(); // Replace with actual function if different
    } catch (error) {
        console.error("Failed to fetch grade levels:", error);
        toast.error("Failed to load grade levels for selection.");
        return [];
    }
}
async function fetchEducationalStages(): Promise<EducationalStageDto[]> {
     try {
        // Assuming an API function exists to get all stages
        return await getAllEducationalStagesList(); // Replace with actual function if different
    } catch (error) {
        console.error("Failed to fetch educational stages:", error);
        toast.error("Failed to load educational stages for selection.");
        return [];
    }
}
async function fetchBranches(): Promise<BranchDto[]> {
     try {
        // Assuming an API function exists to get all branches
        return await getAllBranchesList(); // Replace with actual function if different
    } catch (error) {
        console.error("Failed to fetch branches:", error);
        toast.error("Failed to load branches for selection.");
        return [];
    }
}


export function AddFeeDialog({ onFeeAdded }: AddFeeDialogProps) {
    const t = useTranslations("AdminFeesPage.AddDialog");
    const queryClient = useQueryClient();
    const [isOpen, setIsOpen] = useState(false);

    const form = useForm<CreateFeeInput>({
        resolver: zodResolver(createFeeSchema(t)),
        defaultValues: {
            nameEn: "",
            nameAr: "",
            descriptionEn: "",
            descriptionAr: "",
            amount: 0,
            academicYear: "", // Consider setting a default based on current active year if possible
            dueDate: undefined,
            feeCategoryId: "",
            applicableGradeId: null,
            applicableStageId: null,
            applicableBranchId: null,
        },
    });

    // Fetch data for dropdowns
    const { data: feeCategories, isLoading: isLoadingCategories } = useQuery({
        queryKey: ["feeCategoriesList"],
        queryFn: fetchFeeCategories,
        enabled: isOpen, // Only fetch when dialog is open
        staleTime: 5 * 60 * 1000,
    });
     const { data: gradeLevels, isLoading: isLoadingGrades } = useQuery({
        queryKey: ["gradeLevelsList"],
        queryFn: fetchGradeLevels,
        enabled: isOpen,
        staleTime: 5 * 60 * 1000,
    });
     const { data: educationalStages, isLoading: isLoadingStages } = useQuery({
        queryKey: ["educationalStagesList"],
        queryFn: fetchEducationalStages,
        enabled: isOpen,
        staleTime: 5 * 60 * 1000,
    });
     const { data: branches, isLoading: isLoadingBranches } = useQuery({
        queryKey: ["branchesList"],
        queryFn: fetchBranches,
        enabled: isOpen,
        staleTime: 5 * 60 * 1000,
    });

    const mutation = useMutation({
        mutationFn: createFee,
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.nameEn }));
            queryClient.invalidateQueries({ queryKey: ["fees"] }); // Invalidate cache
            onFeeAdded?.(); // Call the callback
            setIsOpen(false); // Close dialog
            form.reset(); // Reset form
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: getErrorMessage(error) }));
        },
    });

    const onSubmit = (data: CreateFeeInput) => {
        // Format date to YYYY-MM-DD string before sending
        const formattedData = {
            ...data,
            dueDate: data.dueDate.toISOString().split('T')[0], // Format to YYYY-MM-DD
            // Ensure optional IDs are sent correctly (null or UUID)
            applicableGradeId: data.applicableGradeId || null,
            applicableStageId: data.applicableStageId || null,
            applicableBranchId: data.applicableBranchId || null,
        };
        console.log("Submitting Fee Data:", formattedData);
        mutation.mutate(formattedData);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button>{t("triggerButton")}</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description")}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            <FormField
                                control={form.control}
                                name="nameEn"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("nameEnLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="nameAr"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("nameArLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("nameArPlaceholder")} {...field} dir="rtl" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <FormField
                            control={form.control}
                            name="amount"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("amountLabel")}</FormLabel>
                                    <FormControl>
                                        <Input type="number" step="0.01" placeholder={t("amountPlaceholder")} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                         <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                             <FormField
                                control={form.control}
                                name="academicYear"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("academicYearLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("academicYearPlaceholder")} {...field} />
                                        </FormControl>
                                         <FormDescription>{t("academicYearDescription")}</FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                             <FormField
                                control={form.control}
                                name="dueDate"
                                render={({ field }) => (
                                    <FormItem className="flex flex-col pt-2">
                                        <FormLabel>{t("dueDateLabel")}</FormLabel>
                                        <FormControl className="mt-1.5">
                                             <DatePicker
                                                date={field.value}
                                                setDate={field.onChange}
                                                placeholder={t("dueDatePlaceholder")}
                                                className="w-full"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                         </div>

                         <FormField
                            control={form.control}
                            name="feeCategoryId"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("categoryLabel")}</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoadingCategories}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder={t("categoryPlaceholder")} />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {feeCategories?.map((category) => (
                                                <SelectItem key={category.id} value={category.id}>
                                                    {category.nameEn} / {category.nameAr}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Applicability Dropdowns */}
                         <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                             <FormField
                                control={form.control}
                                name="applicableGradeId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("applicableGradeLabel")}</FormLabel>
                                        <Select onValueChange={field.onChange} defaultValue={field.value ?? ""} disabled={isLoadingGrades}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("applicableGradePlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                               {/*<SelectItem value="--ALL--">{t("allGradesValue")}</SelectItem>*/}
                                                {gradeLevels?.map((grade) => (
                                                    <SelectItem key={grade.id} value={grade.id}>
                                                        {grade.nameEn} / {grade.nameAr}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                             <FormField
                                control={form.control}
                                name="applicableStageId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("applicableStageLabel")}</FormLabel>
                                        <Select onValueChange={field.onChange} defaultValue={field.value ?? ""} disabled={isLoadingStages}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("applicableStagePlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                 {/*<SelectItem value="--ALL--">{t("allStagesValue")}</SelectItem>*/}
                                                {educationalStages?.map((stage) => (
                                                    <SelectItem key={stage.id} value={stage.id}>
                                                        {stage.nameEn} / {stage.nameAr}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                             <FormField
                                control={form.control}
                                name="applicableBranchId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("applicableBranchLabel")}</FormLabel>
                                        <Select onValueChange={field.onChange} defaultValue={field.value ?? ""} disabled={isLoadingBranches}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("applicableBranchPlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                 {/*<SelectItem value="--ALL--">{t("allBranchesValue")}</SelectItem>*/}
                                                {branches?.map((branch) => (
                                                    <SelectItem key={branch.id} value={branch.id}>
                                                        {branch.nameEn} / {branch.nameAr}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                         </div>
                         <FormDescription>{t("applicabilityDescription")}</FormDescription>


                        <FormField
                            control={form.control}
                            name="descriptionEn"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                    <FormControl>
                                        <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} value={field.value ?? ''} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="descriptionAr"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                    <FormControl>
                                        <Textarea placeholder={t("descriptionArPlaceholder")} {...field} value={field.value ?? ''} dir="rtl" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
