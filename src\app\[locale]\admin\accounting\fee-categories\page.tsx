import { FeeCategoriesDataTable } from "./data-table"; // Adjust path
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default async function FeeCategoriesPage() {
    const t = await getTranslations("AdminFeeCategoriesPage"); // Adjust namespace

    return (
        <Card>
            <CardHeader>
                <CardTitle>{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent>
                <FeeCategoriesDataTable />
            </CardContent>
        </Card>
    );
}
