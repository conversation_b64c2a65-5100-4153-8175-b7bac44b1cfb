import { Page } from "@/lib/dto/common.dto";

// Based on #/components/schemas/GradeLevelDto
export interface GradeLevelDto {
    id: string;
    nameEn: string;
    nameAr: string;
    levelOrder: number;
    educationalStageId: string; // UUID
    createdAt: string; // ISO DateTime string
    updatedAt: string; // ISO DateTime string
}

// Based on #/components/schemas/CreateGradeLevelRequest
export interface CreateGradeLevelRequest {
    nameEn: string;
    nameAr: string;
    levelOrder: number;
    educationalStageId: string; // UUID
}

// Based on #/components/schemas/UpdateGradeLevelRequest
export interface UpdateGradeLevelRequest {
    nameEn?: string;
    nameAr?: string;
    levelOrder?: number;
}

// Parameters for fetching Grade Levels (assuming pagination might exist or be added)
// Based on GET /api/v1/admin/grade-levels query parameters
export interface GetGradeLevelsParams {
    stageId: string; // Required based on API
    page?: number;
    size?: number;
    sort?: string[];
}

// Although the /all endpoint isn't there, we might fetch with large size
// Define a Page type in case a paginated endpoint is used/added
export type PageGradeLevelDto = Page<GradeLevelDto>;

// Simplified DTO for lists/dropdowns
export interface SimpleGradeLevelDto {
    id: string;
    nameEn: string;
    nameAr: string;
    levelOrder: number;
}
