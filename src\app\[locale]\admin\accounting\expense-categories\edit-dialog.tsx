"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getExpenseCategoryById, updateExpenseCategory, getAllExpenseCategoriesList } from "@/lib/api/admin/accounting/expense-categories";
import { UpdateExpenseCategoryInput, updateExpenseCategorySchema } from "@/lib/schemas/admin/accounting/expense-categories";
import { UpdateExpenseCategoryRequest } from "@/lib/dto/admin/accounting/expense-categories.dto"; // Import request type
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";

interface EditExpenseCategoryDialogProps {
    categoryId: string;
    isOpen: boolean;
    onOpenChange: (isOpen: boolean) => void;
}

export function EditExpenseCategoryDialog({ categoryId, isOpen, onOpenChange }: EditExpenseCategoryDialogProps) {
    const t = useTranslations("AdminExpenseCategoriesPage.EditDialog");
    const tValidation = useTranslations("AdminExpenseCategoriesPage.EditDialog.validation");
    const tAdd = useTranslations("AdminExpenseCategoriesPage.AddDialog"); // Reuse some translations
    const queryClient = useQueryClient();

    const formSchema = updateExpenseCategorySchema(tValidation);

    // Fetch the specific category data
    const { data: categoryData, isLoading: isLoadingCategory, isError, error } = useQuery({
        queryKey: ["expense-category", categoryId],
        queryFn: () => getExpenseCategoryById(categoryId),
        enabled: isOpen, // Only fetch when the dialog is open
    });

    // Fetch potential parent categories
    const { data: parentCategoriesData, isLoading: isLoadingParents } = useQuery({
        queryKey: ["expense-categories-list"],
        queryFn: getAllExpenseCategoriesList,
        enabled: isOpen,
        staleTime: 300000,
    });

    const form = useForm<UpdateExpenseCategoryInput>({
        resolver: zodResolver(formSchema),
        defaultValues: { // Set default values once data is loaded
            nameEn: "",
            nameAr: "",
            descriptionEn: "",
            descriptionAr: "",
            parentCategoryId: undefined,
            // No 'active' field
        },
    });

    // Reset form when category data loads or changes
    useEffect(() => {
        if (categoryData) {
            form.reset({
                nameEn: categoryData.nameEn,
                nameAr: categoryData.nameAr,
                descriptionEn: categoryData.descriptionEn ?? "",
                descriptionAr: categoryData.descriptionAr ?? "",
                parentCategoryId: categoryData.parentCategory?.id ?? undefined,
            });
        }
    }, [categoryData, form]);


    const mutation = useMutation({
        // Ensure the mutation function expects the correct request type
        mutationFn: (data: UpdateExpenseCategoryRequest) => updateExpenseCategory(categoryId, data),
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.nameEn }));
            queryClient.invalidateQueries({ queryKey: ["expense-categories"] }); // Invalidate main table
            queryClient.invalidateQueries({ queryKey: ["expense-category", categoryId] }); // Invalidate this specific category
            queryClient.invalidateQueries({ queryKey: ["expense-categories-list"] }); // Invalidate list
            onOpenChange(false); // Close dialog
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message }));
        },
    });

    const onSubmit = (values: UpdateExpenseCategoryInput) => {
        // Construct the payload ensuring required fields are present
        // and optional fields are null if empty.
        const payload: UpdateExpenseCategoryRequest = {
            nameEn: values.nameEn || categoryData?.nameEn, // Ensure nameEn is sent (use original if somehow empty in form)
            nameAr: values.nameAr || categoryData?.nameAr, // Ensure nameAr is sent
            descriptionEn: values.descriptionEn || null, // Send null if empty
            descriptionAr: values.descriptionAr || null, // Send null if empty
            parentCategoryId: values.parentCategoryId || null, // Send null if empty/undefined
        }

        // Optional: Check if anything actually changed compared to original data
        if (payload.nameEn === categoryData?.nameEn &&
            payload.nameAr === categoryData?.nameAr &&
            payload.descriptionEn === (categoryData?.descriptionEn ?? null) &&
            payload.descriptionAr === (categoryData?.descriptionAr ?? null) &&
            payload.parentCategoryId === (categoryData?.parentCategory?.id ?? null))
        {
             toast.info(t("noChanges"));
             return;
        }

        mutation.mutate(payload);
    };

    const isLoading = isLoadingCategory || isLoadingParents;

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>{t("title", { name: categoryData?.nameEn ?? '...' })}</DialogTitle>
                    <DialogDescription>{t("description", { name: categoryData?.nameEn ?? '...' })}</DialogDescription>
                </DialogHeader>
                {isLoading ? (
                    <div className="space-y-4 py-4">
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-20 w-full" />
                        <Skeleton className="h-20 w-full" />
                    </div>
                ) : isError ? (
                     <div className="py-4 text-center text-destructive">
                        {t("errorLoadingCategory", { error: error?.message || 'Unknown error' })}
                    </div>
                ) : (
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <ScrollArea className="h-[50vh] pr-6">
                                <div className="space-y-4 p-1">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <FormField
                                            control={form.control}
                                            name="nameEn"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>{t("nameEnLabel")}</FormLabel>
                                                    <FormControl>
                                                        <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="nameAr"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>{t("nameArLabel")}</FormLabel>
                                                    <FormControl>
                                                        <Input dir="rtl" placeholder={t("nameArPlaceholder")} {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>

                                    <FormField
                                        control={form.control}
                                        name="parentCategoryId"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{tAdd("parentCategoryLabel")}</FormLabel>
                                                <Select
                                                    onValueChange={(value) => field.onChange(value === "__NONE__" ? undefined : value)}
                                                    value={field.value ?? "__NONE__"}
                                                    disabled={isLoadingParents}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            {isLoadingParents ? (
                                                                <Skeleton className="h-6 w-3/4" />
                                                            ) : (
                                                                <SelectValue placeholder={tAdd("parentCategoryPlaceholder")} />
                                                            )}
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        <SelectItem value="__NONE__">{tAdd("noParentValue")}</SelectItem>
                                                        {parentCategoriesData?.filter(cat => cat.id !== categoryId) // Exclude self
                                                            .map((category) => (
                                                            <SelectItem key={category.id} value={category.id}>
                                                                {category.nameEn} / {category.nameAr}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormDescription>{tAdd("parentCategoryDescription")}</FormDescription>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="descriptionEn"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                                <FormControl>
                                                    <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="descriptionAr"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                                <FormControl>
                                                    <Textarea dir="rtl" placeholder={t("descriptionArPlaceholder")} {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    {/* No 'active' switch */}
                                </div>
                            </ScrollArea>
                            <DialogFooter>
                                <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={mutation.isPending}>
                                    {t("cancelButton")}
                                </Button>
                                <Button type="submit" disabled={mutation.isPending || isLoading}>
                                    {mutation.isPending ? t("savingButton") : t("saveButton")}
                                </Button>
                            </DialogFooter>
                        </form>
                    </Form>
                )}
            </DialogContent>
        </Dialog>
    );
}
