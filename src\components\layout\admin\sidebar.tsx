"use client"; // Make it a client component for usePathname

import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";
import {
    BookText,
    Building,
    CalendarDays,
    CalendarHeart,
    Cog,
    CreditCard,
    GraduationCap,
    Home,
    Landmark,
    Layers3,
    LayoutGrid,
    ListFilter,
    Percent,
    Receipt,
    Settings,
    ShieldCheck,
    Tags,
    Users,
    UsersRound,
    Wallet
} from "lucide-react";

import Link from "next/link";
import Logo from "@/components/common/icons/logo";
import {cn} from "@/lib/utils";
import { usePathname } from "@/i18n/navigation"; // Use i18n navigation hook
import { useTranslations } from "next-intl";
import { useUiStore } from "@/stores/ui"; // Import the UI store

export function Sidebar({ className, variant = 'desktop' }: { className?: string, variant?: 'desktop' | 'mobile' }) {
    const t = useTranslations("AdminLayout.Sidebar");
    const pathname = usePathname(); // Get current path
    const { isSidebarOpen } = useUiStore(); // Get state from store

    // Define navigation structure with groups
    const navStructure = [
        {type: "link", href: "/admin/dashboard", label: t("dashboard"), icon: Home},
        {type: "divider"}, // Optional divider
        {type: "header", label: t("userManagementGroup")},
        {type: "link", href: "/admin/users", label: t("users"), icon: Users},
        {type: "link", href: "/admin/roles", label: t("roles"), icon: ShieldCheck},
        {type: "divider"}, // Optional divider
        {type: "header", label: t("studentsManagementGroup")},
        {type: "link", href: "/admin/students", label: t("students"), icon: GraduationCap},
        {type: "link", href: "/admin/guardians", label: t("guardians"), icon: UsersRound},
        {type: "divider"}, // Optional divider
        {type: "header", label: t("schoolManagementGroup")},
        {
            id: "school-configs", // Add unique ID
            type: "collapsible",
            label: t("configurations"), // Keep configurations if other items remain
            icon: Cog,
            children: [
                // {type: "link", href: "/admin/stages", label: t("stages"), icon: Layers3}, // Keep commented if moved
                {type: "link", href: "/admin/educational-stages", label: t("stages"), icon: Layers3}, // Corrected link
                {type: "link", href: "/admin/grade-levels", label: t("gradeLevels"), icon: GraduationCap},
                {type: "link", href: "/admin/sections", label: t("sections"), icon: LayoutGrid},
                {type: "link", href: "/admin/academic-years", label: t("academicYears"), icon: CalendarDays},
                {type: "link", href: "/admin/holidays", label: t("holidays"), icon: CalendarHeart},
                {type: "link", href: "/admin/branches", label: t("branches"), icon: Building},
            ],
        },

        {type: "divider"}, // Optional divider
        {type: "header", label: t("accountingGroup")},
        // Add the Journal Entries link here
        {type: "link", href: "/admin/accounting/journal-entries", label: t("journalEntries"), icon: BookText},
        // Add the Expenses link here
        {type: "link", href: "/admin/accounting/expenses", label: t("expenses"), icon: Wallet},
        {
            id: "accounting-reports", // Add unique ID for accounting reports collapsible
            type: "collapsible",
            label: t("reportsGroup"), // New reports group label
            icon: BookText, // Icon for reports group
            children: [
                {type: "link", href: "/admin/accounting/reports/general-ledger", label: t("generalLedger"), icon: BookText},
                {type: "link", href: "/admin/accounting/reports/trial-balance", label: t("trialBalance"), icon: ListFilter},
                {type: "link", href: "/admin/accounting/reports/balance-sheet", label: t("balanceSheet"), icon: Receipt},
                {type: "link", href: "/admin/accounting/reports/income-statement", label: t("incomeStatement"), icon: CreditCard},
                {type: "link", href: "/admin/accounting/reports/expenses", label: t("expenses"), icon: Wallet},
                {type: "link", href: "/admin/accounting/reports/ai-reports", label: t("aiReports"), icon: BookText},
            ],
        },
        {
            id: "accounting-configs", // Add unique ID for accounting collapsible
            type: "collapsible",
            label: t("configurations"),
            icon: Cog,
            children: [
                // { type: "link", href: "/admin/accounting/chart-of-accounts", label: t("chartOfAccounts"), icon: Landmark }, // Removed from here
                {
                    type: "link",
                    href: "/admin/accounting/chart-of-accounts",
                    label: t("chartOfAccounts"),
                    icon: Landmark
                },
                { type: "link", href: "/admin/accounting/payment-methods", label: t("paymentMethods"), icon: CreditCard },
                { type: "link", href: "/admin/accounting/fee-categories", label: t("feeCategories"), icon: Tags },
                { type: "link", href: "/admin/accounting/fees", label: t("fees"), icon: Receipt },
                { type: "link", href: "/admin/accounting/expense-categories", label: t("expenseCategories"), icon: ListFilter },
                { type: "link", href: "/admin/accounting/taxes", label: t("taxes"), icon: Percent }, // Add Taxes link
            ],
        },
        {type: "divider"}, // Optional divider
        {type: "link", href: "/admin/settings", label: t("settings"), icon: Settings},
    ];


    return (
        <aside
            className={cn(
                "bg-muted transition-all duration-300 ease-in-out overflow-hidden", // Base styles + transition + overflow
                variant === 'desktop' ?
                    cn(
                        "border-e", // Add border only for desktop variant
                        isSidebarOpen ? "w-[240px] opacity-100" : "w-0 opacity-0", // Conditional width and opacity for desktop
                        "hidden lg:block" // Hidden on small screens for desktop
                    ) :
                    "w-full h-full border-none", // Full width and height, no border for mobile variant
                className
            )}
            // Ensure the parent grid layout handles the 0 width correctly (which it does via lg:grid-cols-[0px_1fr])
        >
            {/* Content needs to be hidden when collapsed for desktop, always visible for mobile */}
            <div className={cn("flex h-full max-h-screen flex-col", variant === 'desktop' && !isSidebarOpen && "invisible")}>
                {/* Logo/Brand Area - Keep background consistent with Navbar */}
                <div className={cn("flex h-14 items-center border-b px-4 lg:h-[60px]", variant === 'desktop' ? 'lg:px-6' : 'px-4')}> {/* Adjust padding based on variant */}
                    <Link
                        prefetch={true}
                        href="/admin/dashboard"
                        className="flex items-center gap-2 font-semibold text-foreground"
                    >
                        {/* <Package2 className="h-6 w-6 text-primary"/>  */}
                        <Logo className="h-8 w-8"/>
                        <span className="text-lg">{t("panelTitle")}</span> {/* Slightly larger title */}
                    </Link>
                    {/* Optional: Add a notification bell or similar icon here */}
                    {/* <Button variant="outline" size="icon" className="ml-auto h-8 w-8">
            <Bell className="h-4 w-4" />
            <span className="sr-only">Toggle notifications</span>
          </Button> */}
                </div>
                {/* Navigation Area */}
                <nav className="flex-1 space-y-1 overflow-y-auto px-4 py-4 text-sm font-medium">
                    {navStructure.map((item, index) => {
                        if (item.type === "link") {
                            const isActive = pathname === item.href;
                            return (
                                <Link
                                    prefetch={true}
                                    key={`${item.type}-${item.label}`}
                                    href={item.href as string}
                                    className={cn(
                                        "flex items-center gap-3 rounded-lg px-3 py-2 transition-all", // Base styles
                                        isActive
                                            ? "bg-primary text-primary-foreground" // Active: Primary background, white text
                                            : "text-muted-foreground hover:bg-muted/50 hover:text-foreground" // Inactive: Muted text, slight bg change on hover
                                    )}
                                >
                                    {item.icon && <item.icon className="h-4 w-4"/>}
                                    {item.label}
                                </Link>
                            );
                        } else if (item.type === "header") {
                            return (
                                <h4
                                    key={`${item.type}-${item.label}`}
                                    className="mb-1 mt-3 px-3 text-xs font-semibold uppercase text-muted-foreground/80" // Group header styling
                                >
                                    {item.label}
                                </h4>
                            );
                        } else if (item.type === "divider") {
                            // Optional visual divider
                            return <hr key={`divider-${index}`} className="my-2 border-border/50"/>;
                        } else if (item.type === "collapsible") {
                            // Check if any child link is active
                            const isChildActive = item.children?.some(child => child.type === 'link' && pathname === child.href);
                            // Ensure unique key using id if available, otherwise index
                            const uniqueKey = item.id ? `${item.type}-${item.id}` : `${item.type}-${item.label}-${index}`;
                            return (
                                <Accordion key={uniqueKey} type="single" collapsible
                                           defaultValue={isChildActive ? item.label : undefined} className="w-full">
                                    <AccordionItem value={item.label as string} className="border-b-0">
                                        <AccordionTrigger
                                            className={cn(
                                                "flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:no-underline", // Base styles for trigger
                                                isChildActive
                                                    ? "text-primary" // Highlight trigger if a child is active
                                                    : "text-muted-foreground hover:text-foreground" // Inactive trigger styling
                                            )}
                                        >
                                            {item.icon && <item.icon className="h-4 w-4"/>}
                                            <span className="flex-1 text-left">{item.label}</span>
                                        </AccordionTrigger>
                                        <AccordionContent className="pt-1 pl-7"> {/* Indent content */}
                                            {item.children && item.children.map((child) => {
                                                if (child.type === "link") {
                                                    const isChildLinkActive = pathname === child.href;
                                                    return (
                                                        <Link
                                                            prefetch={true}
                                                            key={`${child.type}-${child.label}`}
                                                            href={child.href as string}
                                                            className={cn(
                                                                "flex items-center gap-3 rounded-lg px-3 py-2 transition-all text-sm", // Child link styles
                                                                isChildLinkActive
                                                                    ? "bg-primary/10 text-primary" // Active child: Primary accent
                                                                    : "text-muted-foreground hover:bg-muted/50 hover:text-foreground" // Inactive child
                                                            )}
                                                        >
                                                            {child.icon && <child.icon className="h-4 w-4"/>}
                                                            {child.label}
                                                        </Link>
                                                    );
                                                }
                                                return null;
                                            })}
                                        </AccordionContent>
                                    </AccordionItem>
                                </Accordion>
                            );
                        }
                        // Removed the duplicated block that caused the error
                        return null; // Handle potential unknown types
                    })}
                </nav>
                {/* Optional: Add extra content like settings link or user info at the bottom */}
                {/* <div className="mt-auto border-t p-4"> ... </div> */}
            </div>
        </aside>
    );
}
