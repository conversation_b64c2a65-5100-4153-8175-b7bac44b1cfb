import { z } from "zod";

// Schema for creating a new grade level
export const createGradeLevelSchema = z.object({
  nameEn: z
    .string()
    .min(1, "English name is required")
    .max(100, "English name cannot exceed 100 characters"),
  nameAr: z
    .string()
    .min(1, "Arabic name is required")
    .max(100, "Arabic name cannot exceed 100 characters"),
  levelOrder: z
    .number()
    .int("Level order must be an integer")
    .min(1, "Level order must be at least 1")
    .max(999, "Level order cannot exceed 999"),
  educationalStageId: z
    .string()
    .uuid("Please select a valid educational stage"),
});

// Schema for updating an existing grade level
export const updateGradeLevelSchema = z.object({
  nameEn: z
    .string()
    .min(1, "English name is required")
    .max(100, "English name cannot exceed 100 characters")
    .optional(),
  nameAr: z
    .string()
    .min(1, "Arabic name is required")
    .max(100, "Arabic name cannot exceed 100 characters")
    .optional(),
  levelOrder: z
    .number()
    .int("Level order must be an integer")
    .min(1, "Level order must be at least 1")
    .max(999, "Level order cannot exceed 999")
    .optional(),
});

// Type definitions for form inputs
export type CreateGradeLevelInput = z.infer<typeof createGradeLevelSchema>;
export type UpdateGradeLevelInput = z.infer<typeof updateGradeLevelSchema>;
