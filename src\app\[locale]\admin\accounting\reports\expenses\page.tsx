"use client";

import { DateRange } from "react-day-picker";
import { DataTableToolbar } from "./data-table-toolbar";
import { ErrorResponse } from "@/lib/dto/error-response.dto";
import { ExpensesReportDisplay } from "./expenses-report-display";
import { ExpenseReportDto } from "@/lib/dto/admin/accounting/expense-reports.dto";
import { PageHeader, PageHeaderDescription, PageHeaderHeading } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";
import { useExpenseReport } from "@/lib/api/admin/accounting/expense-reports";
import { useState } from "react";
import { useTranslations } from "next-intl";

export default function ExpenseReportPage() {
  const t = useTranslations("ExpenseReportPage");
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [shouldFetch, setShouldFetch] = useState(false);

  const startDate = dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined;
  const endDate = dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined;

  const {
    data: expenseReportData,
    isLoading,
    error,
  } = useExpenseReport(
    shouldFetch ? startDate : undefined,
    shouldFetch ? endDate : undefined
  );

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    setShouldFetch(false); // Reset fetch flag when date range changes
  };

  const handleGenerateReport = () => {
    if (dateRange?.from && dateRange?.to) {
      setShouldFetch(true);
    }
  };

  return (
    <>
      <PageHeader>
        <PageHeaderHeading>{t("title")}</PageHeaderHeading>
        <PageHeaderDescription>{t("description")}</PageHeaderDescription>
      </PageHeader>
      <div className="container mx-auto py-10">
        <DataTableToolbar
          onDateRangeChange={handleDateRangeChange}
          onGenerateReport={handleGenerateReport}
          isLoading={isLoading}
          dateRange={dateRange}
        />
        
        {isLoading && (
          <div className="space-y-4 mt-6">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        )}
        
        {error && (
          <div className="text-red-500 mt-6">
            {t("errorLoading")}: {(error as ErrorResponse).message}
          </div>
        )}
        
        {!shouldFetch && !isLoading && (
          <div className="text-center py-10 text-muted-foreground">
            {t("selectDatesAndGenerate")}
          </div>
        )}
        
        {expenseReportData && !isLoading && (
          <div className="mt-6">
            <ExpensesReportDisplay data={expenseReportData} />
          </div>
        )}
      </div>
    </>
  );
}
