// import { getRequestConfig } from 'next-intl/server';
// import { routing } from './routing'; // Import locales from routing

// export default getRequestConfig(async ({ requestLocale}) => {
//   let locale = await requestLocale;
// console.log("llllll", locale);

//   if (!locale || !routing.locales.includes(locale)) {
//        locale = routing.defaultLocale;
//      }    // Use imported locales

//   return {
//     locale,
//     messages: (await import(`../../messages/${locale}.json`)).default
//   };
// });
import {getRequestConfig} from 'next-intl/server';
import {hasLocale} from 'next-intl';
import {routing} from './routing';

export default getRequestConfig(async ({requestLocale}) => {
  // Typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default
  };
});