export interface BalanceSheetAccountDto {
  accountId: string;
  accountNumber: string;
  accountNameEn: string;
  accountNameAr: string;
  debit: number;
  credit: number;
  balance: number;
}

export interface BalanceSheetSectionDto {
  category: "ASSET" | "LIABILITY" | "EQUITY" | "REVENUE" | "EXPENSE";
  accounts: BalanceSheetAccountDto[];
  totalDebit: number;
  totalCredit: number;
  totalBalance: number;
}

export interface BalanceSheetDto {
  reportDate: string; // Assuming date string format
  assets: BalanceSheetSectionDto;
  liabilities: BalanceSheetSectionDto;
  equity: BalanceSheetSectionDto;
}
