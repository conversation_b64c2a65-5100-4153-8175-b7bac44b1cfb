import { Page } from "@/lib/dto/page.dto";
import { SimpleChartOfAccountDto } from "./chart-of-accounts.dto";

export interface JournalEntryLineDto {
    id: string;
    chartOfAccount: SimpleChartOfAccountDto;
    type: 'DEBIT' | 'CREDIT';
    amount: number;
    description?: string; // Optional based on schema
}

export interface JournalEntryDto {
    id: string;
    journalEntryNumber: string; // Added this field
    entryDate: string; // ISO Date string (e.g., "YYYY-MM-DD")
    description: string;
    referenceNumber?: string; // Optional based on schema
    lines: JournalEntryLineDto[];
    postedDate?: string; // ISO DateTime string, present if posted
    createdDate: string; // ISO DateTime string
    lastModifiedDate: string; // ISO DateTime string
}

export interface CreateJournalEntryLineRequest {
    chartOfAccountId: string; // UUID
    type: 'DEBIT' | 'CREDIT';
    amount: number; // Must be > 0
    description?: string; // Optional
}

export interface CreateJournalEntryRequest {
    entryDate: string; // ISO Date string (e.g., "YYYY-MM-DD")
    description: string;
    referenceNumber?: string; // Optional
    lines: CreateJournalEntryLineRequest[]; // Must contain at least one debit and one credit, and balance
}

// Update request reuses CreateJournalEntryLineRequest for lines
export interface UpdateJournalEntryRequest {
    entryDate: string; // ISO Date string (e.g., "YYYY-MM-DD")
    description: string;
    referenceNumber?: string; // Optional
    lines: CreateJournalEntryLineRequest[]; // Must contain at least one debit and one credit, and balance
}

export type PageJournalEntryDto = Page<JournalEntryDto>;

export interface GetJournalEntriesParams {
    page?: number;
    size?: number;
    sort?: string[];
    startDate?: string; // ISO Date string (e.g., "YYYY-MM-DD")
    endDate?: string;   // ISO Date string (e.g., "YYYY-MM-DD")
    referenceNumber?: string; // Search term
    isPosted?: boolean; // Filter by posted status
}
