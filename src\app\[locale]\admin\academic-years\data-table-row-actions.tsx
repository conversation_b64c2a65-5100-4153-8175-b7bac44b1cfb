"use client";

import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Pen, Trash, CheckCircle, XCircle } from "lucide-react";
import { AcademicYearDto } from "@/lib/dto/admin/academic-year.dto";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteAcademicYear, setActiveAcademicYear } from "@/lib/api/admin/academic-year";
import { toast } from "sonner";
import { useState } from "react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, Al<PERSON><PERSON><PERSON><PERSON>Header, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { EditAcademicYearDialog } from "./edit-dialog"; // Import Edit Dialog

interface DataTableRowActionsProps<TData> {
    row: Row<TData>;
}

export function DataTableRowActions<TData>({
    row,
}: DataTableRowActionsProps<TData>) {
    const academicYear = row.original as AcademicYearDto;
    const t = useTranslations("AdminAcademicYearsPage.table.actions");
    const tConfirm = useTranslations("Shared.confirmationDialog");
    const queryClient = useQueryClient();
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isActivateDialogOpen, setIsActivateDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

    const deleteMutation = useMutation({
        mutationFn: deleteAcademicYear,
        onSuccess: () => {
            toast.success(t("deleteSuccessToast", { name: academicYear.name }));
            queryClient.invalidateQueries({ queryKey: ["academic-years"] });
        },
        onError: (error) => {
            toast.error(t("deleteErrorToast", { error: error.message }));
        },
        onSettled: () => {
            setIsDeleteDialogOpen(false);
        }
    });

    const activateMutation = useMutation({
        mutationFn: setActiveAcademicYear,
        onSuccess: () => {
            toast.success(t("activateSuccessToast", { name: academicYear.name }));
            queryClient.invalidateQueries({ queryKey: ["academic-years"] });
        },
        onError: (error) => {
            toast.error(t("activateErrorToast", { error: error.message }));
        },
        onSettled: () => {
            setIsActivateDialogOpen(false);
        }
    });

    const handleDelete = () => {
        deleteMutation.mutate(academicYear.id);
    };

    const handleActivate = () => {
        activateMutation.mutate(academicYear.id);
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                    >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{t("openMenu")}</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                        <Pen className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("edit")}
                    </DropdownMenuItem>
                    {!academicYear.active && ( // Show activate only if not already active
                        <DropdownMenuItem onClick={() => setIsActivateDialogOpen(true)}>
                            <CheckCircle className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                            {t("activate")}
                        </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        variant="destructive"
                        onClick={() => setIsDeleteDialogOpen(true)}
                        disabled={deleteMutation.isPending}
                    >
                        <Trash className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("delete")}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Edit Dialog */}
            <EditAcademicYearDialog
                academicYear={academicYear}
                isOpen={isEditDialogOpen}
                onOpenChange={setIsEditDialogOpen}
            />

            {/* Confirmation Dialog for Delete */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{tConfirm("title")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {tConfirm("deleteMessage", { item: `${t("academicYearItem")} '${academicYear.name}'` })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={deleteMutation.isPending}>{tConfirm("cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            disabled={deleteMutation.isPending}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {deleteMutation.isPending ? tConfirm("deleting") : tConfirm("delete")}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Confirmation Dialog for Activate */}
            <AlertDialog open={isActivateDialogOpen} onOpenChange={setIsActivateDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{t("activateConfirmTitle")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {t("activateConfirmMessage", { name: academicYear.name })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={activateMutation.isPending}>{tConfirm("cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleActivate}
                            disabled={activateMutation.isPending}
                            className="bg-success text-success-foreground hover:bg-success/90" // Use success variant
                        >
                            {activateMutation.isPending ? t("activating") : t("activate")}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
