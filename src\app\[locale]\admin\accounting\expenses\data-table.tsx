"use client";

import * as React from "react";

import {
    ColumnFiltersState,
    PaginationState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { formatIsoDate, getErrorMessage } from "@/lib/utils"; // Helper to format date and get error message

import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar";
import { DateRange } from "react-day-picker";
import { GetExpensesParams } from "@/lib/dto/admin/accounting/expenses.dto";
import { Skeleton } from "@/components/ui/skeleton";
import { findExpenses } from "@/lib/api/admin/accounting/expenses";
import { getColumns } from "./columns";
import { useAuthStore } from "@/stores/auth"; // Import auth store
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export function ExpensesDataTable() {
    const t = useTranslations("AdminExpensesPage");
    const tShared = useTranslations("Shared");
    const { accessToken, isHydrated } = useAuthStore(); // Get accessToken and hydration status

    const [rowSelection, setRowSelection] = React.useState({});
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [{ pageIndex, pageSize }, setPagination] = React.useState<PaginationState>({
        pageIndex: 0,
        pageSize: 10,
    });

    // Specific state for date range filter
    const [dateRange, setDateRange] = React.useState<DateRange | undefined>(undefined);
    // Specific state for category filter
    const [categoryFilter, setCategoryFilter] = React.useState<string>("");

    const pagination = React.useMemo(
        () => ({ pageIndex, pageSize }),
        [pageIndex, pageSize]
    );

    // Prepare query parameters
    const queryParams: GetExpensesParams = {
        page: pageIndex,
        size: pageSize,
        sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
        startDate: dateRange?.from ? formatIsoDate(dateRange.from) : undefined,
        endDate: dateRange?.to ? formatIsoDate(dateRange.to) : undefined,
        categoryId: categoryFilter || undefined,
    };

    // Fetch data using TanStack Query
    const { data, isLoading, error, refetch, isFetching } = useQuery({
        queryKey: ["expenses", queryParams], // Include query params in the key
        queryFn: () => findExpenses(queryParams),
        enabled: isHydrated && !!accessToken, // Only run query when hydrated and accessToken exists
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    // Get translations specifically for the table section
    const tTable = useTranslations("AdminExpensesPage.table");
    const columns = React.useMemo(() => getColumns({ t: tTable, tShared: tShared }), [tTable, tShared]);

    const table = useReactTable({
        data: data?.content ?? [], // Use fetched data or empty array
        columns,
        pageCount: data?.totalPages ?? 0, // Use totalPages from API response
        state: {
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters, // Keep this if you use built-in filters alongside custom ones
            pagination,
        },
        enableRowSelection: true,
        manualPagination: true, // We handle pagination server-side
        manualSorting: true, // We handle sorting server-side
        manualFiltering: true, // We handle filtering server-side (via queryParams)
        onPaginationChange: setPagination,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters, // Still needed for visibility toggle
        onColumnVisibilityChange: setColumnVisibility,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(), // Needed for client-side filtering if any
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(), // Needed for view options
        getFacetedUniqueValues: getFacetedUniqueValues(), // Needed for view options
    });

    // Handle loading, initial hydration, and error states
    const renderTableContent = () => {
        // Show skeleton if loading OR if not hydrated yet (initial load)
        if (isLoading || !isHydrated || isFetching) {
            return Array.from({ length: pageSize }).map((_, rowIndex) => (
                <TableRow key={`skeleton-row-${rowIndex}`}>
                    {columns.map((column, colIndex) => (
                        // Use colIndex as fallback for key if column.id is undefined
                        <TableCell key={`skeleton-cell-${rowIndex}-${column.id ?? colIndex}`}>
                            <Skeleton className="h-6 w-full" />
                        </TableCell>
                    ))}
                </TableRow>
            ));
        }

        if (error) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center text-destructive">
                        {t("errorLoading", { error: getErrorMessage(error) })}
                    </TableCell>
                </TableRow>
            );
        }

        if (!table.getRowModel().rows?.length) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        {t("noResults")}
                    </TableCell>
                </TableRow>
            );
        }

        // Render actual rows
        return table.getRowModel().rows.map((row) => (
            <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
            >
                {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                ))}
            </TableRow>
        ));
    };

    return (
        <div className="space-y-4">
            <DataTableToolbar
                table={table}
                dateRange={dateRange}
                setDateRange={setDateRange}
                categoryFilter={categoryFilter}
                setCategoryFilter={setCategoryFilter}
                onExpenseAdded={refetch} // Pass refetch to Add/Edit sheet trigger
            />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id} colSpan={header.colSpan}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {renderTableContent()}
                    </TableBody>
                </Table>
            </div>
            <DataTablePagination table={table} t={useTranslations('Shared.dataTable.pagination')} />
        </div>
    );
}
