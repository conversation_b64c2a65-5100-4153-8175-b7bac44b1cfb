"use client";

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import React, { useEffect, useState } from 'react'; // Import useState, useEffect

import { AddStageDialog } from './add-dialog';
import { DataTable } from './data-table';
import { EducationalStageDto } from '@/lib/dto/admin/educational-stage.dto';
import { Skeleton } from '@/components/ui/skeleton'; // For loading state
import { Terminal } from 'lucide-react';
import { columns } from './columns';
import { getAllEducationalStagesList } from '@/lib/api/admin/educational-stages';
import { useQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';

export default function AdminEducationalStagesPage() {
    const tPage = useTranslations('AdminEducationalStagesPage'); // Translations for the page itself
    const tTable = useTranslations('AdminEducationalStagesPage.table'); // Translations specifically for the table columns
    const [isClient, setIsClient] = useState(false); // State to track client mount

    // Set isClient to true only after component mounts
    useEffect(() => {
        setIsClient(true);
    }, []);

    const { data: stages, isLoading, error, refetch } = useQuery<EducationalStageDto[], Error>({
        queryKey: ['educationalStages'],
        queryFn: getAllEducationalStagesList,
    });

    const handleStageAdded = () => {
        refetch(); // Refetch data after adding a new stage
    };

    const handleStageUpdated = () => {
        refetch(); // Refetch data after updating a stage
    };

     const handleStageDeleted = () => {
        refetch(); // Refetch data after deleting a stage
    };

    return (
        <div className="container mx-auto py-10">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-3xl font-bold">{tPage('title')}</h1>
                    <p className="text-muted-foreground">{tPage('description')}</p>
                </div>
                {/* Conditionally render Add button only on client to potentially avoid hydration issues with DialogTrigger */}
                {/*{isClient && <AddStageDialog onStageAdded={handleStageAdded} />}*/}
            </div>

            {/* Only render main content after client mount */}
            {!isClient && ( // Show skeletons during SSR and before client mount
                <div className="space-y-4">
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                </div>
            )}

            {isClient && isLoading && ( // Show skeletons on client while loading
                <div className="space-y-4">
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-12 w-full" />
                </div>
            )}

            {error && (
                 <Alert variant="destructive">
                    <Terminal className="h-4 w-4" />
                    <AlertTitle>{tPage('errorLoadingTitle')}</AlertTitle>
                    <AlertDescription>{tPage('errorLoading', { error: error.message })}</AlertDescription>
                </Alert>
            )}

            {/* Pass the tTable function obtained at the top level down to columns */}
            {isClient && !isLoading && !error && stages && (
                <DataTable
                    columns={columns({ t: tTable, onStageUpdated: handleStageUpdated, onStageDeleted: handleStageDeleted })}
                    data={stages}
                />
            )}
             {/* Show no results message only on client */}
             {isClient && !isLoading && !error && (!stages || stages.length === 0) && (
                 <div className="text-center text-muted-foreground mt-8">
                     {tPage('noResults')}
                 </div>
             )}
        </div>
    );
}
