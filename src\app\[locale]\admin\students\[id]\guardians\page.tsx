"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { getStudentById, linkGuardianToStudent, unlinkGuardianFromStudent } from "@/lib/api/admin/students";
import { getGuardians } from "@/lib/api/admin/guardians";
import { GuardianDto } from "@/lib/dto/admin/guardian.dto";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal, ArrowLeft, UserPlus, UserMinus } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

export default function ManageGuardiansPage() {
  const t = useTranslations("ManageGuardiansPage");
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const studentId = params.id as string;
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [selectedGuardianId, setSelectedGuardianId] = useState<string>("");

  // Fetch student data
  const { data: student, isLoading: isLoadingStudent, isError: isErrorStudent, error: studentError } = useQuery({
    queryKey: ["student", studentId],
    queryFn: () => getStudentById(studentId),
  });

  // Fetch all guardians
  const { data: allGuardians, isLoading: isLoadingGuardians } = useQuery({
    queryKey: ["guardians"],
    queryFn: () => getGuardians(),
  });

  // Filter out guardians that are already linked to the student
  const availableGuardians = allGuardians?.filter(
    (guardian) => !student?.guardians.some((g) => g.id === guardian.id)
  );

  // Link guardian mutation
  const linkMutation = useMutation({
    mutationFn: () => linkGuardianToStudent(studentId, selectedGuardianId),
    onSuccess: () => {
      toast.success(t("linkSuccess"));
      queryClient.invalidateQueries({ queryKey: ["student", studentId] });
      setIsAddDialogOpen(false);
      setSelectedGuardianId("");
    },
    onError: (error) => {
      toast.error(t("linkError", { error: error instanceof Error ? error.message : String(error) }));
    },
  });

  // Unlink guardian mutation
  const unlinkMutation = useMutation({
    mutationFn: (guardianId: string) => unlinkGuardianFromStudent(studentId, guardianId),
    onSuccess: () => {
      toast.success(t("unlinkSuccess"));
      queryClient.invalidateQueries({ queryKey: ["student", studentId] });
    },
    onError: (error) => {
      toast.error(t("unlinkError", { error: error instanceof Error ? error.message : String(error) }));
    },
  });

  const handleLinkGuardian = () => {
    if (selectedGuardianId) {
      linkMutation.mutate();
    }
  };

  const handleUnlinkGuardian = (guardianId: string) => {
    unlinkMutation.mutate(guardianId);
  };

  if (isLoadingStudent) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/4 mb-2" />
            <Skeleton className="h-4 w-1/3" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isErrorStudent) {
    return (
      <Alert variant="destructive">
        <Terminal className="h-4 w-4" />
        <AlertTitle>{t("errorTitle")}</AlertTitle>
        <AlertDescription>
          {studentError instanceof Error ? studentError.message : t("unknownError")}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">
            {t("title", { name: `${student.userAccount.firstName} ${student.userAccount.lastName}` })}
          </h1>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              {t("addGuardian")}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("addGuardianTitle")}</DialogTitle>
              <DialogDescription>{t("addGuardianDescription")}</DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <Select
                value={selectedGuardianId}
                onValueChange={setSelectedGuardianId}
                disabled={isLoadingGuardians}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("selectGuardian")} />
                </SelectTrigger>
                <SelectContent>
                  {availableGuardians?.map((guardian) => (
                    <SelectItem key={guardian.id} value={guardian.id}>
                      {guardian.userAccount.firstName} {guardian.userAccount.lastName} ({guardian.userAccount.email})
                    </SelectItem>
                  ))}
                  {availableGuardians?.length === 0 && (
                    <SelectItem value="none" disabled>
                      {t("noAvailableGuardians")}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
              >
                {t("cancel")}
              </Button>
              <Button
                onClick={handleLinkGuardian}
                disabled={!selectedGuardianId || linkMutation.isPending}
              >
                {linkMutation.isPending ? t("linking") : t("link")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("currentGuardiansTitle")}</CardTitle>
          <CardDescription>{t("currentGuardiansDescription")}</CardDescription>
        </CardHeader>
        <CardContent>
          {student.guardians.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("name")}</TableHead>
                  <TableHead>{t("email")}</TableHead>
                  <TableHead>{t("phone")}</TableHead>
                  <TableHead>{t("occupation")}</TableHead>
                  <TableHead>{t("nationalId")}</TableHead>
                  <TableHead>{t("actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {student.guardians.map((guardian) => (
                  <TableRow key={guardian.id}>
                    <TableCell className="font-medium">
                      {guardian.firstName} {guardian.lastName}
                    </TableCell>
                    <TableCell>{guardian.email}</TableCell>
                    <TableCell>{guardian.phoneNumber || t("notProvided")}</TableCell>
                    <TableCell>{guardian.occupation}</TableCell>
                    <TableCell>
                      {guardian.nationalId}{" "}
                      <Badge variant="outline" className="ml-2">
                        {t(`idTypes.${guardian.idType.toLowerCase()}`)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleUnlinkGuardian(guardian.id)}
                        disabled={unlinkMutation.isPending}
                      >
                        <UserMinus className="mr-2 h-4 w-4" />
                        {t("unlink")}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <p className="text-muted-foreground">{t("noGuardians")}</p>
              <Button
                className="mt-4"
                onClick={() => setIsAddDialogOpen(true)}
              >
                <UserPlus className="mr-2 h-4 w-4" />
                {t("addFirstGuardian")}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
