import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { NextIntlClientProvider } from 'next-intl';
import GradeLevelsPage from '../page';

// Mock the API functions
vi.mock('@/lib/api/admin/grade-levels', () => ({
  getAllGradeLevelsList: vi.fn(),
}));

vi.mock('@/lib/api/admin/educational-stages', () => ({
  getAllEducationalStagesList: vi.fn(),
}));

// Mock the navigation hook
vi.mock('@/i18n/navigation', () => ({
  usePathname: () => '/admin/grade-levels',
}));

const mockMessages = {
  AdminGradeLevelsPage: {
    title: 'Grade Levels Management',
    description: 'Manage grade levels within educational stages.',
    addNewButton: 'Add New Grade Level',
    filterByStage: 'Filter by Educational Stage',
    allStages: 'All Educational Stages',
    table: {
      nameEn: 'English Name',
      nameAr: 'Arabic Name',
      levelOrder: 'Order',
      educationalStage: 'Educational Stage',
      actions: 'Actions',
    },
  },
  Shared: {
    Messages: {
      error: 'Error',
      unexpectedError: 'An unexpected error occurred',
      loading: 'Loading...',
    },
    Actions: {
      refresh: 'Refresh',
    },
    DataTable: {
      searchPlaceholder: 'Search...',
      noResults: 'No results found.',
    },
  },
};

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        {children}
      </NextIntlClientProvider>
    </QueryClientProvider>
  );
};

describe('GradeLevelsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the page title and description', async () => {
    const { getAllGradeLevelsList } = await import('@/lib/api/admin/grade-levels');
    const { getAllEducationalStagesList } = await import('@/lib/api/admin/educational-stages');
    
    vi.mocked(getAllGradeLevelsList).mockResolvedValue([]);
    vi.mocked(getAllEducationalStagesList).mockResolvedValue([]);

    render(<GradeLevelsPage />, { wrapper: createWrapper() });

    expect(screen.getByText('Grade Levels Management')).toBeInTheDocument();
    expect(screen.getByText('Manage grade levels within educational stages.')).toBeInTheDocument();
    expect(screen.getByText('Add New Grade Level')).toBeInTheDocument();
  });

  it('shows loading state initially', async () => {
    const { getAllGradeLevelsList } = await import('@/lib/api/admin/grade-levels');
    const { getAllEducationalStagesList } = await import('@/lib/api/admin/educational-stages');
    
    vi.mocked(getAllGradeLevelsList).mockImplementation(() => new Promise(() => {}));
    vi.mocked(getAllEducationalStagesList).mockResolvedValue([]);

    render(<GradeLevelsPage />, { wrapper: createWrapper() });

    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
  });

  it('displays error message when API fails', async () => {
    const { getAllGradeLevelsList } = await import('@/lib/api/admin/grade-levels');
    const { getAllEducationalStagesList } = await import('@/lib/api/admin/educational-stages');
    
    vi.mocked(getAllGradeLevelsList).mockRejectedValue(new Error('API Error'));
    vi.mocked(getAllEducationalStagesList).mockResolvedValue([]);

    render(<GradeLevelsPage />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByText('Error')).toBeInTheDocument();
    });
  });
});
