"use client";

import { Row } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Trash2, Pencil } from "lucide-react";
import { useTranslations } from "next-intl";
import { ExpenseDto } from "@/lib/dto/admin/accounting/expenses.dto";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteExpense } from "@/lib/api/admin/accounting/expenses";
import { toast } from "sonner";
import { getErrorMessage } from "@/lib/utils";
import { useState } from "react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialog<PERSON>ooter,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eader,
    <PERSON>ertDialog<PERSON>itle,
} from "@/components/ui/alert-dialog";
import { AddEditExpenseSheet } from "./add-edit-expense-sheet"; // Import the sheet

interface DataTableRowActionsProps<TData extends ExpenseDto> {
    row: Row<TData>;
    // Add onExpenseUpdated callback if needed from parent
}

export function DataTableRowActions<TData extends ExpenseDto>({
    row,
}: DataTableRowActionsProps<TData>) {
    const t = useTranslations("AdminExpensesPage.table");
    const tShared = useTranslations("Shared");
    const queryClient = useQueryClient();
    const expense = row.original;

    const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState(false);
    const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);

    // --- Delete Mutation ---
    const deleteMutation = useMutation({
        mutationFn: () => deleteExpense(expense.id),
        onSuccess: () => {
            toast.success(t("deleteSuccessToast", { id: expense.id })); // Use ID or description
            queryClient.invalidateQueries({ queryKey: ["expenses"] }); // Invalidate cache
            setIsConfirmDeleteDialogOpen(false);
        },
        onError: (error: unknown) => {
            toast.error(t('deleteErrorToast', { error: getErrorMessage(error) }));
            setIsConfirmDeleteDialogOpen(false);
        }
    });

    const handleEditOpen = () => {
        setIsEditSheetOpen(true);
    };

    const handleEditSuccess = () => {
        queryClient.invalidateQueries({ queryKey: ["expenses"] });
        // Optionally call onExpenseUpdated callback if passed
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                    >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{tShared("dataTable.openMenu")}</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuItem onClick={handleEditOpen}>
                        <Pencil className="mr-2 h-4 w-4" />
                        {tShared("dataTable.edit")}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        className="text-destructive focus:text-destructive focus:bg-destructive/10"
                        onClick={() => setIsConfirmDeleteDialogOpen(true)}
                    >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {tShared("dataTable.delete")}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Edit Sheet */}
            <AddEditExpenseSheet
                expenseId={expense.id} // Pass ID for editing
                isOpen={isEditSheetOpen}
                onOpenChange={setIsEditSheetOpen}
                onSuccess={handleEditSuccess}
            />

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={isConfirmDeleteDialogOpen} onOpenChange={setIsConfirmDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{tShared("confirmationDialog.title")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {tShared("confirmationDialog.deleteMessage", { item: t("expenseItem") })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>{tShared("confirmationDialog.cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => deleteMutation.mutate()}
                            disabled={deleteMutation.isPending}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {deleteMutation.isPending ? tShared("confirmationDialog.deleting") : tShared("confirmationDialog.delete")}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
