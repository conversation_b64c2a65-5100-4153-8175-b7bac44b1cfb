import { type UserDto, type PageUserDto } from "@/lib/dto/admin/user.dto"; // Assuming DTO types exist
import { fetchWithAuth } from "@/lib/fetch-with-auth"; // Assuming a helper for authenticated fetches
import { API_BASE_URL } from "@/lib/constants"; // Assuming API base URL constant

export interface GetUsersParams {
  page?: number; // Zero-based page index
  size?: number;
  sort?: string[]; // e.g., ["email,asc", "firstName,desc"]
  roleName?: string;
  // Add other potential filter params like search query if API supports it
}

// Type guard to check if the response is a PageUserDto
function isPageUserDto(data: unknown): data is PageUserDto {
  return (
    typeof data === "object" &&
    data !== null &&
    "content" in data &&
    Array.isArray((data as PageUserDto).content) &&
    "totalPages" in data &&
    "totalElements" in data &&
    "number" in data && // page number
    "size" in data // page size
  );
}


export async function getUsers(params: GetUsersParams = {}): Promise<PageUserDto> {
  const { page = 0, size = 10, sort, roleName } = params;

  const queryParams = new URLSearchParams({
    page: page.toString(),
    size: size.toString(),
  });

  if (sort) {
    sort.forEach((s) => queryParams.append("sort", s));
  }
  if (roleName) {
    queryParams.set("roleName", roleName);
  }
  // Add search query param if needed: queryParams.set("search", searchQuery);

  const url = `${API_BASE_URL}/admin/users?${queryParams.toString()}`;

  try {
    const response = await fetchWithAuth(url); // Use authenticated fetch

    if (!response.ok) {
      // Attempt to parse error response
      const errorData = await response.json().catch(() => ({})); // Graceful error parsing
      throw new Error(
        errorData?.message || `Failed to fetch users: ${response.statusText}`
      );
    }

    const data: unknown = await response.json();

    // Validate the structure of the response
    if (!isPageUserDto(data)) {
        console.error("Invalid API response structure:", data);
        throw new Error("Invalid API response structure for users.");
    }

    return data;
  } catch (error) {
    console.error("Error fetching users:", error);
    // Re-throw the error so TanStack Query can handle it
    throw error instanceof Error ? error : new Error("An unknown error occurred while fetching users.");
  }
}

// Define DTO types locally if not imported (replace with actual imports)
// These should ideally come from a shared types definition or generated from OpenAPI spec
declare module "@/lib/dto/admin/user.dto" {
    export interface UserDto {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        phoneNumber?: string;
        enabled: boolean;
        roles: string[];
        createdAt: string; // ISO date string
        updatedAt: string; // ISO date string
    }

    export interface PageUserDto {
        content: UserDto[];
        totalPages: number;
        totalElements: number;
        size: number;
        number: number; // Current page number (zero-based)
        numberOfElements: number; // Number of elements on the current page
        first: boolean;
        last: boolean;
        empty: boolean;
        // Include sort and pageable objects if needed from API response
    }
}
