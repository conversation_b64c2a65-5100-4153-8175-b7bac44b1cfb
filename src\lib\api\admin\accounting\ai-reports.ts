import { API_BASE_URL } from "@/lib/constants";
import { 
  AiReportDto, 
  CreateAiReportRequest, 
  GetAiReportsParams, 
  PageAiReportDto, 
  ReportType 
} from "@/lib/dto/admin/accounting/ai-reports.dto";
import { ErrorResponse } from "@/lib/dto/error-response.dto";
import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { buildQueryString } from "@/lib/utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

const AI_REPORTS_API_PATH = `${API_BASE_URL}/accounting/ai-reports`;

/**
 * Fetches all AI reports with optional filtering
 */
export async function getAiReports(params: GetAiReportsParams = {}): Promise<PageAiReportDto> {
  const queryString = buildQueryString({
    reportType: params.reportType,
    startDate: params.startDate,
    endDate: params.endDate,
    page: params.page,
    size: params.size,
    sort: params.sort,
  });

  const url = `${AI_REPORTS_API_PATH}${queryString ? `?${queryString}` : ""}`;

  const response = await fetchWithAuth(url, {
    method: "GET",
  });

  const data = await response.json();

  if (!response.ok) {
    throw data as ErrorResponse;
  }

  return data as PageAiReportDto;
}

/**
 * Generates a new AI report
 */
export async function generateAiReport(request: CreateAiReportRequest): Promise<AiReportDto> {
  const response = await fetchWithAuth(AI_REPORTS_API_PATH, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(request),
  });

  const data = await response.json();

  if (!response.ok) {
    throw data as ErrorResponse;
  }

  return data as AiReportDto;
}

/**
 * Gets a specific AI report by ID
 */
export async function getAiReportById(id: string): Promise<AiReportDto> {
  const response = await fetchWithAuth(`${AI_REPORTS_API_PATH}/${id}`, {
    method: "GET",
  });

  const data = await response.json();

  if (!response.ok) {
    throw data as ErrorResponse;
  }

  return data as AiReportDto;
}

/**
 * React Query hook for fetching AI reports
 */
export function useAiReports(params: GetAiReportsParams = {}) {
  return useQuery<PageAiReportDto, ErrorResponse>({
    queryKey: ["aiReports", params],
    queryFn: async () => {
      return await getAiReports(params);
    },
  });
}

/**
 * React Query hook for generating an AI report
 */
export function useGenerateAiReport(onSuccess?: (data: AiReportDto) => void) {
  return useMutation<AiReportDto, ErrorResponse, CreateAiReportRequest>({
    mutationFn: generateAiReport,
    onSuccess: (data) => {
      toast.success("Report generation started. It will be available soon.");
      if (onSuccess) {
        onSuccess(data);
      }
    },
    onError: (error) => {
      toast.error(`Failed to generate report: ${error.message}`);
    },
  });
}

/**
 * React Query hook for fetching a specific AI report
 */
export function useAiReport(id: string) {
  return useQuery<AiReportDto, ErrorResponse>({
    queryKey: ["aiReport", id],
    queryFn: async () => {
      return await getAiReportById(id);
    },
    enabled: !!id,
  });
}
