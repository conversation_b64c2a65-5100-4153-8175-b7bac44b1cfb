@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* Base */
  --background: oklch(1 0 0); /* White */
  --foreground: oklch(0.145 0 0); /* Near Black */
  /* Card */
  --card: oklch(1 0 0); /* White */
  --card-foreground: oklch(0.145 0 0); /* Near Black */
  /* Popover */
  --popover: oklch(1 0 0); /* White */
  --popover-foreground: oklch(0.145 0 0); /* Near Black */
  /* Primary (Rust - #B14E00) */
  --primary: oklch(51.91% 0.141 45.47);
  --primary-foreground: oklch(0.985 0 0); /* Near White */
  /* Secondary (Teal - #1C5954) */
  --secondary: oklch(40.51% 0.061 198.01);
  --secondary-foreground: oklch(0.985 0 0); /* Near White */
  /* Muted (Stone - #C2C1B1 derived) */
  --muted: oklch(95% 0.01 100.03); /* Lighter Stone */
  --muted-foreground: oklch(55% 0.04 100.03); /* Darker Stone */
  /* Accent (Gold - #D6AA48) */
  --accent: oklch(72.81% 0.111 84.5);
  --accent-foreground: oklch(0.145 0 0); /* Near Black */
  /* Destructive (Keep default red) */
  --destructive: oklch(0.577 0.245 27.325);
  /* Border (Stone - #C2C1B1) */
  --border: oklch(79.78% 0.019 100.03);
  /* Input (Stone - #C2C1B1 derived) */
  --input: oklch(85% 0.019 100.03); /* Lighter Stone */
  /* Ring (Primary/Rust with opacity) */
  --ring: oklch(51.91% 0.141 45.47 / 0.5);
  /* Chart colors (Keep defaults for now, can be themed later) */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* Base */
  --background: oklch(0.145 0 0); /* Near Black */
  --foreground: oklch(0.985 0 0); /* Near White */
  /* Card */
  --card: oklch(0.205 0 0); /* Dark Gray */
  --card-foreground: oklch(0.985 0 0); /* Near White */
  /* Popover */
  --popover: oklch(0.205 0 0); /* Dark Gray */
  --popover-foreground: oklch(0.985 0 0); /* Near White */
  /* Primary (Rust - #B14E00 derived) */
  --primary: oklch(60% 0.141 45.47); /* Lighter Rust */
  --primary-foreground: oklch(0.145 0 0); /* Near Black */
  /* Secondary (Teal - #1C5954 derived) */
  --secondary: oklch(50% 0.061 198.01); /* Lighter Teal */
  --secondary-foreground: oklch(0.985 0 0); /* Near White */
  /* Muted (Stone - #C2C1B1 derived) */
  --muted: oklch(0.269 0 0); /* Dark Gray */
  --muted-foreground: oklch(70% 0.019 100.03); /* Lighter Stone */
  /* Accent (Gold - #D6AA48 derived) */
  --accent: oklch(80% 0.111 84.5); /* Lighter Gold */
  --accent-foreground: oklch(0.145 0 0); /* Near Black */
  /* Destructive (Keep default red) */
  --destructive: oklch(0.704 0.191 22.216);
  /* Border (Stone - #C2C1B1 derived) */
  --border: oklch(30% 0.019 100.03); /* Darker Stone */
  /* Input (Stone - #C2C1B1 derived) */
  --input: oklch(35% 0.019 100.03); /* Slightly Lighter Dark Stone */
  /* Ring (Primary/Rust derived with opacity) */
  --ring: oklch(60% 0.141 45.47 / 0.5);
  /* Chart colors (Keep defaults for now, can be themed later) */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
