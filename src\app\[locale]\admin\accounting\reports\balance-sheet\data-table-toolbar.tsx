"use client";

import { useEffect, useState } from "react"; // Import useState and useEffect
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import { DateRange } from "react-day-picker"; // Import DateRange type
import { DateRangePicker } from "@/components/ui/date-range-picker";

export function DataTableToolbar() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const fromDateParam = searchParams.get("fromDate");
  const toDateParam = searchParams.get("toDate");

  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);

  useEffect(() => {
    setDateRange({
      from: fromDateParam ? new Date(fromDateParam) : undefined,
      to: toDateParam ? new Date(toDateParam) : undefined,
    });
  }, [fromDateParam, toDateParam]);

  const handleDateChange = (range: DateRange | undefined) => {
    setDateRange(range);
    const params = new URLSearchParams(searchParams.toString());
    if (range?.from) {
      params.set("fromDate", range.from.toISOString().split("T")[0]);
    } else {
      params.delete("fromDate");
    }
    if (range?.to) {
      params.set("toDate", range.to.toISOString().split("T")[0]);
    } else {
      params.delete("toDate");
    }
    router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        {/* Add other filters here if needed */}
      </div>
      <DateRangePicker
        date={dateRange} // Use 'date' prop
        onDateChange={handleDateChange} // Use 'onDateChange' prop
      />
    </div>
  );
}
