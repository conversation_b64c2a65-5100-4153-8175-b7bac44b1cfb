import { SimpleExpenseCategoryDto } from "./expense-categories.dto";
import { SimpleTaxDto } from "./taxes.dto";
import { SimpleChartOfAccountDto } from "./chart-of-accounts.dto";

export interface ExpenseReportDto {
  startDate: string;
  endDate: string;
  expenses: ExpenseDto[];
  categorySummaries: ExpenseReportSummaryDto[];
  totalAmount: number;
  totalTaxAmount: number;
  totalAmountWithTax: number;
  totalExpenseCount: number;
}

export interface ExpenseReportSummaryDto {
  categoryId: string;
  categoryNameEn: string;
  categoryNameAr: string;
  totalAmount: number;
  totalTaxAmount: number;
  totalAmountWithTax: number;
  expenseCount: number;
  percentageOfTotal: number;
}

export interface ExpenseDto {
  id: string;
  expenseDate: string;
  amountBeforeTax: number;
  taxAmount: number;
  totalAmount: number;
  descriptionEn: string;
  descriptionAr: string;
  vendor?: string | null;
  referenceNumber?: string | null;
  category: SimpleExpenseCategoryDto;
  tax?: SimpleTaxDto | null;
  paymentAccount: SimpleChartOfAccountDto;
  createdDate: string;
  lastModifiedDate: string;
}
