import { z } from "zod";
import { UseTranslations } from "next-intl";

// Helper function to create the schema with translations
export const createTaxSchema = (t: UseTranslations<string>) => z.object({
    nameEn: z.string()
        .min(1, { message: t("validation.nameEnRequired") })
        .max(100, { message: t("validation.nameEnTooLong") }),
    nameAr: z.string()
        .min(1, { message: t("validation.nameArRequired") })
        .max(100, { message: t("validation.nameArTooLong") }),
    percent: z.coerce // Use coerce for better handling of string inputs from forms
        .number({ invalid_type_error: t("validation.percentInvalid") })
        .min(0, { message: t("validation.percentMin") })
        .max(100, { message: t("validation.percentMax") }),
    descriptionEn: z.string()
        .max(500, { message: t("validation.descriptionEnTooLong") })
        .optional()
        .nullable(), // Allow null or undefined
    descriptionAr: z.string()
        .max(500, { message: t("validation.descriptionArTooLong") })
        .optional()
        .nullable(), // Allow null or undefined
    chartOfAccountId: z.string().uuid({ message: t("validation.chartOfAccountIdRequired") }), // Add required UUID validation
});

// Schema for updating (similar to create, but fields might be optional depending on API)
// Assuming PUT requires all fields as per tax-api.json, but making them optional
// allows for partial updates if the API supports PATCH or if PUT handles missing fields gracefully.
// If PUT strictly requires all fields, remove .optional()
export const updateTaxSchema = (t: UseTranslations<string>) => z.object({
    nameEn: z.string()
        .min(1, { message: t("validation.nameEnRequired") })
        .max(100, { message: t("validation.nameEnTooLong") })
        .optional(), // Make optional for partial updates if needed
    nameAr: z.string()
        .min(1, { message: t("validation.nameArRequired") })
        .max(100, { message: t("validation.nameArTooLong") })
        .optional(), // Make optional
    percent: z.coerce
        .number({ invalid_type_error: t("validation.percentInvalid") })
        .min(0, { message: t("validation.percentMin") })
        .max(100, { message: t("validation.percentMax") })
        .optional(), // Make optional
    descriptionEn: z.string()
        .max(500, { message: t("validation.descriptionEnTooLong") })
        .optional()
        .nullable(),
    descriptionAr: z.string()
        .max(500, { message: t("validation.descriptionArTooLong") })
        .optional()
        .nullable(),
    chartOfAccountId: z.string().uuid({ message: t("validation.chartOfAccountIdRequired") }).optional(), // Add optional UUID validation for update
});


// Infer TypeScript types from schemas
export type CreateTaxInput = z.infer<ReturnType<typeof createTaxSchema>>;
export type UpdateTaxInput = z.infer<ReturnType<typeof updateTaxSchema>>;
