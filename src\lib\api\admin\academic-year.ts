import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { API_BASE_URL } from "@/lib/constants";
import {
    AcademicYearDto,
    CreateAcademicYearRequest,
    UpdateAcademicYearRequest,
    PageAcademicYearDto,
    GetAcademicYearsParams
} from "@/lib/dto/admin/academic-year.dto";

/**
 * Fetches a paginated list of academic years (all or active).
 * Corresponds to GET /api/v1/admin/academic-years or /api/v1/admin/academic-years/active
 */
export async function searchAcademicYears(params: GetAcademicYearsParams): Promise<PageAcademicYearDto> {
    const queryParams = new URLSearchParams();
    if (params.page !== undefined) queryParams.append("page", params.page.toString());
    if (params.size !== undefined) queryParams.append("size", params.size.toString());
    if (params.sort) params.sort.forEach(s => queryParams.append("sort", s));
    if (params.search) queryParams.append("search", params.search);

    // Determine the endpoint based on the activeOnly flag
    const endpoint = params.activeOnly ? "/admin/academic-years/active" : "/admin/academic-years";
    const url = `${API_BASE_URL}${endpoint}?${queryParams.toString()}`;
    console.log("Fetching academic years from:", url);

    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Failed to fetch academic years:", response.status, response.statusText, errorData);
            throw new Error(errorData?.message || `Failed to fetch academic years: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as PageAcademicYearDto;
    } catch (error) {
        console.error("Error in searchAcademicYears:", error);
        throw error;
    }
}

/**
 * Fetches a single academic year by its ID.
 * Corresponds to GET /api/v1/admin/academic-years/{id}
 */
export async function getAcademicYearById(id: string): Promise<AcademicYearDto> {
    const url = `${API_BASE_URL}/admin/academic-years/${id}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch academic year ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as AcademicYearDto;
    } catch (error) {
        console.error(`Error fetching academic year ${id}:`, error);
        throw error;
    }
}

/**
 * Creates a new academic year.
 * Corresponds to POST /api/v1/admin/academic-years
 */
export async function createAcademicYear(request: CreateAcademicYearRequest): Promise<AcademicYearDto> {
    const url = `${API_BASE_URL}/admin/academic-years`;
    try {
        const response = await fetchWithAuth(url, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(request),
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to create academic year: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as AcademicYearDto;
    } catch (error) {
        console.error("Error creating academic year:", error);
        throw error;
    }
}

/**
 * Updates an existing academic year.
 * Corresponds to PUT /api/v1/admin/academic-years/{id}
 */
export async function updateAcademicYear(id: string, request: UpdateAcademicYearRequest): Promise<AcademicYearDto> {
    const url = `${API_BASE_URL}/admin/academic-years/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(request),
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to update academic year ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as AcademicYearDto;
    } catch (error) {
        console.error(`Error updating academic year ${id}:`, error);
        throw error;
    }
}

/**
 * Deletes an academic year by its ID.
 * Corresponds to DELETE /api/v1/admin/academic-years/{id}
 */
export async function deleteAcademicYear(id: string): Promise<void> {
    const url = `${API_BASE_URL}/admin/academic-years/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: "DELETE",
        });
        if (!response.ok && response.status !== 200 && response.status !== 204) { // Allow 200 or 204 for DELETE
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to delete academic year ${id}: ${response.statusText}`);
        }
        // Consume body if present (for 200 OK)
        if (response.status === 200 && response.headers.get("content-length") && parseInt(response.headers.get("content-length")!, 10) > 0) {
             await response.json();
        }
    } catch (error) {
        console.error(`Error deleting academic year ${id}:`, error);
        throw error;
    }
}

/**
 * Sets an academic year as active.
 * Corresponds to POST /api/v1/admin/academic-years/{id}/activate
 */
export async function setActiveAcademicYear(id: string): Promise<AcademicYearDto> {
    const url = `${API_BASE_URL}/admin/academic-years/${id}/activate`;
    try {
        const response = await fetchWithAuth(url, {
            method: "POST",
            // No body needed for this request based on API docs
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to activate academic year ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as AcademicYearDto;
    } catch (error) {
        console.error(`Error activating academic year ${id}:`, error);
        throw error;
    }
}

/**
 * Fetches all academic years as a simple list (not paginated).
 * Useful for dropdowns and selection components.
 */
export async function getAllAcademicYearsList(): Promise<AcademicYearDto[]> {
    try {
        // Fetch with a large page size to get all academic years
        const result = await searchAcademicYears({ page: 0, size: 1000 });
        return result.content || [];
    } catch (error) {
        console.error("Error fetching all academic years list:", error);
        throw error;
    }
}
