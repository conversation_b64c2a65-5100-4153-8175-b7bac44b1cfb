"use client";

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { createEducationalStage } from '@/lib/api/admin/educational-stages';
import { CreateEducationalStageInput, createEducationalStageSchema } from '@/lib/schemas/admin/educational-stage';
import { handleApiError } from '@/lib/api-error-handler'; // Corrected import path

interface AddStageDialogProps {
    onStageAdded: () => void; // Callback to refresh data
}

export function AddStageDialog({ onStageAdded }: AddStageDialogProps) {
    const t = useTranslations('AdminEducationalStagesPage.AddDialog');
    const tValidation = useTranslations('AdminEducationalStagesPage.AddDialog.validation');
    const [isOpen, setIsOpen] = useState(false);

    const formSchema = createEducationalStageSchema(tValidation);
    const form = useForm<CreateEducationalStageInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            nameEn: '',
            nameAr: '',
            sortOrder: undefined, // Initialize as undefined for number input
        },
    });

    const mutation = useMutation({
        mutationFn: createEducationalStage,
        onSuccess: (data) => {
            toast.success(t('successToast', { name: data.nameEn }));
            form.reset();
            setIsOpen(false);
            onStageAdded(); // Trigger data refresh in the parent component
        },
        onError: (error) => {
            handleApiError(error, t('errorToast'));
        },
    });

    const onSubmit = (values: CreateEducationalStageInput) => {
        mutation.mutate(values);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button>{t('triggerButton')}</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>{t('title')}</DialogTitle>
                    <DialogDescription>{t('description')}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="nameEn"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('nameEnLabel')}</FormLabel>
                                    <FormControl>
                                        <Input placeholder={t('nameEnPlaceholder')} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="nameAr"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('nameArLabel')}</FormLabel>
                                    <FormControl>
                                        <Input dir="rtl" placeholder={t('nameArPlaceholder')} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                         <FormField
                            control={form.control}
                            name="sortOrder"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('sortOrderLabel')}</FormLabel>
                                    <FormControl>
                                        <Input type="number" placeholder={t('sortOrderPlaceholder')} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                {t('cancelButton')}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t('savingButton') : t('saveButton')}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
