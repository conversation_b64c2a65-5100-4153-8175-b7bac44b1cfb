import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { API_BASE_URL } from "@/lib/constants";
import {
    CreateExpenseCategoryRequest,
    ExpenseCategoryDto,
    GetExpenseCategoriesParams,
    PageExpenseCategoryDto,
    UpdateExpenseCategoryRequest
} from "@/lib/dto/admin/accounting/expense-categories.dto";

const EXPENSE_CATEGORIES_API_URL = `${API_BASE_URL}/accounting/expense-categories`;

// GET /api/v1/accounting/expense-categories (Paginated)
export async function getAllExpenseCategories(params: GetExpenseCategoriesParams): Promise<PageExpenseCategoryDto> {
    const queryParams = new URLSearchParams();
    if (params.page !== undefined) queryParams.append('page', params.page.toString());
    if (params.size !== undefined) queryParams.append('size', params.size.toString());
    if (params.sort) params.sort.forEach(s => queryParams.append('sort', s));
    if (params.searchTerm) queryParams.append('searchTerm', params.searchTerm); // Use 'searchTerm'

    const url = `${EXPENSE_CATEGORIES_API_URL}?${queryParams.toString()}`;
    const response = await fetchWithAuth(url);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to fetch expense categories: ${response.statusText}`);
    }
    return await response.json() as PageExpenseCategoryDto;
}

// GET /api/v1/accounting/expense-categories/all (List)
export async function getAllExpenseCategoriesList(): Promise<ExpenseCategoryDto[]> {
    const url = `${EXPENSE_CATEGORIES_API_URL}/all`;
    const response = await fetchWithAuth(url);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to fetch expense categories list: ${response.statusText}`);
    }
    return await response.json() as ExpenseCategoryDto[];
}


// GET /api/v1/accounting/expense-categories/{id}
export async function getExpenseCategoryById(id: string): Promise<ExpenseCategoryDto> {
    const url = `${EXPENSE_CATEGORIES_API_URL}/${id}`;
    const response = await fetchWithAuth(url);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to fetch expense category ${id}: ${response.statusText}`);
    }
    return await response.json() as ExpenseCategoryDto;
}

// POST /api/v1/accounting/expense-categories
export async function createExpenseCategory(data: CreateExpenseCategoryRequest): Promise<ExpenseCategoryDto> {
    const response = await fetchWithAuth(EXPENSE_CATEGORIES_API_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to create expense category: ${response.statusText}`);
    }
    return await response.json() as ExpenseCategoryDto;
}

// PUT /api/v1/accounting/expense-categories/{id}
export async function updateExpenseCategory(id: string, data: UpdateExpenseCategoryRequest): Promise<ExpenseCategoryDto> {
    const url = `${EXPENSE_CATEGORIES_API_URL}/${id}`;
    const response = await fetchWithAuth(url, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to update expense category ${id}: ${response.statusText}`);
    }
    return await response.json() as ExpenseCategoryDto;
}

// DELETE /api/v1/accounting/expense-categories/{id}
export async function deleteExpenseCategory(id: string): Promise<void> {
    const url = `${EXPENSE_CATEGORIES_API_URL}/${id}`;
    const response = await fetchWithAuth(url, {
        method: 'DELETE',
    });

    if (!response.ok) {
        // Handle 409 Conflict specifically if needed
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to delete expense category ${id}: ${response.statusText}`);
    }
    // No content expected on successful delete (204)
}
