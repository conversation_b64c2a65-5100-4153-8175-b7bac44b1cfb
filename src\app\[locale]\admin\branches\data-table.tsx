"use client";

import * as React from "react";
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { useTranslations } from 'next-intl';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/ui/data-table-pagination"; // Adjusted path
import { DataTableToolbar } from "./data-table-toolbar";
import { BranchDto } from "@/lib/dto/admin/branch.dto";
import { getColumns } from "./columns";

interface DataTableProps<TData, TValue> {
    data: TData[];
    isLoading?: boolean;
    onBranchAdded?: () => void;
    onBranchUpdated?: () => void;
    onBranchDeleted?: () => void;
}

export function DataTable<TData extends BranchDto, TValue>({
    data,
    isLoading = false,
    onBranchAdded,
    onBranchUpdated,
    onBranchDeleted,
}: DataTableProps<TData, TValue>) {
    const t = useTranslations('AdminBranchesPage.table');
    const tShared = useTranslations('Shared.dataTable');

    const [rowSelection, setRowSelection] = React.useState({});
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [sorting, setSorting] = React.useState<SortingState>([]);

    // Memoize columns to prevent re-creation on every render
    const columns = React.useMemo(
        () => getColumns({ t, onBranchUpdated, onBranchDeleted }),
        [t, onBranchUpdated, onBranchDeleted] // Dependencies for memoization
    );

    const table = useReactTable({
        data,
        columns,
        state: {
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
        },
        enableRowSelection: true,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
    });

    const colSpan = table.getAllColumns().length;

    return (
        <div className="space-y-4">
            <DataTableToolbar table={table} onBranchAdded={onBranchAdded} />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id} colSpan={header.colSpan}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan={colSpan} className="h-24 text-center">
                                    Loading... {/* TODO: Add Skeleton Loader */}
                                </TableCell>
                            </TableRow>
                        ) : table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && "selected"}
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={colSpan} className="h-24 text-center">
                                    {tShared('noResults')}
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
             {/* Pass translations scoped specifically for pagination */}
            <DataTablePagination table={table} t={useTranslations('Shared.dataTable.pagination')} />
        </div>
    );
}
