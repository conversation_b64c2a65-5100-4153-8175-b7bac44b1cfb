"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { AccountCombobox } from "@/components/ui/combobox-coa";
import { Button } from "@/components/ui/button";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Table } from "@tanstack/react-table";
import { X } from "lucide-react";
import { useState } from "react";
import { useTranslations } from "next-intl";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  onDateRangeChange: (range: DateRange | undefined) => void;
  onAccountSelect: (accountId: string | undefined) => void; // Changed to single account ID
  onIsPostedChange: (isPosted: boolean | null | undefined) => void;
}

export function GeneralLedgerDataTableToolbar<TData>({
  table,
  onDateRangeChange,
  onAccountSelect,
  onIsPostedChange,
}: DataTableToolbarProps<TData>) {
  const t = useTranslations("GeneralLedger");
  const isFiltered = table.getState().columnFilters.length > 0;

  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [selectedAccountId, setSelectedAccountId] = useState<string | undefined>(undefined); // Changed to single account ID state
  const [isPostedFilter, setIsPostedFilter] = useState<boolean | null | undefined>(undefined);

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    onDateRangeChange(range);
  };

  const handleAccountSelect = (accountId: string | undefined) => { // Changed to single account ID
    setSelectedAccountId(accountId); // Update single account ID state
    onAccountSelect(accountId);
  };

  const handleIsPostedChange = (value: string) => {
    let isPosted: boolean | null | undefined;
    if (value === "true") {
      isPosted = true;
    } else if (value === "false") {
      isPosted = false;
    } else {
      isPosted = undefined; // Represents "All"
    }
    setIsPostedFilter(isPosted);
    onIsPostedChange(isPosted);
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        {/* Date Range Picker */}
        <DateRangePicker date={dateRange} onDateChange={handleDateRangeChange} />

        {/* Account Filter (Single Select for now) */}
        <AccountCombobox
          value={selectedAccountId} // Pass single account ID state
          onChange={handleAccountSelect} // Use onChange prop
          placeholder={t("selectAccountPlaceholder")} // Use translation key
          searchPlaceholder={t("searchAccountPlaceholder")} // Use translation key
          noResultsText={t("noAccountFound")} // Use translation key
        />

        {/* Is Posted Filter */}
        <Select onValueChange={handleIsPostedChange} value={isPostedFilter === true ? "true" : isPostedFilter === false ? "false" : "all"}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t("filterByStatusPlaceholder")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("allStatuses")}</SelectItem>
            <SelectItem value="true">{t("posted")}</SelectItem>
            <SelectItem value="false">{t("unposted")}</SelectItem>
          </SelectContent>
        </Select>


        {/* Global Search (Optional) */}
        {/* <Input
          placeholder={t("filterPlaceholder")}
          value={(table.getColumn("accountName")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("accountName")?.setFilterValue(event.target.value)
          }
          className="h-8 w-[150px] lg:w-[250px]"
        /> */}

        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            {t("resetFilters")}
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  );
}
