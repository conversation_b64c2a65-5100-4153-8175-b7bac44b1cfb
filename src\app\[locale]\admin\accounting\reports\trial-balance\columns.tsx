"use client";

import { ColumnDef } from "@tanstack/react-table";
import { TrialBalanceAccountDto } from "@/lib/dto/admin/accounting/general-ledger.dto";
export const createColumns = (t: (key: string) => string): ColumnDef<TrialBalanceAccountDto>[] => {

  return [
    {
      accessorKey: "accountCode",
      header: t("accountCode"),
    },
    {
      accessorKey: "accountName",
      header: t("accountName"),
    },
    {
      accessorKey: "debitBalance",
      header: t("debitBalance"),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("debitBalance"));
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD", // TODO: Use dynamic currency from settings
        }).format(amount);

        return <div className="text-right font-medium">{formatted}</div>;
      },
    },
    {
      accessorKey: "creditBalance",
      header: t("creditBalance"),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("creditBalance"));
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD", // TODO: Use dynamic currency from settings
        }).format(amount);

        return <div className="text-right font-medium">{formatted}</div>;
      },
    },
  ];
};
