import { PageHeader, PageHeaderDescription, PageHeaderHeading } from "@/components/ui/page-header";

import { GeneralLedgerDataTable } from "./data-table";
import { useTranslations } from "next-intl";

export default function GeneralLedgerPage() {
  const t = useTranslations("GeneralLedger");

  return (
    <>
      <PageHeader>
        <PageHeaderHeading>{t("title")}</PageHeaderHeading>
        <PageHeaderDescription>{t("description")}</PageHeaderDescription>
      </PageHeader>
      <GeneralLedgerDataTable />
    </>
  );
}
