"use client";

import * as React from "react";
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar";
import { getAllExpenseCategories } from "@/lib/api/admin/accounting/expense-categories";
import { ExpenseCategoryDto } from "@/lib/dto/admin/accounting/expense-categories.dto";
import { Skeleton } from "@/components/ui/skeleton";
import { getColumns } from "./columns";

// Debounce hook (copied from chart-of-accounts)
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

    React.useEffect(() => {
        const timer = setTimeout(() => setDebouncedValue(value), delay);
        return () => clearTimeout(timer);
    }, [value, delay]);

    return debouncedValue;
}

interface DataTableProps<TData, TValue> {
    // No columns prop needed as it's generated internally
}

export function ExpenseCategoriesDataTable<TData extends ExpenseCategoryDto, TValue>({}: DataTableProps<TData, TValue>) {
    const tPage = useTranslations("AdminExpenseCategoriesPage");
    const tTable = useTranslations("AdminExpenseCategoriesPage.table");
    const tShared = useTranslations("Shared");

    // Generate columns using the imported function and translations
    const columns = React.useMemo(() => getColumns(tTable, tShared), [tTable, tShared]);

    const [rowSelection, setRowSelection] = React.useState({});
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [pagination, setPagination] = React.useState({
        pageIndex: 0,
        pageSize: 10,
    });
    const [globalFilter, setGlobalFilter] = React.useState(''); // For general search

    // Debounce search term
    const debouncedSearchTerm = useDebounce(globalFilter, 300);

    const queryKey = [
        "expense-categories",
        {
            page: pagination.pageIndex,
            size: pagination.pageSize,
            sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
            searchTerm: debouncedSearchTerm, // Use the correct API parameter 'searchTerm'
        }
    ];

    const { data, isLoading, isError, error } = useQuery({
        queryKey,
        queryFn: () => getAllExpenseCategories({
            page: pagination.pageIndex,
            size: pagination.pageSize,
            sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
            searchTerm: debouncedSearchTerm,
        }),
        placeholderData: (previousData) => previousData,
        // keepPreviousData: true, // Consider enabling for smoother UX
    });

    const table = useReactTable({
        data: data?.content ?? [],
        columns,
        state: {
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
            pagination,
            globalFilter,
        },
        enableRowSelection: true,
        manualPagination: true,
        manualSorting: true,
        manualFiltering: true, // Server-side filtering via searchTerm
        pageCount: data?.totalPages ?? -1,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onPaginationChange: setPagination,
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    });

    // Loading and Error States
    const renderTableContent = () => {
        if (isLoading) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        <div className="space-y-2">
                            {Array.from({ length: 5 }).map((_, i) => (
                                <Skeleton key={i} className="h-8 w-full" />
                            ))}
                        </div>
                    </TableCell>
                </TableRow>
            );
        }

        if (isError) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center text-destructive">
                        {tPage("errorLoading", { error: error?.message || 'Unknown error' })}
                    </TableCell>
                </TableRow>
            );
        }

        if (!table.getRowModel().rows?.length) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        {tPage("noResults")}
                    </TableCell>
                </TableRow>
            );
        }

        return table.getRowModel().rows.map((row) => (
            <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
            >
                {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                ))}
            </TableRow>
        ));
    };

    return (
        <div className="space-y-4">
            <DataTableToolbar
                table={table}
                globalFilter={globalFilter}
                setGlobalFilter={setGlobalFilter}
            />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead key={header.id} colSpan={header.colSpan}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {renderTableContent()}
                    </TableBody>
                </Table>
            </div>
            <DataTablePagination table={table} t={useTranslations('Shared.dataTable.pagination')} />
        </div>
    );
}
