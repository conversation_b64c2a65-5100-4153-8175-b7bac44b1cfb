"use client";

import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { TrialBalanceAccountDto, TrialBalanceDto } from "@/lib/dto/admin/accounting/general-ledger.dto";
import { useMemo, useState } from "react";

import { DataTable } from "@/components/ui/data-table";
import { DateRange } from "react-day-picker";
import { Skeleton } from "@/components/ui/skeleton";
import { TrialBalanceDataTableToolbar } from "./data-table-toolbar";
import { createColumns } from "./columns";
import { format } from "date-fns";
import { useGetTrialBalance } from "@/lib/api/admin/accounting/general-ledger";
import { useTranslations } from "next-intl";

export function TrialBalanceDataTable() {
  const t = useTranslations("TrialBalance");
  const columns: ColumnDef<TrialBalanceAccountDto>[] = useMemo(() => createColumns(t), [t]);

  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [generateReport, setGenerateReport] = useState(false);

  const { data, isLoading, isError } = useGetTrialBalance({
    startDate: dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : "",
    endDate: dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : "",
    enabled: generateReport && !!dateRange?.from && !!dateRange?.to,
  }) as { data: TrialBalanceDto | undefined, isLoading: boolean, isError: boolean };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    setGenerateReport(false); // Reset generate trigger on date change
  };

  const handleGenerateReport = () => {
    setGenerateReport(true);
  };

  const table = useReactTable<TrialBalanceAccountDto>({
    data: data?.accounts || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <TrialBalanceDataTableToolbar
          onDateRangeChange={handleDateRangeChange}
          onGenerateReport={handleGenerateReport}
          isLoading={isLoading}
        />
        <div className="rounded-md border p-4">
          <Skeleton className="h-8 w-full mb-4" />
          {[...Array(5)].map((_, index) => ( // Render a few skeleton rows
            <Skeleton key={index} className="h-6 w-full mb-2" />
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return <div>{t("errorFetchingTrialBalance")}</div>;
  }

  return (
    <div className="space-y-4">
      <TrialBalanceDataTableToolbar
        onDateRangeChange={handleDateRangeChange}
        onGenerateReport={handleGenerateReport}
        isLoading={isLoading}
      />
      {data?.accounts && data.accounts.length > 0 ? (
        <>
          <div className="rounded-md border">
            <DataTable<TrialBalanceAccountDto> table={table} columns={columns} />
          </div>
          <div className="flex justify-end space-x-4 text-lg font-bold">
            <div>{t("totalDebits")}: {new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(data.totalDebits)}</div> {/* TODO: Dynamic currency */}
            <div>{t("totalCredits")}: {new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(data.totalCredits)}</div> {/* TODO: Dynamic currency */}
          </div>
        </>
      ) : (
        <div className="text-center text-muted-foreground">
          {generateReport ? t("noDataFound") : t("selectDatesAndGenerate")}
        </div>
      )}
    </div>
  );
}
