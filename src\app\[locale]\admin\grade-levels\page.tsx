"use client";

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal } from 'lucide-react';

import { DataTable } from './data-table';
import { AddGradeLevelDialog } from './add-dialog';
import { getAllGradeLevelsList } from '@/lib/api/admin/grade-levels';
import { getAllEducationalStagesList } from '@/lib/api/admin/educational-stages';
import { GradeLevelDto } from '@/lib/dto/admin/grade-level.dto';
import { EducationalStageDto } from '@/lib/dto/admin/educational-stage.dto';

export default function GradeLevelsPage() {
  const t = useTranslations('AdminGradeLevelsPage');
  const tShared = useTranslations('Shared');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [selectedStageId, setSelectedStageId] = useState<string | undefined>();

  // Fetch grade levels
  const {
    data: gradeLevels,
    isLoading: isLoadingGradeLevels,
    error: gradeLevelsError,
    refetch: refetchGradeLevels
  } = useQuery<GradeLevelDto[], Error>({
    queryKey: ['grade-levels'],
    queryFn: getAllGradeLevelsList,
  });

  // Fetch educational stages for filtering
  const {
    data: educationalStages,
    isLoading: isLoadingStages
  } = useQuery<EducationalStageDto[], Error>({
    queryKey: ['educational-stages'],
    queryFn: getAllEducationalStagesList,
  });

  // Filter grade levels by selected stage
  const filteredGradeLevels = React.useMemo(() => {
    if (!gradeLevels) return [];
    if (!selectedStageId) return gradeLevels;
    return gradeLevels.filter(gradeLevel => gradeLevel.educationalStageId === selectedStageId);
  }, [gradeLevels, selectedStageId]);

  // Create a map of stage ID to stage name for display
  const stageMap = React.useMemo(() => {
    if (!educationalStages) return new Map();
    return new Map(educationalStages.map(stage => [stage.id, stage]));
  }, [educationalStages]);

  // Enhanced grade levels with stage information
  const enhancedGradeLevels = React.useMemo(() => {
    return filteredGradeLevels.map(gradeLevel => ({
      ...gradeLevel,
      educationalStage: stageMap.get(gradeLevel.educationalStageId)
    }));
  }, [filteredGradeLevels, stageMap]);

  const handleAddSuccess = () => {
    refetchGradeLevels();
    setIsAddDialogOpen(false);
  };

  if (gradeLevelsError) {
    return (
      <Alert>
        <Terminal className="h-4 w-4" />
        <AlertTitle>{tShared('Messages.error')}</AlertTitle>
        <AlertDescription>
          {gradeLevelsError.message || tShared('Messages.unexpectedError')}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t('title')}</CardTitle>
              <CardDescription>{t('description')}</CardDescription>
            </div>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <PlusCircle className="mr-2 h-4 w-4" />
              {t('addNewButton')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoadingGradeLevels ? (
            <div className="space-y-4" data-testid="loading-skeleton">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          ) : (
            <DataTable
              data={enhancedGradeLevels}
              educationalStages={educationalStages || []}
              selectedStageId={selectedStageId}
              onStageFilterChange={setSelectedStageId}
              onRefetch={refetchGradeLevels}
            />
          )}
        </CardContent>
      </Card>

      <AddGradeLevelDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSuccess={handleAddSuccess}
        educationalStages={educationalStages || []}
      />
    </div>
  );
}
