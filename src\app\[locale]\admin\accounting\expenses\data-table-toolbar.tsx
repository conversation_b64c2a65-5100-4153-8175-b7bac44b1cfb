"use client";

import { Table } from "@tanstack/react-table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options";
import { AddEditExpenseSheet } from "./add-edit-expense-sheet"; // Import the sheet
import { useTranslations } from "next-intl";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker"; // Import DateRangePicker
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { getAllExpenseCategoriesList } from "@/lib/api/admin/accounting/expense-categories"; // API to fetch categories
import { SimpleExpenseCategoryDto } from "@/lib/dto/admin/accounting/expense-categories.dto";
import { useState } from "react"; // Import useState

interface DataTableToolbarProps<TData> {
    table: Table<TData>;
    dateRange: DateRange | undefined;
    setDateRange: (date: DateRange | undefined) => void;
    categoryFilter: string;
    setCategoryFilter: (value: string) => void;
    onExpenseAdded?: () => void; // Callback for when an expense is added/edited
}

// Helper function to fetch all expense categories
async function fetchAllExpenseCategories(): Promise<SimpleExpenseCategoryDto[]> {
    try {
        return await getAllExpenseCategoriesList();
    } catch (error) {
        console.error("Failed to fetch expense categories for filter:", error);
        return [];
    }
}

export function DataTableToolbar<TData>({
    table,
    dateRange,
    setDateRange,
    categoryFilter,
    setCategoryFilter,
    onExpenseAdded,
}: DataTableToolbarProps<TData>) {
    const t = useTranslations("AdminExpensesPage");
    const tShared = useTranslations("Shared");
    const tSharedDate = useTranslations("Shared.dateRangePicker");
    const [isAddSheetOpen, setIsAddSheetOpen] = useState(false); // State for Add sheet

    const isFiltered = !!dateRange || !!categoryFilter;

    // Fetch expense categories for the filter dropdown
    const { data: expenseCategories, isLoading: isLoadingCategories } = useQuery({
        queryKey: ["expenseCategoriesList"], // Use a distinct key
        queryFn: fetchAllExpenseCategories,
        staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    });

    const handleResetFilters = () => {
        setDateRange(undefined);
        setCategoryFilter("");
        // Optionally reset other table filters if needed
        // table.resetColumnFilters();
    };

    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
                {/* Date Range Filter */}
                <DateRangePicker
                    date={dateRange}
                    onDateChange={setDateRange}
                    placeholder={tSharedDate("placeholder")}
                    className="h-8 w-full sm:w-auto"
                />

                {/* Expense Category Filter Select */}
                <Select
                    value={categoryFilter}
                    onValueChange={(value) => setCategoryFilter(value === "all" ? "" : value)}
                    disabled={isLoadingCategories}
                >
                    <SelectTrigger className="h-8 w-full sm:w-[180px] lg:w-[220px]">
                        <SelectValue placeholder={t("filterByCategoryPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">{t("allCategories")}</SelectItem>
                        {expenseCategories?.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                                {/* Display name based on locale preference if available */}
                                {category.nameEn} / {category.nameAr}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                {/* Reset Button */}
                {isFiltered && (
                    <Button
                        variant="ghost"
                        onClick={handleResetFilters}
                        className="h-8 px-2 lg:px-3"
                    >
                        {t("resetFilters")}
                        <X className="ml-2 h-4 w-4" />
                    </Button>
                )}
            </div>
            <div className="flex items-center space-x-2">
                <DataTableViewOptions table={table} t={tShared.raw("dataTable")} />
                {/* Use AddEditExpenseSheet for adding, controlled by state */}
                <AddEditExpenseSheet
                    isOpen={isAddSheetOpen}
                    onOpenChange={setIsAddSheetOpen} // Pass the state setter
                    onSuccess={() => {
                        onExpenseAdded?.(); // Call refetch/callback
                        setIsAddSheetOpen(false); // Ensure sheet closes on success
                    }}
                >
                    {/* The Button now acts as the trigger via the Sheet's children mechanism */}
                    <Button>{t("addExpenseButton")}</Button>
                </AddEditExpenseSheet>
            </div>
        </div>
    );
}
