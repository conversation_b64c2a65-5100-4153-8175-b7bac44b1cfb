"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { format } from "date-fns";
import { useState } from "react";
import { useTranslations } from "next-intl";

interface DataTableToolbarProps {
  onDateRangeChange: (range: DateRange | undefined) => void;
  onGenerateReport: () => void;
  isLoading: boolean;
  dateRange: DateRange | undefined;
}

export function DataTableToolbar({
  onDateRangeChange,
  onGenerateReport,
  isLoading,
  dateRange,
}: DataTableToolbarProps) {
  const t = useTranslations("IncomeStatementPage");

  return (
    <div className="flex flex-col space-y-4 md:flex-row md:items-end md:space-x-4 md:space-y-0">
      <div className="flex-1">
        <DateRangePicker
          date={dateRange}
          onDateChange={onDateRangeChange}
          placeholder={t("selectDateRange")}
        />
      </div>
      <Button
        onClick={onGenerateReport}
        disabled={isLoading || !dateRange?.from || !dateRange?.to}
      >
        {isLoading ? t("generating") : t("generateReport")}
      </Button>
    </div>
  );
}
