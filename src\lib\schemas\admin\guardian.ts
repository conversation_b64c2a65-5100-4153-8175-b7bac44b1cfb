import { z } from "zod";
import { IdType } from "@/lib/dto/admin/guardian.dto";

// Enum definition matching the API and DTO
export const IdTypeEnum = z.enum(["NATIONAL_ID", "PASSPORT"]);

// Schema for creating a guardian profile with an existing user (legacy)
export const createGuardianProfileSchema = z.object({
    userId: z.string().uuid("Invalid user ID"),
    occupation: z.string().min(1, "Occupation is required").max(100, "Occupation cannot exceed 100 characters"),
    address: z.string().max(255, "Address cannot exceed 255 characters").optional(),
    nationalId: z.string().min(1, "National ID is required").max(50, "National ID cannot exceed 50 characters"),
    idType: IdTypeEnum.refine(val => val !== undefined, { message: "ID type is required" }),
});

export type CreateGuardianProfileInput = z.infer<typeof createGuardianProfileSchema>;

// Schema for creating a guardian with a new user account
export const createGuardianWithUserSchema = z.object({
    firstName: z.string().min(2, "First name must be at least 2 characters").max(50, "First name cannot exceed 50 characters"),
    lastName: z.string().min(2, "Last name must be at least 2 characters").max(50, "Last name cannot exceed 50 characters"),
    email: z.string().email("Invalid email address").max(100, "Email cannot exceed 100 characters"),
    password: z.string().min(8, "Password must be at least 8 characters").max(100, "Password cannot exceed 100 characters"),
    phoneNumber: z.string().max(20, "Phone number cannot exceed 20 characters").optional(),
    occupation: z.string().min(1, "Occupation is required").max(100, "Occupation cannot exceed 100 characters"),
    address: z.string().max(255, "Address cannot exceed 255 characters").optional(),
    nationalId: z.string().min(1, "National ID is required").max(50, "National ID cannot exceed 50 characters"),
    idType: IdTypeEnum.refine(val => val !== undefined, { message: "ID type is required" }),
});

export type CreateGuardianWithUserInput = z.infer<typeof createGuardianWithUserSchema>;

// Schema for updating a guardian profile with generic error messages
export const updateGuardianProfileSchema = z.object({
    occupation: z.string().min(1, "Occupation is required").max(100, "Occupation cannot exceed 100 characters"),
    address: z.string().max(255, "Address cannot exceed 255 characters").optional(),
    nationalId: z.string().min(1, "National ID is required").max(50, "National ID cannot exceed 50 characters"),
    idType: IdTypeEnum.refine(val => val !== undefined, { message: "ID type is required" }),
});

export type UpdateGuardianProfileInput = z.infer<typeof updateGuardianProfileSchema>;
