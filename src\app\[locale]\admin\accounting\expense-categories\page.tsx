import { ExpenseCategoriesDataTable } from "./data-table";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default async function ExpenseCategoriesPage() {
    const t = await getTranslations("AdminExpenseCategoriesPage");

    return (
        <Card>
            <CardHeader>
                <CardTitle>{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent>
                <ExpenseCategoriesDataTable />
            </CardContent>
        </Card>
    );
}
