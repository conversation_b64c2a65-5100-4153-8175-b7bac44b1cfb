import { PageableObject, SortObject } from "../../../dto/common.dto";

export interface GeneralLedgerEntryDto {
  date: string;
  journalEntryId: string;
  accountCode: string;
  accountName: string;
  description: string;
  debit: number;
  credit: number;
  runningBalance: number;
}

export interface PageGeneralLedgerEntryDto {
  totalElements: number;
  totalPages: number;
  size: number;
  content: GeneralLedgerEntryDto[];
  number: number;
  sort: SortObject[];
  numberOfElements: number;
  first: boolean;
  last: boolean;
  pageable: PageableObject;
  empty: boolean;
}

export interface TrialBalanceAccountDto {
  accountCode: string;
  accountName: string;
  debitBalance: number;
  creditBalance: number;
}

export interface TrialBalanceDto {
  startDate: string;
  endDate: string;
  accounts: TrialBalanceAccountDto[];
  totalDebits: number;
  totalCredits: number;
}
