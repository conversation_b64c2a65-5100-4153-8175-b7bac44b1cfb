"use client";

import React, { createContext, useContext } from "react";

import { LoadingIndicator } from "@/components/common/loading-indicator";
import { useNavigationEvents } from "@/hooks/use-navigation-events";

// Create context for route change state
interface RouteChangeContextType {
  isRouteChanging: boolean;
}

const RouteChangeContext = createContext<RouteChangeContextType>({
  isRouteChanging: false,
});

export const useRouteChange = () => useContext(RouteChangeContext);

interface RouteChangeProviderProps {
  children: React.ReactNode;
}

export const RouteChangeProvider: React.FC<RouteChangeProviderProps> = ({
  children,
}) => {
  // Use our custom hook to track navigation events
  const { isNavigating } = useNavigationEvents();

  return (
    <RouteChangeContext.Provider value={{ isRouteChanging: isNavigating }}>
      <LoadingIndicator isLoading={isNavigating} type="spinner" />
      {children}
    </RouteChangeContext.Provider>
  );
};
