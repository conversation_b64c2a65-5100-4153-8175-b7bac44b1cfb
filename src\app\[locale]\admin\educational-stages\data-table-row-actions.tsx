"use client";

import { Row } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { useTranslations } from 'next-intl';

import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { EducationalStageDto } from "@/lib/dto/admin/educational-stage.dto";
import { EditStageDialog } from "./edit-dialog"; // Import Edit Dialog
import { DeleteStageDialog } from "./delete-dialog"; // Import Delete Dialog
import { useState } from "react";

interface DataTableRowActionsProps<TData extends EducationalStageDto> {
    row: Row<TData>;
    onStageUpdated: () => void; // Callback after successful update
    onStageDeleted: () => void; // Callback after successful delete
}

export function DataTableRowActions<TData extends EducationalStageDto>({
    row,
    onStageUpdated,
    onStageDeleted,
}: DataTableRowActionsProps<TData>) {
    const t = useTranslations('AdminEducationalStagesPage.table');
    const stage = row.original; // Get the full stage data
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                    >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{t('actionsOpenMenu')}</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                        {t('actionsEdit')}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        className="text-destructive focus:text-destructive focus:bg-destructive/10"
                        onClick={() => setIsDeleteDialogOpen(true)}
                    >
                        {t('actionsDelete')}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Edit Dialog */}
            <EditStageDialog
                stage={stage}
                isOpen={isEditDialogOpen}
                onOpenChange={setIsEditDialogOpen}
                onStageUpdated={() => {
                    setIsEditDialogOpen(false); // Close dialog on success
                    onStageUpdated(); // Trigger refetch
                }}
            />

            {/* Delete Dialog */}
            <DeleteStageDialog
                stage={stage}
                isOpen={isDeleteDialogOpen}
                onOpenChange={setIsDeleteDialogOpen}
                onStageDeleted={() => {
                    setIsDeleteDialogOpen(false); // Close dialog on success
                    onStageDeleted(); // Trigger refetch
                }}
            />
        </>
    );
}
