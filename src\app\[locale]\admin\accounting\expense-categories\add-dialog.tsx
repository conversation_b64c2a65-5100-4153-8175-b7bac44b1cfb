"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PlusCircle } from "lucide-react";
import { createExpenseCategory, getAllExpenseCategoriesList } from "@/lib/api/admin/accounting/expense-categories";
import { CreateExpenseCategoryInput, createExpenseCategorySchema } from "@/lib/schemas/admin/accounting/expense-categories";
import { CreateExpenseCategoryRequest } from "@/lib/dto/admin/accounting/expense-categories.dto"; // Import request type
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton"; // For loading state

export function AddExpenseCategoryDialog() {
    const [isOpen, setIsOpen] = useState(false);
    const [hasMounted, setHasMounted] = useState(false);

    useEffect(() => {
        setHasMounted(true);
    }, []);

    const t = useTranslations("AdminExpenseCategoriesPage.AddDialog");
    const tValidation = useTranslations("AdminExpenseCategoriesPage.AddDialog.validation");
    const queryClient = useQueryClient();

    const formSchema = createExpenseCategorySchema(tValidation);
    const form = useForm<CreateExpenseCategoryInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            nameEn: "",
            nameAr: "",
            descriptionEn: "",
            descriptionAr: "",
            parentCategoryId: undefined,
        },
    });

    // Fetch potential parent categories (full list)
    const { data: parentCategoriesData, isLoading: isLoadingParents } = useQuery({
        queryKey: ["expense-categories-list"], // Use a distinct key for the list
        queryFn: getAllExpenseCategoriesList,
        enabled: isOpen, // Only fetch when the dialog is open
        staleTime: 300000, // Cache for 5 minutes
    });

    const mutation = useMutation({
        mutationFn: createExpenseCategory,
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.nameEn }));
            queryClient.invalidateQueries({ queryKey: ["expense-categories"] }); // Invalidate main table data
            queryClient.invalidateQueries({ queryKey: ["expense-categories-list"] }); // Invalidate list
            setIsOpen(false);
            form.reset();
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message }));
        },
    });

    const onSubmit = (values: CreateExpenseCategoryInput) => {
        // Ensure empty string/null parentCategoryId becomes null for API
        // Ensure empty descriptions become null for API
        const submissionValues: CreateExpenseCategoryRequest = {
            ...values,
            parentCategoryId: values.parentCategoryId || null,
            descriptionEn: values.descriptionEn || null,
            descriptionAr: values.descriptionAr || null,
        };
        mutation.mutate(submissionValues);
    };

    // Prevent rendering the dialog until mounted
    if (!hasMounted) {
        return (
            <Button onClick={() => setIsOpen(true)}>
                <PlusCircle className="mr-2 h-4 w-4" />
                {t("triggerButton")}
            </Button>
        );
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t("triggerButton")}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description")}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <ScrollArea className="h-[50vh] pr-6">
                            <div className="space-y-4 p-1">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <FormField
                                        control={form.control}
                                        name="nameEn"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("nameEnLabel")}</FormLabel>
                                                <FormControl>
                                                    <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="nameAr"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("nameArLabel")}</FormLabel>
                                                <FormControl>
                                                    <Input dir="rtl" placeholder={t("nameArPlaceholder")} {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <FormField
                                    control={form.control}
                                    name="parentCategoryId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("parentCategoryLabel")}</FormLabel>
                                            <Select
                                                onValueChange={(value) => field.onChange(value === "__NONE__" ? undefined : value)}
                                                value={field.value ?? "__NONE__"} // Use value, fallback to "__NONE__"
                                                disabled={isLoadingParents}
                                            >
                                                <FormControl>
                                                    <SelectTrigger>
                                                        {isLoadingParents ? (
                                                            <Skeleton className="h-6 w-3/4" />
                                                        ) : (
                                                            <SelectValue placeholder={t("parentCategoryPlaceholder")} />
                                                        )}
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    <SelectItem value="__NONE__">{t("noParentValue")}</SelectItem>
                                                    {parentCategoriesData?.map((category) => (
                                                        <SelectItem key={category.id} value={category.id}>
                                                            {category.nameEn} / {category.nameAr}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormDescription>{t("parentCategoryDescription")}</FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="descriptionEn"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                            <FormControl>
                                                <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} value={field.value ?? ''} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="descriptionAr"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                            <FormControl>
                                                <Textarea dir="rtl" placeholder={t("descriptionArPlaceholder")} {...field} value={field.value ?? ''} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </ScrollArea>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={mutation.isPending}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending || isLoadingParents}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
