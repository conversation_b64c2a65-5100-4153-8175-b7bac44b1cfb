import { type PermissionDto } from "./permission.dto";

export interface RoleDto {
  id: string;
  name: string;
  description?: string;
  permissions: PermissionDto[]; // API returns full permission objects
  createdAt: string;
  updatedAt: string;
}

export interface CreateRoleRequest {
  name: string;
  description?: string;
  permissionNames: string[]; // API expects array of names for creation
}

export interface UpdateRoleRequest {
    name?: string; // Optional update fields
    description?: string;
    permissionNames?: string[];
}

// Used when assigning permissions specifically
export type AssignPermissionsRequest = string[]; // Array of permission names
