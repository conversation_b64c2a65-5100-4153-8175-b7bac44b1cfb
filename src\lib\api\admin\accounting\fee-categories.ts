import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { API_BASE_URL } from "@/lib/constants";
import {
    FeeCategoryDto,
    CreateFeeCategoryRequest,
    UpdateFeeCategoryRequest,
    PageFeeCategoryDto,
    GetFeeCategoriesParams
} from "@/lib/dto/admin/accounting/fee-categories.dto";

/**
 * Fetches a paginated list of fee categories.
 * Corresponds to GET /api/v1/accounting/fee-categories
 */
export async function getFeeCategories(params: GetFeeCategoriesParams): Promise<PageFeeCategoryDto> {
    const queryParams = new URLSearchParams();
    if (params.page !== undefined) queryParams.append("page", params.page.toString());
    if (params.size !== undefined) queryParams.append("size", params.size.toString());
    if (params.sort) params.sort.forEach(s => queryParams.append("sort", s));
    if (params.search) queryParams.append("search", params.search); // Assuming backend supports 'search'

    const url = `${API_BASE_URL}/accounting/fee-categories?${queryParams.toString()}`;
    console.log("Fetching fee categories from:", url); // Log the URL

    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Failed to fetch fee categories:", response.status, response.statusText, errorData);
            throw new Error(errorData?.message || `Failed to fetch fee categories: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as PageFeeCategoryDto;
    } catch (error) {
        console.error("Error in getFeeCategories:", error);
        throw error; // Re-throw the error after logging
    }
}

/**
 * Fetches a single fee category by its ID.
 * Corresponds to GET /api/v1/accounting/fee-categories/{id}
 */
export async function getFeeCategoryById(id: string): Promise<FeeCategoryDto> {
    const url = `${API_BASE_URL}/accounting/fee-categories/${id}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch fee category ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as FeeCategoryDto;
    } catch (error) {
        console.error(`Error fetching fee category ${id}:`, error);
        throw error;
    }
}

/**
 * Fetches all fee categories as a list.
 * Corresponds to GET /api/v1/accounting/fee-categories/all
 */
// Update return type to SimpleFeeCategoryDto[] based on API doc
export async function getAllFeeCategoriesList(): Promise<SimpleFeeCategoryDto[]> {
    const url = `${API_BASE_URL}/accounting/fee-categories/all`;
    console.log(`Attempting to fetch all fee categories from: ${url}`); // Log URL
    try {
        // Log headers being sent by fetchWithAuth (if possible, might need modification to fetchWithAuth)
        // For now, just log the attempt
        const response = await fetchWithAuth(url);
        console.log(`Response status for ${url}: ${response.status}`); // Log status

        if (!response.ok) {
            let errorBodyText = await response.text(); // Get raw error body
            console.error(`API Error Body for ${url}: ${errorBodyText}`); // Log raw body
            const errorData = JSON.parse(errorBodyText || '{}'); // Attempt to parse, default to empty object
            // Include status code in the fallback message
            const fallbackMessage = `Failed to fetch all fee categories: ${response.status} ${response.statusText || '(No status text)'}`;
            throw new Error(errorData?.message || fallbackMessage);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        // Return the correct type based on API doc
        return data as SimpleFeeCategoryDto[];
    } catch (error) {
        console.error("Error fetching all fee categories:", error);
        throw error;
    }
}


/**
 * Creates a new fee category.
 * Corresponds to POST /api/v1/accounting/fee-categories
 */
export async function createFeeCategory(request: CreateFeeCategoryRequest): Promise<FeeCategoryDto> {
    const url = `${API_BASE_URL}/accounting/fee-categories`;
    try {
        const response = await fetchWithAuth(url, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(request),
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to create fee category: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as FeeCategoryDto;
    } catch (error) {
        console.error("Error creating fee category:", error);
        throw error;
    }
}

/**
 * Updates an existing fee category.
 * Corresponds to PUT /api/v1/accounting/fee-categories/{id}
 */
export async function updateFeeCategory(id: string, request: UpdateFeeCategoryRequest): Promise<FeeCategoryDto> {
    const url = `${API_BASE_URL}/accounting/fee-categories/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(request),
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to update fee category ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as FeeCategoryDto;
    } catch (error) {
        console.error(`Error updating fee category ${id}:`, error);
        throw error;
    }
}

/**
 * Deletes a fee category by its ID.
 * Corresponds to DELETE /api/v1/accounting/fee-categories/{id}
 */
export async function deleteFeeCategory(id: string): Promise<void> {
    const url = `${API_BASE_URL}/accounting/fee-categories/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: "DELETE",
        });
        if (!response.ok) {
            // Handle cases where DELETE might return 204 No Content on success but still fail
            if (response.status === 204) {
                return; // Treat 204 as success for DELETE
            }
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to delete fee category ${id}: ${response.statusText}`);
        }
        // Check if response has content before trying to parse JSON (for 200 OK with body)
        if (response.headers.get("content-length") && parseInt(response.headers.get("content-length")!, 10) > 0) {
             await response.json(); // Consume body if present, but void return
        }
    } catch (error) {
        console.error(`Error deleting fee category ${id}:`, error);
        throw error;
    }
}
