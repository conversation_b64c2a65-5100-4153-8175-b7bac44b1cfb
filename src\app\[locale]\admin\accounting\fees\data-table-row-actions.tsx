"use client";

import { Row } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Trash2, <PERSON>cil, ToggleLeft, ToggleRight } from "lucide-react";
import { useTranslations } from "next-intl";
import { FeeDto } from "@/lib/dto/admin/accounting/fees.dto";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteFee, activateFee, deactivateFee } from "@/lib/api/admin/accounting/fees";
import { toast } from "sonner";
import { getErrorMessage } from "@/lib/utils";
import { useState } from "react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"; // Import AlertDialog components
import { EditFeeDialog } from "./edit-fee-dialog"; // Import the Edit dialog

interface DataTableRowActionsProps<TData extends FeeDto> {
    row: Row<TData>;
}

export function DataTableRowActions<TData extends FeeDto>({
    row,
}: DataTableRowActionsProps<TData>) {
    const t = useTranslations("AdminFeesPage.table");
    const tShared = useTranslations("Shared");
    const queryClient = useQueryClient();
    const fee = row.original;

    const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState(false);
    const [isConfirmActivateDialogOpen, setIsConfirmActivateDialogOpen] = useState(false);
    const [isConfirmDeactivateDialogOpen, setIsConfirmDeactivateDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

    // --- Mutations ---
    const mutationOptions = {
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["fees"] }); // Invalidate cache after action
        },
        onError: (error: unknown) => {
            toast.error(getErrorMessage(error));
        },
    };

    const deleteMutation = useMutation({
        mutationFn: () => deleteFee(fee.id),
        ...mutationOptions,
        onSuccess: () => {
            toast.success(t("deleteSuccessToast", { name: fee.nameEn }));
            mutationOptions.onSuccess(); // Call shared success logic
            setIsConfirmDeleteDialogOpen(false);
        },
        onError: (error) => {
             toast.error(t('deleteErrorToast', { error: getErrorMessage(error) }));
             setIsConfirmDeleteDialogOpen(false);
        }
    });

    const activateMutation = useMutation({
        mutationFn: () => activateFee(fee.id),
        ...mutationOptions,
        onSuccess: () => {
            toast.success(t("activateSuccessToast", { name: fee.nameEn }));
            mutationOptions.onSuccess();
            setIsConfirmActivateDialogOpen(false);
        },
         onError: (error) => {
             toast.error(t('activateErrorToast', { error: getErrorMessage(error) }));
             setIsConfirmActivateDialogOpen(false);
        }
    });

    const deactivateMutation = useMutation({
        mutationFn: () => deactivateFee(fee.id),
        ...mutationOptions,
        onSuccess: () => {
            toast.success(t("deactivateSuccessToast", { name: fee.nameEn }));
            mutationOptions.onSuccess();
            setIsConfirmDeactivateDialogOpen(false);
        },
         onError: (error) => {
             toast.error(t('deactivateErrorToast', { error: getErrorMessage(error) }));
             setIsConfirmDeactivateDialogOpen(false);
        }
    });

    const isPending = deleteMutation.isPending || activateMutation.isPending || deactivateMutation.isPending;

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                    >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{t("actionsOpenMenu")}</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                        <Pencil className="mr-2 h-4 w-4" />
                        {t("actionsEdit")}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {fee.active ? (
                        <DropdownMenuItem onClick={() => setIsConfirmDeactivateDialogOpen(true)}>
                            <ToggleLeft className="mr-2 h-4 w-4" />
                            {t("actionsDeactivate")}
                        </DropdownMenuItem>
                    ) : (
                        <DropdownMenuItem onClick={() => setIsConfirmActivateDialogOpen(true)}>
                            <ToggleRight className="mr-2 h-4 w-4" />
                            {t("actionsActivate")}
                        </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                        className="text-destructive focus:text-destructive focus:bg-destructive/10"
                        onClick={() => setIsConfirmDeleteDialogOpen(true)}
                    >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t("actionsDelete")}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Edit Dialog */}
            <EditFeeDialog
                isOpen={isEditDialogOpen}
                setIsOpen={setIsEditDialogOpen}
                fee={fee}
            />

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={isConfirmDeleteDialogOpen} onOpenChange={setIsConfirmDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{tShared("confirmationDialog.title")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {tShared("confirmationDialog.deleteMessage", { item: t("feeItem") })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>{tShared("confirmationDialog.cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => deleteMutation.mutate()}
                            disabled={deleteMutation.isPending}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {deleteMutation.isPending ? tShared("confirmationDialog.deleting") : tShared("confirmationDialog.delete")}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

             {/* Activate Confirmation Dialog */}
             <AlertDialog open={isConfirmActivateDialogOpen} onOpenChange={setIsConfirmActivateDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{t("activateConfirmTitle")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {t("activateConfirmMessage", { name: fee.nameEn })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>{tShared("confirmationDialog.cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => activateMutation.mutate()}
                            disabled={activateMutation.isPending}
                        >
                            {activateMutation.isPending ? tShared("confirmationDialog.activating") : t("actionsActivate")} {/* Add activating state if needed */}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

             {/* Deactivate Confirmation Dialog */}
             <AlertDialog open={isConfirmDeactivateDialogOpen} onOpenChange={setIsConfirmDeactivateDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{t("deactivateConfirmTitle")}</AlertDialogTitle>
                        <AlertDialogDescription>
                             {t("deactivateConfirmMessage", { name: fee.nameEn })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>{tShared("confirmationDialog.cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => deactivateMutation.mutate()}
                            disabled={deactivateMutation.isPending}
                        >
                             {deactivateMutation.isPending ? tShared("confirmationDialog.deactivating") : t("actionsDeactivate")} {/* Add deactivating state if needed */}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
