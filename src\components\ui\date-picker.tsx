"use client"

import * as React from "react"
import { format } from "date-fns"
import {  CalendarIcon } from "lucide-react"
// import { Calendar as CalendarIcon } from "lucide-react"
import { SelectSingleEventHandler } from "react-day-picker" // Import the type

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
// Removed unused PopoverClose import

interface DatePickerProps {
    date: Date | undefined;
    setDate: (date: Date | undefined) => void; // Use SelectSingleEventHandler or a compatible signature
    placeholder?: string;
    disabled?: (date: Date) => boolean; // Optional prop to disable specific dates
    fromDate?: Date; // Optional prop to disable dates before this date
    toDate?: Date; // Optional prop to disable dates after this date
    className?: string; // Allow passing custom class names
}

export function DatePicker({ date, setDate, placeholder, disabled, fromDate, toDate, className }: DatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false); // Manage open state internally

  return (
    // Add modal={false} to prevent focus trapping issues when nested in Dialogs
    <Popover open={isOpen} onOpenChange={setIsOpen} modal={isOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal", // Changed w-[280px] to w-full
            !date && "text-muted-foreground",
            className // Apply custom className
          )}
          onClick={() => setIsOpen(true)} // Explicitly open the popover on button click
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "PPP") : <span>{placeholder || "Pick a date"}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={date}
          // Explicitly handle the onSelect event to pass only the date and close popover
          onSelect={(selectedDate, _selectedDay, _activeModifiers, _e) => {
            setDate(selectedDate);
            setIsOpen(false); // Close the popover after selection
          }}
          initialFocus
          disabled={disabled || ((d) => { // Combine external disabled logic with from/to dates
            if (fromDate && d < fromDate) return true;
            if (toDate && d > toDate) return true;
            return false;
          })}
        />
      </PopoverContent>
    </Popover>
  )
}
