"use client";

import { Table } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options"; // Reusable view options
import { Input } from "@/components/ui/input";
import { useLocale, useTranslations } from "next-intl";
import { useDebounce } from "@/hooks/use-debounce"; // Import the useDebounce hook
import * as React from "react"; // Import React for useEffect

// Remove t and tShared from props
interface DataTableToolbarProps<TData> {
    table: Table<TData>;
    // t: ReturnType<typeof useTranslations<'AdminTaxesPage'>>; // Removed
    // tShared: ReturnType<typeof useTranslations<'Shared'>>; // Removed
    onAddTax: () => void; // Callback to open the add dialog
}

export function DataTableToolbar<TData>({
    table,
    // t, // Removed
    // tShared, // Removed
    onAddTax,
}: DataTableToolbarProps<TData>) {
    // Get translations internally
    const t = useTranslations("AdminTaxesPage");
    const tShared = useTranslations("Shared");
    const tSharedDataTable = useTranslations("Shared.dataTable"); // Specific for dataTable actions/labels

    const locale = useLocale();
    const nameColumnId = locale === 'ar' ? 'nameAr' : 'nameEn'; // Determine column based on locale

    // State for the search input value
    const [searchValue, setSearchValue] = React.useState<string>(
        (table.getColumn(nameColumnId)?.getFilterValue() as string) ?? ""
    );
    // Debounce the search value
    const debouncedSearchValue = useDebounce(searchValue, 500); // 500ms delay

    // Effect to apply the debounced filter value to the table
    React.useEffect(() => {
        table.getColumn(nameColumnId)?.setFilterValue(debouncedSearchValue);
    }, [debouncedSearchValue, table, nameColumnId]);

    const isFiltered = table.getState().columnFilters.length > 0;

    return (
        <div className="flex items-center justify-end">
            {/*<div className="flex flex-1 items-center space-x-2 rtl:space-x-reverse">*/}
            {/*    <Input*/}
            {/*        placeholder={t("searchPlaceholder")}*/}
            {/*        value={searchValue}*/}
            {/*        onChange={(event) => setSearchValue(event.target.value)}*/}
            {/*        className="h-8 w-[150px] lg:w-[250px]"*/}
            {/*    />*/}
            {/*    /!* Add other filters here if needed *!/*/}
            {/*    {isFiltered && (*/}
            {/*        <Button*/}
            {/*            variant="ghost"*/}
            {/*            onClick={() => {*/}
            {/*                table.resetColumnFilters();*/}
            {/*                setSearchValue(""); // Reset input field as well*/}
            {/*            }}*/}
            {/*            className="h-8 px-2 lg:px-3"*/}
            {/*        >*/}
            {/*            {t("resetFilters")}*/}
            {/*            /!* <Cross2Icon className="ml-2 h-4 w-4" /> *!/*/}
            {/*        </Button>*/}
            {/*    )}*/}
            {/*</div>*/}
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <DataTableViewOptions table={table} />

                <Button

                    size="sm"
                    className="h-8"
                    onClick={onAddTax} // Trigger the add dialog
                >
                    <PlusCircle className="me-2 h-4 w-4" />
                    {t("addTaxButton")}
                </Button>
                {/* DataTableViewOptions should also fetch its own translations if needed */}
            </div>
        </div>
    );
}
