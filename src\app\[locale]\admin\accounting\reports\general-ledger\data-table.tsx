"use client";

import {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  SortingState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";

import { DataTable } from "@/components/ui/data-table";
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DateRange } from "react-day-picker";
import { GeneralLedgerDataTableToolbar } from "./data-table-toolbar";
import { GeneralLedgerEntryDto } from "@/lib/dto/admin/accounting/general-ledger.dto"; // Keep import for typing useReactTable
import { Skeleton } from "@/components/ui/skeleton"; // Import Skeleton
import { createColumns } from "./columns";
import { format } from "date-fns";
import { useGetGeneralLedger } from "@/lib/api/admin/accounting/general-ledger";
import { useTranslations } from "next-intl";

export function GeneralLedgerDataTable() {
  const t = useTranslations("GeneralLedger");
  const columns: ColumnDef<GeneralLedgerEntryDto>[] = useMemo(() => createColumns(), [t]); // Explicitly type columns

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [accountIds, setAccountIds] = useState<string[]>([]); // State for account filtering
  const [isPostedFilter, setIsPostedFilter] = useState<boolean | null | undefined>(undefined);

  const { data, isLoading, isError } = useGetGeneralLedger({
    page: pagination.pageIndex,
    size: pagination.pageSize,
    sort: sorting.map(s => `${s.id},${s.desc ? "desc" : "asc"}`),
    startDate: dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
    endDate: dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
    accountIds: accountIds.length > 0 ? accountIds : undefined,
    isPosted: isPostedFilter,
  });

  const table = useReactTable<GeneralLedgerEntryDto>({ // Explicitly type useReactTable
    data: data?.content || [],
    columns,
    pageCount: data?.totalPages || -1, // -1 allows the table to not know how many pages there are
    state: {
      pagination,
      sorting,
      columnFilters,
    },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(), // Fixed typo
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: true, // Tell the table we're doing pagination server-side
    manualSorting: true, // Tell the table we're doing sorting server-side
    manualFiltering: true, // Tell the table we're doing filtering server-side
  });

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    table.setPageIndex(0); // Reset to first page on filter change
  };

  const handleAccountSelect = (accountId: string | undefined) => {
    setAccountIds(accountId ? [accountId] : []); // Update state with single account ID or empty array
    table.setPageIndex(0); // Reset to first page on filter change
  };

  const handleIsPostedChange = (isPosted: boolean | null | undefined) => {
    setIsPostedFilter(isPosted);
    table.setPageIndex(0); // Reset to first page on filter change
  };

  if (isLoading) {
    // Render skeleton loading state
    return (
      <div className="space-y-4">
        <GeneralLedgerDataTableToolbar
          table={table} // Pass table prop even during loading for layout consistency
          onDateRangeChange={handleDateRangeChange}
          onAccountSelect={handleAccountSelect}
          onIsPostedChange={handleIsPostedChange}
        />
        <div className="rounded-md border p-4">
          <Skeleton className="h-8 w-full mb-4" /> {/* Skeleton for header */}
          {[...Array(pagination.pageSize)].map((_, index) => (
            <Skeleton key={index} className="h-6 w-full mb-2" /> // Skeletons for rows
          ))}
        </div>
        <Skeleton className="h-8 w-full" /> {/* Skeleton for pagination */}
      </div>
    );
  }

  if (isError) {
    return <div>{t("errorFetching")}</div>; // Add error state
  }

  return (
    <div className="space-y-4">
      <GeneralLedgerDataTableToolbar
        table={table}
        onDateRangeChange={handleDateRangeChange}
        onAccountSelect={handleAccountSelect}
        onIsPostedChange={handleIsPostedChange}
      />
      <div className="rounded-md border">
        <DataTable<GeneralLedgerEntryDto> table={table} columns={columns} /> {/* Type DataTable with TData */}
      </div>
      <DataTablePagination table={table} />
    </div>
  );
}
