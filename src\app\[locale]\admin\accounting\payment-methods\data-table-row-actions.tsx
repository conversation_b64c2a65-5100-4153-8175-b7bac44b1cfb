"use client";

import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Pen, Trash, Power, PowerOff } from "lucide-react"; // Add Power icons
import { PaymentMethodDto } from "@/lib/dto/admin/accounting/payment-methods.dto"; // Import correct DTO
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deletePaymentMethod, activatePaymentMethod, deactivatePaymentMethod } from "@/lib/api/admin/accounting/payment-methods"; // Import correct API functions
import { toast } from "sonner";
import { useState, useEffect } from "react"; // Import useEffect
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { EditPaymentMethodDialog } from "./edit-dialog"; // Import Edit Dialog

interface DataTableRowActionsProps<TData> {
    row: Row<TData>;
}

export function DataTableRowActions<TData>({
    row,
}: DataTableRowActionsProps<TData>) {
    const paymentMethod = row.original as PaymentMethodDto;
    const t = useTranslations("AdminPaymentMethodsPage.table.actions"); // Use correct scope
    const tConfirm = useTranslations("Shared.confirmationDialog");
    const queryClient = useQueryClient();
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false); // State for edit dialog

    const invalidateQueries = () => {
        queryClient.invalidateQueries({ queryKey: ["payment-methods"] });
    };

    const deleteMutation = useMutation({
        mutationFn: deletePaymentMethod,
        onSuccess: () => {
            toast.success(t("deleteSuccessToast", { name: paymentMethod.nameEn }));
            invalidateQueries();
        },
        onError: (error) => {
            toast.error(t("deleteErrorToast", { error: error.message }));
        },
        onSettled: () => {
            setIsDeleteDialogOpen(false);
        }
    });

    const activateMutation = useMutation({
        mutationFn: activatePaymentMethod,
        onSuccess: () => {
            toast.success(t("activateSuccessToast", { name: paymentMethod.nameEn }));
            invalidateQueries();
        },
        onError: (error) => {
            toast.error(t("activateErrorToast", { error: error.message }));
        },
    });

    const deactivateMutation = useMutation({
        mutationFn: deactivatePaymentMethod,
        onSuccess: () => {
            toast.success(t("deactivateSuccessToast", { name: paymentMethod.nameEn }));
            invalidateQueries();
        },
        onError: (error) => {
            toast.error(t("deactivateErrorToast", { error: error.message }));
        },
    });

    const handleDelete = () => {
        deleteMutation.mutate(paymentMethod.id);
    };

    const handleActivate = () => {
        activateMutation.mutate(paymentMethod.id);
    };

    const handleDeactivate = () => {
        deactivateMutation.mutate(paymentMethod.id);
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                    >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{t("openMenu")}</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                        <Pen className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("edit")}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {paymentMethod.active ? (
                        <DropdownMenuItem onClick={handleDeactivate} disabled={deactivateMutation.isPending}>
                            <PowerOff className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                            {t("deactivate")}
                        </DropdownMenuItem>
                    ) : (
                        <DropdownMenuItem onClick={handleActivate} disabled={activateMutation.isPending}>
                            <Power className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                            {t("activate")}
                        </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        variant="destructive"
                        onClick={() => setIsDeleteDialogOpen(true)}
                        disabled={deleteMutation.isPending}
                    >
                        <Trash className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("delete")}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Edit Dialog */}
            <EditPaymentMethodDialog
                isOpen={isEditDialogOpen}
                setIsOpen={setIsEditDialogOpen}
                paymentMethod={paymentMethod}
            />

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{tConfirm("title")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {tConfirm("deleteMessage", { item: `${t("paymentMethodItem")} ${paymentMethod.nameEn}` })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={deleteMutation.isPending}>{tConfirm("cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            disabled={deleteMutation.isPending}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {deleteMutation.isPending ? tConfirm("deleting") : tConfirm("delete")}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
