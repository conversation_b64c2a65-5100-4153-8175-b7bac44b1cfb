import { z } from 'zod';
import { UseTranslations } from 'next-intl';

// Define the categories based on the DTO
export const AccountCategoryEnum = z.enum(['ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE']);

// Helper function to create the schema with translations
export const createChartOfAccountSchema = (t: UseTranslations<'AdminChartOfAccountsPage.AddDialog.validation'>) => z.object({
    accountNumber: z.string()
        .min(1, t('accountNumberRequired'))
        .max(50, t('accountNumberTooLong')),
    nameEn: z.string()
        .min(1, t('nameEnRequired'))
        .max(255, t('nameEnTooLong')),
    nameAr: z.string()
        .min(1, t('nameArRequired'))
        .max(255, t('nameArTooLong')),
    descriptionEn: z.string()
        .max(1000, t('descriptionEnTooLong'))
        .optional(),
    descriptionAr: z.string()
        .max(1000, t('descriptionArTooLong'))
        .optional(),
    category: AccountCategoryEnum.refine(val => !!val, {
        message: t('categoryRequired'),
    }),
    parentAccountId: z.string().uuid(t('invalidParentAccount')).optional().nullable(), // Allow empty/null selection
});

// Infer the type from the schema
export type CreateChartOfAccountInput = z.infer<ReturnType<typeof createChartOfAccountSchema>>;

// Schema for updating a chart of account
export const updateChartOfAccountSchema = (t: UseTranslations<'AdminChartOfAccountsPage.EditDialog.validation'>) => z.object({
    // Account number is usually not updatable, so it's omitted
    nameEn: z.string()
        .min(1, t('nameEnRequired'))
        .max(255, t('nameEnTooLong'))
        .optional(), // Optional in case only other fields are updated
    nameAr: z.string()
        .min(1, t('nameArRequired'))
        .max(255, t('nameArTooLong'))
        .optional(), // Optional
    descriptionEn: z.string()
        .max(1000, t('descriptionEnTooLong'))
        .optional().nullable(),
    descriptionAr: z.string()
        .max(1000, t('descriptionArTooLong'))
        .optional().nullable(),
    category: AccountCategoryEnum.optional(), // Category might be updatable
    parentAccountId: z.string().uuid(t('invalidParentAccount')).optional().nullable(),
    active: z.boolean().optional(), // Active status is updatable
});
export type UpdateChartOfAccountInput = z.infer<ReturnType<typeof updateChartOfAccountSchema>>;
