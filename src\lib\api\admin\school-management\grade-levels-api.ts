import { API_ENDPOINTS, DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE } from '@/lib/constants';
import { GradeLevelDto, PageGradeLevelDto, GetGradeLevelsParams } from '@/lib/dto/admin/grade-level.dto';
import { Page } from '@/lib/dto/common.dto';

import { fetchWithAuth } from '@/lib/fetch-with-auth';
import { useQuery } from '@tanstack/react-query';

// API Hooks

/**
 * Fetches a paginated list of grade levels.
 */
export const useGetGradeLevels = (
  params: Partial<GetGradeLevelsParams> = {}
) => {
  const {
    page = DEFAULT_PAGE_INDEX,
    size = DEFAULT_PAGE_SIZE,
    stageId,
    sort
  } = params;

  return useQuery<PageGradeLevelDto | GradeLevelDto[], Error>({
    queryKey: [API_ENDPOINTS.ADMIN_GRADE_LEVELS, { page, size, stageId, sort }],
    queryFn: async () => {
      const queryParams = new URLSearchParams();

      if (page !== undefined) queryParams.append('page', page.toString());
      if (size !== undefined) queryParams.append('size', size.toString());
      if (stageId) queryParams.append('stageId', stageId);
      if (sort && sort.length > 0) {
        sort.forEach(s => queryParams.append('sort', s));
      }

      const url = queryParams.toString()
        ? `${API_ENDPOINTS.ADMIN_GRADE_LEVELS}?${queryParams.toString()}`
        : API_ENDPOINTS.ADMIN_GRADE_LEVELS;

      const response = await fetchWithAuth(url);

      if (!response.ok) {
        throw new Error('Failed to fetch grade levels');
      }

      return response.json();
    },
  });
};

/**
 * Fetches a single grade level by its ID.
 */
export const useGetGradeLevelById = (id: string | undefined, options?: { enabled?: boolean }) => {
  return useQuery<GradeLevelDto, Error>({
    queryKey: [API_ENDPOINTS.ADMIN_GRADE_LEVELS, id],
    queryFn: async () => {
      if (!id) throw new Error('Grade Level ID is required');
      const response = await fetchWithAuth(`${API_ENDPOINTS.ADMIN_GRADE_LEVELS}/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch grade level');
      }

      return response.json();
    },
    enabled: options?.enabled !== undefined ? options.enabled && !!id : !!id,
  });
};
