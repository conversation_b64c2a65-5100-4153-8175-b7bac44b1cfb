import { ErrorResponse } from '@/lib/dto/error-response.dto'; // Assuming ErrorResponse DTO exists

/**
 * Parses API error responses and throws a more informative error.
 * @param response - The Fetch API Response object.
 * @param defaultMessage - A default message to use if the API doesn't provide one.
 * @throws An Error with a message derived from the API response or the default message.
 */
export async function handleApiError(response: Response, defaultMessage: string = 'An API error occurred'): Promise<never> {
    let errorMessage = defaultMessage;
    let errorDetails: Record<string, string[]> | undefined;

    try {
        const errorData: ErrorResponse = await response.json();
        // Use the message from the API if available
        if (errorData?.message) {
            errorMessage = errorData.message;
        }
        // Include field errors if present
        if (errorData?.fieldErrors && Object.keys(errorData.fieldErrors).length > 0) {
             errorDetails = errorData.fieldErrors;
             // Optionally append field errors to the main message or handle them differently
             const fieldErrorSummary = Object.entries(errorData.fieldErrors)
                 .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
                 .join('; ');
             errorMessage += ` (Details: ${fieldErrorSummary})`;
        }
    } catch (e) {
        // If parsing JSON fails, use the response status text or the default message
        errorMessage = response.statusText || defaultMessage;
        console.error("Failed to parse API error response:", e);
    }

    // Create a custom error object if needed, or just throw a standard Error
    const error = new Error(errorMessage);
    // You could potentially add the status code or details to the error object
    // (error as any).status = response.status;
    // (error as any).details = errorDetails;

    throw error;
}

/**
 * Type guard to check if an error object has field errors structure.
 * Useful for displaying specific field errors in forms.
 */
export function hasFieldErrors(error: unknown): error is { details?: Record<string, string[]> } {
  return typeof error === 'object' && error !== null && 'details' in error && typeof (error as any).details === 'object';
}
