import { API_BASE_URL } from "@/lib/constants";
import { BalanceSheetDto } from "@/lib/dto/admin/accounting/balance-sheet.dto";
import { ErrorResponse } from "@/lib/dto/error-response.dto";
import { fetchWithAuth } from "@/lib/fetch-with-auth";

const BALANCE_SHEET_API_PATH = `${API_BASE_URL}/accounting/balance-sheet`;

export async function getBalanceSheet(
  fromDate?: string,
  toDate?: string
): Promise<BalanceSheetDto> { // Change return type to only BalanceSheetDto
  const params = new URLSearchParams();
  if (fromDate) {
    params.append("fromDate", fromDate);
  }
  if (toDate) {
    params.append("toDate", toDate);
  }

  const url = `${BALANCE_SHEET_API_PATH}${params.toString() ? `?${params.toString()}` : ""}`;

  const response = await fetchWithAuth(url, {
    method: "GET",
  });

  const data = await response.json();

  if (!response.ok) {
    // Throw the error response directly
    throw data as ErrorResponse;
  }

  return data as BalanceSheetDto;
}
