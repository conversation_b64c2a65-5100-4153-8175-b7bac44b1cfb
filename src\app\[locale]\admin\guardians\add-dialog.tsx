"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { PlusCircle } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { createGuardianProfile } from "@/lib/api/admin/guardians";
import { CreateGuardianProfileInput, createGuardianProfileSchema } from "@/lib/schemas/admin/guardian";
import { getErrorMessage } from "@/lib/utils";
import { getUsers } from "@/lib/api/admin/users";
import { UserDto } from "@/lib/dto/admin/user.dto";

interface AddGuardianDialogProps {
    onSuccess?: () => void; // Optional callback
}

export function AddGuardianDialog({ onSuccess }: AddGuardianDialogProps) {
    const t = useTranslations("AdminGuardiansPage.AddDialog");
    const queryClient = useQueryClient();
    const [isOpen, setIsOpen] = React.useState(false);

    // Fetch users for dropdown
    const { data: users, isLoading: isLoadingUsers } = useQuery({
        queryKey: ["users"],
        queryFn: () => getUsers({ size: 100 }), // Fetch up to 100 users
        enabled: isOpen, // Only fetch when dialog is open
    });

    const form = useForm<CreateGuardianProfileInput>({
        resolver: zodResolver(createGuardianProfileSchema),
        defaultValues: {
            userId: "",
            occupation: "",
            address: "",
            nationalId: "",
            idType: "NATIONAL_ID",
        },
    });

    const mutation = useMutation({
        mutationFn: createGuardianProfile,
        onSuccess: (data) => {
            toast.success(t("successToast", { name: `${data.userAccount.firstName} ${data.userAccount.lastName}` }));
            queryClient.invalidateQueries({ queryKey: ["guardians"] });
            setIsOpen(false); // Close dialog
            form.reset(); // Reset form
            onSuccess?.();
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: getErrorMessage(error) }));
        },
    });

    const onSubmit = (data: CreateGuardianProfileInput) => {
        mutation.mutate(data);
    };

    // Reset form when dialog opens/closes
    React.useEffect(() => {
        if (!isOpen) {
            form.reset();
        }
    }, [isOpen, form]);

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button size="sm" className="ml-auto h-8">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t("triggerButton")}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description")}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4 py-4">
                        <FormField
                            control={form.control}
                            name="userId"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("userIdLabel")}</FormLabel>
                                    <Select
                                        disabled={isLoadingUsers}
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder={t("userIdPlaceholder")} />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {users?.content?.map((user: UserDto) => (
                                                <SelectItem key={user.id} value={user.id}>
                                                    {`${user.firstName} ${user.lastName} (${user.email})`}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormDescription>{t("userIdDescription")}</FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="occupation"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("occupationLabel")}</FormLabel>
                                    <FormControl>
                                        <Input placeholder={t("occupationPlaceholder")} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="address"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("addressLabel")}</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder={t("addressPlaceholder")}
                                            className="resize-none"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="nationalId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("nationalIdLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("nationalIdPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="idType"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("idTypeLabel")}</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("idTypePlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem value="NATIONAL_ID">{t("idTypeNationalId")}</SelectItem>
                                                <SelectItem value="PASSPORT">{t("idTypePassport")}</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <DialogFooter>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("submitting") : t("submit")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
