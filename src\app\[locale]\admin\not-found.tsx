'use client'; // Keep this as a client component for potential future interactions

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function AdminNotFound() {
  const t = useTranslations('NotFound');

  return (
    <div className="flex min-h-[calc(100vh-4rem)] items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">{t('adminTitle')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">{t('adminMessage')}</p>
          <Button asChild>
            <Link href="/admin/dashboard">{t('adminGoBackButton')}</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
