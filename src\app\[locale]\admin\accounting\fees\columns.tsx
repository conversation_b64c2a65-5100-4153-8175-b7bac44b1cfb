"use client";

import { ColumnDef } from "@tanstack/react-table";
import { FeeDto } from "@/lib/dto/admin/accounting/fees.dto";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { useTranslations } from "next-intl";

// Define props if needed, e.g., for passing translation function or callbacks
interface ColumnsProps {
    t: ReturnType<typeof useTranslations<'AdminFeesPage.table'>>;
    tShared: ReturnType<typeof useTranslations<'Shared'>>;
    onFeeUpdated?: () => void; // Optional callback after update
    onFeeDeleted?: () => void; // Optional callback after delete
}

export const getColumns = ({ t, tShared }: ColumnsProps): ColumnDef<FeeDto>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "nameEn",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("nameEn")} />
        ),
        cell: ({ row }) => <div className="w-[150px] truncate">{row.getValue("nameEn")}</div>,
        enableSorting: true,
        enableHiding: true,
    },
    {
        accessorKey: "nameAr",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("nameAr")} />
        ),
        cell: ({ row }) => <div className="w-[150px] truncate">{row.getValue("nameAr")}</div>,
        enableSorting: true,
        enableHiding: true,
    },
    {
        accessorKey: "amount",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("amount")} />
        ),
        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("amount"));
            // Format as currency (adapt locale and currency as needed)
            const formatted = new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD", // TODO: Make currency dynamic if needed
            }).format(amount);
            return <div className="text-right font-medium">{formatted}</div>;
        },
        enableSorting: true,
        enableHiding: true,
    },
    {
        accessorKey: "academicYear",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("academicYear")} />
        ),
        cell: ({ row }) => <div className="w-[100px]">{row.getValue("academicYear")}</div>,
        enableSorting: true,
        enableHiding: true,
    },
    {
        accessorKey: "dueDate",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("dueDate")} />
        ),
        cell: ({ row }) => {
            const date = row.getValue("dueDate") as string;
            // Format date (adapt locale as needed)
            const formattedDate = date ? new Date(date).toLocaleDateString() : '-';
            return <div className="w-[100px]">{formattedDate}</div>;
        },
        enableSorting: true,
        enableHiding: true,
    },
    {
        accessorKey: "feeCategory",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("category")} />
        ),
        cell: ({ row }) => {
            const category = row.original.feeCategory;
            // TODO: Display based on current locale
            return <div className="w-[150px] truncate">{category?.nameEn || '-'}</div>;
        },
        enableSorting: false, // Sorting by nested object might require specific setup
        enableHiding: true,
    },
    {
        accessorKey: "active",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("status")} />
        ),
        cell: ({ row }) => {
            const isActive = row.getValue("active");
            return (
                <Badge variant={isActive ? "success" : "destructive"}>
                    {isActive ? tShared("statusActive") : tShared("statusInactive")}
                </Badge>
            );
        },
        enableSorting: true,
        enableHiding: true,
        filterFn: (row, id, value) => {
             // Custom filter function for boolean status
             return value.includes(row.getValue(id)?.toString());
        },
    },
    {
        id: "actions",
        cell: ({ row }) => <DataTableRowActions row={row} />,
        enableSorting: false,
        enableHiding: false,
    },
];
