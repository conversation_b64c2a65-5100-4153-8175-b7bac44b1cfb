"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { Switch } from "@/components/ui/switch"; // For active status
import { updateFee } from "@/lib/api/admin/accounting/fees";
import { UpdateFeeInput, updateFeeSchema } from "@/lib/schemas/admin/accounting/fees";
import { FeeDto } from "@/lib/dto/admin/accounting/fees.dto";
import { getErrorMessage } from "@/lib/utils";
import { getAllFeeCategoriesList } from "@/lib/api/admin/accounting/fee-categories"; // Correct function name
import { getAllGradeLevelsList } from "@/lib/api/admin/grade-levels"; // Correct function name
import { getAllEducationalStagesList } from "@/lib/api/admin/educational-stages"; // Correct function name
import { getAllBranchesList } from "@/lib/api/admin/branches"; // Correct function name
import { getAllAcademicYearsList } from "@/lib/api/admin/academic-year"; // Add academic years API
import { FeeCategoryDto } from "@/lib/dto/admin/accounting/fee-categories.dto";
import { GradeLevelDto } from "@/lib/dto/admin/grade-level.dto"; // Correct DTO path
import { EducationalStageDto } from "@/lib/dto/admin/educational-stage.dto";
import { BranchDto } from "@/lib/dto/admin/branch.dto";
import { AcademicYearDto } from "@/lib/dto/admin/academic-year.dto"; // Add academic year DTO

interface EditFeeDialogProps {
    isOpen: boolean;
    setIsOpen: (open: boolean) => void;
    fee: FeeDto;
    onFeeUpdated?: () => void; // Optional callback
}

// Helper functions (can be reused or placed in a common file)
async function fetchFeeCategories(): Promise<FeeCategoryDto[]> { /* ... same as AddDialog ... */
    try {
        return await getAllFeeCategoriesList();
    } catch (error) {
        console.error("Failed to fetch fee categories:", error);
        toast.error("Failed to load fee categories for selection.");
        return [];
    }
}
async function fetchGradeLevels(): Promise<GradeLevelDto[]> { /* ... same as AddDialog ... */
     try {
        return await getAllGradeLevelsList();
    } catch (error) {
        console.error("Failed to fetch grade levels:", error);
        toast.error("Failed to load grade levels for selection.");
        return [];
    }
}
async function fetchEducationalStages(): Promise<EducationalStageDto[]> { /* ... same as AddDialog ... */
     try {
        return await getAllEducationalStagesList();
    } catch (error) {
        console.error("Failed to fetch educational stages:", error);
        toast.error("Failed to load educational stages for selection.");
        return [];
    }
}
async function fetchBranches(): Promise<BranchDto[]> { /* ... same as AddDialog ... */
     try {
        return await getAllBranchesList();
    } catch (error) {
        console.error("Failed to fetch branches:", error);
        toast.error("Failed to load branches for selection.");
        return [];
    }
}
async function fetchAcademicYears(): Promise<AcademicYearDto[]> {
     try {
        return await getAllAcademicYearsList();
    } catch (error) {
        console.error("Failed to fetch academic years:", error);
        toast.error("Failed to load academic years for selection.");
        return [];
    }
}

export function EditFeeDialog({ isOpen, setIsOpen, fee, onFeeUpdated }: EditFeeDialogProps) {
    const t = useTranslations("AdminFeesPage.EditDialog");
    const queryClient = useQueryClient();

    const form = useForm<UpdateFeeInput>({
        resolver: zodResolver(updateFeeSchema(t)),
        defaultValues: {
            nameEn: fee.nameEn,
            nameAr: fee.nameAr,
            descriptionEn: fee.descriptionEn ?? "",
            descriptionAr: fee.descriptionAr ?? "",
            amount: fee.amount,
            academicYearId: fee.academicYearId, // Changed from academicYear to academicYearId
            dueDate: fee.dueDate ? new Date(fee.dueDate) : undefined, // Convert string to Date
            feeCategoryId: fee.feeCategory?.id ?? "",
            applicableGradeId: fee.applicableGradeId ?? null,
            applicableStageId: fee.applicableStageId ?? null,
            applicableBranchId: fee.applicableBranchId ?? null,
            active: fee.active,
        },
    });

     // Reset form when fee prop changes (e.g., opening dialog for a different fee)
    useEffect(() => {
        if (fee) {
            form.reset({
                nameEn: fee.nameEn,
                nameAr: fee.nameAr,
                descriptionEn: fee.descriptionEn ?? "",
                descriptionAr: fee.descriptionAr ?? "",
                amount: fee.amount,
                academicYearId: fee.academicYearId, // Changed from academicYear to academicYearId
                dueDate: fee.dueDate ? new Date(fee.dueDate) : undefined,
                feeCategoryId: fee.feeCategory?.id ?? "",
                applicableGradeId: fee.applicableGradeId ?? null,
                applicableStageId: fee.applicableStageId ?? null,
                applicableBranchId: fee.applicableBranchId ?? null,
                active: fee.active,
            });
        }
    }, [fee, form]);


    // Fetch data for dropdowns
    const { data: feeCategories, isLoading: isLoadingCategories } = useQuery({
        queryKey: ["feeCategoriesList"],
        queryFn: fetchFeeCategories,
        enabled: isOpen,
        staleTime: 5 * 60 * 1000,
    });
     const { data: gradeLevels, isLoading: isLoadingGrades } = useQuery({
        queryKey: ["gradeLevelsList"],
        queryFn: fetchGradeLevels,
        enabled: isOpen,
        staleTime: 5 * 60 * 1000,
    });
     const { data: educationalStages, isLoading: isLoadingStages } = useQuery({
        queryKey: ["educationalStagesList"],
        queryFn: fetchEducationalStages,
        enabled: isOpen,
        staleTime: 5 * 60 * 1000,
    });
     const { data: branches, isLoading: isLoadingBranches } = useQuery({
        queryKey: ["branchesList"],
        queryFn: fetchBranches,
        enabled: isOpen,
        staleTime: 5 * 60 * 1000,
    });

    const { data: academicYears, isLoading: isLoadingAcademicYears } = useQuery({
        queryKey: ["academicYearsList"],
        queryFn: fetchAcademicYears,
        enabled: isOpen,
        staleTime: 5 * 60 * 1000,
    });

    const mutation = useMutation({
        mutationFn: (data: UpdateFeeInput) => updateFee(fee.id, data),
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.nameEn }));
            queryClient.invalidateQueries({ queryKey: ["fees"] });
            queryClient.invalidateQueries({ queryKey: ["fee", fee.id] }); // Invalidate specific fee
            onFeeUpdated?.();
            setIsOpen(false);
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: getErrorMessage(error) }));
        },
    });

    const onSubmit = (data: UpdateFeeInput) => {
        // Format date back to string if changed
        const formattedData = {
            ...data,
            dueDate: data.dueDate ? data.dueDate.toISOString().split('T')[0] : undefined,
            // Ensure optional IDs are sent correctly (null or UUID)
            applicableGradeId: data.applicableGradeId || null,
            applicableStageId: data.applicableStageId || null,
            applicableBranchId: data.applicableBranchId || null,
        };
         console.log("Submitting Updated Fee Data:", formattedData);
        mutation.mutate(formattedData);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description", { name: fee.nameEn })}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        {/* Form Fields similar to AddFeeDialog, but using UpdateFeeInput */}
                         <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            <FormField
                                control={form.control}
                                name="nameEn"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("nameEnLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="nameAr"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("nameArLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("nameArPlaceholder")} {...field} dir="rtl" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <FormField
                            control={form.control}
                            name="amount"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("amountLabel")}</FormLabel>
                                    <FormControl>
                                        <Input type="number" step="0.01" placeholder={t("amountPlaceholder")} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                         <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                             <FormField
                                control={form.control}
                                name="academicYearId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("academicYearLabel")}</FormLabel>
                                        <Select onValueChange={field.onChange} value={field.value ?? ""} disabled={isLoadingAcademicYears}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("academicYearPlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {academicYears?.map((academicYear) => (
                                                    <SelectItem key={academicYear.id} value={academicYear.id}>
                                                        {academicYear.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                             <FormField
                                control={form.control}
                                name="dueDate"
                                render={({ field }) => (
                                    <FormItem className="flex flex-col pt-2">
                                        <FormLabel>{t("dueDateLabel")}</FormLabel>
                                        <FormControl className="mt-1.5">
                                             <DatePicker
                                                date={field.value}
                                                setDate={field.onChange}
                                                placeholder={t("dueDatePlaceholder")}
                                                className="w-full"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                         </div>

                         <FormField
                            control={form.control}
                            name="feeCategoryId"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("categoryLabel")}</FormLabel>
                                    <Select onValueChange={field.onChange} value={field.value ?? ""} disabled={isLoadingCategories}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder={t("categoryPlaceholder")} />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {feeCategories?.map((category) => (
                                                <SelectItem key={category.id} value={category.id}>
                                                    {category.nameEn} / {category.nameAr}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Applicability Dropdowns */}
                         <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                             <FormField
                                control={form.control}
                                name="applicableGradeId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("applicableGradeLabel")}</FormLabel>
                                        <Select onValueChange={field.onChange} value={field.value ?? ""} disabled={isLoadingGrades}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("applicableGradePlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                               <SelectItem value="--ALL--">{t("allGradesValue")}</SelectItem>
                                                {gradeLevels?.map((grade) => (
                                                    <SelectItem key={grade.id} value={grade.id}>
                                                        {grade.nameEn} / {grade.nameAr}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                             <FormField
                                control={form.control}
                                name="applicableStageId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("applicableStageLabel")}</FormLabel>
                                        <Select onValueChange={field.onChange} value={field.value ?? ""} disabled={isLoadingStages}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("applicableStagePlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                 <SelectItem value="">{t("allStagesValue")}</SelectItem>
                                                {educationalStages?.map((stage) => (
                                                    <SelectItem key={stage.id} value={stage.id}>
                                                        {stage.nameEn} / {stage.nameAr}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                             <FormField
                                control={form.control}
                                name="applicableBranchId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("applicableBranchLabel")}</FormLabel>
                                        <Select onValueChange={field.onChange} value={field.value ?? ""} disabled={isLoadingBranches}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("applicableBranchPlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                 <SelectItem value="">{t("allBranchesValue")}</SelectItem>
                                                {branches?.map((branch) => (
                                                    <SelectItem key={branch.id} value={branch.id}>
                                                        {branch.nameEn} / {branch.nameAr}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                         </div>
                         <FormDescription>{t("applicabilityDescription")}</FormDescription>


                        <FormField
                            control={form.control}
                            name="descriptionEn"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                    <FormControl>
                                        <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} value={field.value ?? ''} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="descriptionAr"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                    <FormControl>
                                        <Textarea placeholder={t("descriptionArPlaceholder")} {...field} value={field.value ?? ''} dir="rtl" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                         <FormField
                            control={form.control}
                            name="active"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                                    <div className="space-y-0.5">
                                        <FormLabel>{t("activeLabel")}</FormLabel>
                                        <FormDescription>
                                            {t("activeDescription")}
                                        </FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />


                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending || !form.formState.isDirty}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
