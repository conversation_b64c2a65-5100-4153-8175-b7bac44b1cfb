import { BalanceSheetAccountDto, BalanceSheetDto, BalanceSheetSectionDto } from "@/lib/dto/admin/accounting/balance-sheet.dto";

import { useTranslations } from "next-intl";

interface BalanceSheetDisplayProps {
  data: BalanceSheetDto;
}

export function BalanceSheetDisplay({ data }: BalanceSheetDisplayProps) {
  const t = useTranslations("BalanceSheetPage"); // Assuming translations will be under this key

  const renderSection = (section: BalanceSheetSectionDto) => (
    <div key={section.category} className="mb-6">
      <h3 className="text-lg font-semibold mb-2">{t(section.category.toLowerCase())}</h3> {/* Translate category */}
      <div className="ml-4">
        {section.accounts.map((account) => (
          <div key={account.accountId} className="flex justify-between border-b py-1">
            <span>{account.accountNameEn} / {account.accountNameAr} ({account.accountNumber})</span>
            <span>{account.balance.toFixed(2)}</span> {/* Format balance */}
          </div>
        ))}
        <div className="flex justify-between font-semibold mt-2 pt-2 border-t">
          <span>{t("total")} {t(section.category.toLowerCase())}</span> {/* Translate total */}
          <span>{section.totalBalance.toFixed(2)}</span> {/* Format total balance */}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      <div className="text-right text-sm">
        {t("reportDate")}: {data.reportDate} {/* Display report date */}
      </div>
      {renderSection(data.assets)}
      {renderSection(data.liabilities)}
      {renderSection(data.equity)}

      {/* Add a check for Assets = Liabilities + Equity */}
      <div className="flex justify-between font-bold mt-8 pt-4 border-t-2">
         <span>{t("totalLiabilitiesAndEquity")}</span> {/* Translate total liabilities and equity */}
         <span>{(data.liabilities.totalBalance + data.equity.totalBalance).toFixed(2)}</span> {/* Calculate and format total */}
      </div>
    </div>
  );
}
