"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { getChartOfAccountById, updateChartOfAccount, getChartOfAccounts } from "@/lib/api/admin/accounting/chart-of-accounts";
import { UpdateChartOfAccountInput, AccountCategoryEnum, updateChartOfAccountSchema } from "@/lib/schemas/admin/accounting/chart-of-accounts";
import { UpdateChartOfAccountRequest } from "@/lib/dto/admin/accounting/chart-of-accounts.dto"; // Import request type
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";

interface EditAccountDialogProps {
    accountId: string;
    isOpen: boolean;
    onOpenChange: (isOpen: boolean) => void;
}

export function EditAccountDialog({ accountId, isOpen, onOpenChange }: EditAccountDialogProps) {
    const t = useTranslations("AdminChartOfAccountsPage.EditDialog");
    const tValidation = useTranslations("AdminChartOfAccountsPage.EditDialog.validation");
    const tCategories = useTranslations("AdminChartOfAccountsPage.categories");
    const tAdd = useTranslations("AdminChartOfAccountsPage.AddDialog"); // Reuse some translations
    const queryClient = useQueryClient();

    const formSchema = updateChartOfAccountSchema(tValidation);

    // Fetch the specific account data
    const { data: accountData, isLoading: isLoadingAccount, isError, error } = useQuery({
        queryKey: ["chart-of-account", accountId],
        queryFn: () => getChartOfAccountById(accountId),
        enabled: isOpen, // Only fetch when the dialog is open
    });

    // Fetch potential parent accounts (fetch all active for selection)
    const { data: parentAccountsData, isLoading: isLoadingParents } = useQuery({
        // Fetch a larger size or implement search if list is very long
        queryKey: ["chart-of-accounts", { page: 0, size: 500, activeOnly: true }],
        queryFn: () => getChartOfAccounts({ page: 0, size: 500, activeOnly: true }),
        enabled: isOpen,
        staleTime: 300000, // Cache for 5 minutes
    });

    const form = useForm<UpdateChartOfAccountInput>({
        resolver: zodResolver(formSchema),
        defaultValues: { // Set default values once data is loaded
            nameEn: "",
            nameAr: "",
            descriptionEn: "",
            descriptionAr: "",
            category: undefined,
            parentAccountId: undefined,
            active: true, // Default to true, will be overridden by fetched data
        },
    });

    // Reset form when account data loads or changes
    useEffect(() => {
        if (accountData) {
            form.reset({
                nameEn: accountData.nameEn,
                nameAr: accountData.nameAr,
                descriptionEn: accountData.descriptionEn ?? "",
                descriptionAr: accountData.descriptionAr ?? "",
                category: accountData.category,
                parentAccountId: accountData.parentAccount?.id ?? undefined,
                active: accountData.active,
            });
        }
    }, [accountData, form]);


    const mutation = useMutation({
        mutationFn: (data: UpdateChartOfAccountRequest) => updateChartOfAccount(accountId, data),
        onSuccess: (data) => {
            toast.success(t("successToast", { accountNumber: data.accountNumber }));
            queryClient.invalidateQueries({ queryKey: ["chart-of-accounts"] }); // Invalidate main table
            queryClient.invalidateQueries({ queryKey: ["chart-of-account", accountId] }); // Invalidate this specific account
            onOpenChange(false); // Close dialog
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message }));
        },
    });

    const onSubmit = (values: UpdateChartOfAccountInput) => {
        // Construct the payload ensuring required fields are present
        // and optional fields are null if empty.
        const payload: UpdateChartOfAccountRequest = {
            nameEn: values.nameEn || accountData?.nameEn, // Ensure nameEn is sent
            nameAr: values.nameAr || accountData?.nameAr, // Ensure nameAr is sent
            category: values.category || accountData?.category, // Ensure category is sent
            active: values.active, // Active is required boolean
            descriptionEn: values.descriptionEn || null, // Send null if empty
            descriptionAr: values.descriptionAr || null, // Send null if empty
            parentAccountId: values.parentAccountId || null, // Send null if empty/undefined
        }

        // Check if anything actually changed compared to original data
        if (payload.nameEn === accountData?.nameEn &&
            payload.nameAr === accountData?.nameAr &&
            payload.category === accountData?.category &&
            payload.active === accountData?.active &&
            payload.descriptionEn === (accountData?.descriptionEn ?? null) &&
            payload.descriptionAr === (accountData?.descriptionAr ?? null) &&
            payload.parentAccountId === (accountData?.parentAccount?.id ?? null))
        {
             toast.info(t("noChanges"));
             return;
        }

        mutation.mutate(payload);
    };

    const isLoading = isLoadingAccount || isLoadingParents;

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>{t("title", { accountNumber: accountData?.accountNumber ?? '...' })}</DialogTitle>
                    <DialogDescription>{t("description", { accountNumber: accountData?.accountNumber ?? '...' })}</DialogDescription>
                </DialogHeader>
                {isLoading ? (
                    <div className="space-y-4 py-4">
                        {/* Skeletons matching form structure */}
                        <Skeleton className="h-8 w-full" />
                        <div className="grid grid-cols-2 gap-4">
                            <Skeleton className="h-8 w-full" />
                            <Skeleton className="h-8 w-full" />
                        </div>
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-20 w-full" />
                        <Skeleton className="h-20 w-full" />
                        <Skeleton className="h-8 w-1/3" />
                    </div>
                ) : isError ? (
                     <div className="py-4 text-center text-destructive">
                        {t("errorLoadingAccount", { error: error?.message || 'Unknown error' })}
                    </div>
                ) : (
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <ScrollArea className="h-[50vh] pr-6">
                                <div className="space-y-4 p-1">
                                    {/* Account Number (Readonly) */}
                                    <FormItem>
                                        <FormLabel>{tAdd("accountNumberLabel")}</FormLabel>
                                        <FormControl>
                                            <Input value={accountData?.accountNumber ?? ''} readOnly disabled className="bg-muted/50" />
                                        </FormControl>
                                        <FormDescription>{t("accountNumberDescription")}</FormDescription>
                                    </FormItem>

                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <FormField
                                            control={form.control}
                                            name="nameEn"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>{t("nameEnLabel")}</FormLabel>
                                                    <FormControl>
                                                        <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="nameAr"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>{t("nameArLabel")}</FormLabel>
                                                    <FormControl>
                                                        <Input dir="rtl" placeholder={t("nameArPlaceholder")} {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>
                                    <FormField
                                        control={form.control}
                                        name="category"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("categoryLabel")}</FormLabel>
                                                <Select onValueChange={field.onChange} value={field.value}>
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder={t("categoryPlaceholder")} />
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {AccountCategoryEnum.options.map((category) => (
                                                            <SelectItem key={category} value={category}>
                                                                {tCategories(category as any)}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="parentAccountId"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("parentAccountLabel")}</FormLabel>
                                                <Select
                                                    onValueChange={(value) => field.onChange(value === "__NONE__" ? undefined : value)}
                                                    value={field.value ?? "__NONE__"} // Use value, fallback to "__NONE__"
                                                    disabled={isLoadingParents}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            {isLoadingParents ? (
                                                                <Skeleton className="h-6 w-3/4" />
                                                            ) : (
                                                                <SelectValue placeholder={t("parentAccountPlaceholder")} />
                                                            )}
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        <SelectItem value="__NONE__">{tAdd("noParentValue")}</SelectItem>
                                                        {parentAccountsData?.content
                                                            ?.filter(acc => acc.id !== accountId) // Exclude self
                                                            .map((account) => (
                                                            <SelectItem key={account.id} value={account.id}>
                                                                {account.accountNumber} - {account.nameEn} / {account.nameAr}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormDescription>{tAdd("parentAccountDescription")}</FormDescription>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="descriptionEn"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                                <FormControl>
                                                    <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="descriptionAr"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                                <FormControl>
                                                    <Textarea dir="rtl" placeholder={t("descriptionArPlaceholder")} {...field} value={field.value ?? ''} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="active"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                                                <div className="space-y-0.5">
                                                    <FormLabel>{t("activeLabel")}</FormLabel>
                                                    <FormDescription>{t("activeDescription")}</FormDescription>
                                                </div>
                                                <FormControl>
                                                    <Switch
                                                        checked={field.value}
                                                        onCheckedChange={field.onChange}
                                                    />
                                                </FormControl>
                                            </FormItem>
                                        )}
                                    />
                                </div>
                            </ScrollArea>
                            <DialogFooter>
                                <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={mutation.isPending}>
                                    {t("cancelButton")}
                                </Button>
                                <Button type="submit" disabled={mutation.isPending || isLoading}>
                                    {mutation.isPending ? t("savingButton") : t("saveButton")}
                                </Button>
                            </DialogFooter>
                        </form>
                    </Form>
                )}
            </DialogContent>
        </Dialog>
    );
}
