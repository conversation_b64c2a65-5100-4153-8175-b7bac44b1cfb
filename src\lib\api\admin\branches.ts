import { fetchWithAuth } from '@/lib/fetch-with-auth';
import { API_BASE_URL } from "@/lib/constants";
import {
    BranchDto,
    CreateBranchRequest,
    UpdateBranchRequest,
} from '@/lib/dto/admin/branch.dto';
import { ErrorResponse } from '@/lib/dto/error-response.dto';

/**
 * Fetches all branches.
 * Note: API docs don't specify pagination/search for this endpoint.
 * @returns A promise resolving to an array of BranchDto.
 * @throws ErrorResponse if the request fails.
 */
export async function getAllBranchesList(): Promise<BranchDto[]> { // Renamed function
    const url = `${API_BASE_URL}/admin/branches`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch branches: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation if necessary (e.g., with Zod)
        return data as BranchDto[];
    } catch (error) {
        console.error("Error fetching branches:", error);
        throw error; // Re-throw the error to be handled by the caller
    }
}

/**
 * Fetches a single branch by its ID.
 * @param id The ID of the branch to fetch.
 * @returns A promise resolving to a BranchDto.
 * @throws ErrorResponse if the request fails (e.g., not found).
 */
export async function getBranchById(id: string): Promise<BranchDto> {
    const url = `${API_BASE_URL}/admin/branches/${id}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch branch ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation if necessary
        return data as BranchDto;
    } catch (error) {
        console.error(`Error fetching branch ${id}:`, error);
        throw error;
    }
}

/**
 * Creates a new branch.
 * @param request The data for the new branch.
 * @returns A promise resolving to the created BranchDto.
 * @throws ErrorResponse if the request fails.
 */
export async function createBranch(request: CreateBranchRequest): Promise<BranchDto> {
    const url = `${API_BASE_URL}/admin/branches`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request),
        });
        if (!response.ok) { // Check for 200 OK specifically, as API returns 200 on create
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to create branch: ${response.statusText}`);
        }
        const data: unknown = await response.json();
         // TODO: Add robust validation if necessary
        return data as BranchDto;
    } catch (error) {
        console.error("Error creating branch:", error);
        throw error;
    }
}

/**
 * Updates an existing branch.
 * @param id The ID of the branch to update.
 * @param request The data to update the branch with.
 * @returns A promise resolving to the updated BranchDto.
 * @throws ErrorResponse if the request fails.
 */
export async function updateBranch(id: string, request: UpdateBranchRequest): Promise<BranchDto> {
    const url = `${API_BASE_URL}/admin/branches/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request),
        });
        if (!response.ok) {
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to update branch ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
         // TODO: Add robust validation if necessary
        return data as BranchDto;
    } catch (error) {
        console.error(`Error updating branch ${id}:`, error);
        throw error;
    }
}

/**
 * Deletes a branch.
 * @param id The ID of the branch to delete.
 * @returns A promise resolving when the deletion is successful.
 * @throws ErrorResponse if the request fails.
 */
export async function deleteBranch(id: string): Promise<void> {
    const url = `${API_BASE_URL}/admin/branches/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'DELETE',
        });
        if (!response.ok) { // Check for 200 OK as per API docs for delete
             const errorData: ErrorResponse = await response.json().catch(() => ({}));
             // Attempt to parse error, provide default if parsing fails or no message
             const errorMessage = errorData?.message || `Failed to delete branch ${id}: ${response.statusText}`;
             throw new Error(errorMessage);
        }
        // No content expected on successful delete based on API docs (returns 200 OK)
    } catch (error) {
        console.error(`Error deleting branch ${id}:`, error);
        throw error;
    }
}
