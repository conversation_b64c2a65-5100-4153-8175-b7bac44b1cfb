import { useTranslations } from "next-intl";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Activity, CreditCard, DollarSign, Users } from "lucide-react"; // Example icons for cards

export default function DashboardPage() {
  const t = useTranslations("AdminDashboard");
  const tCards = useTranslations("AdminDashboard.Cards"); // Namespace for card translations

  return (
    <div className="flex flex-col gap-4 md:gap-8">
      {/* Optional: Page Title */}
      {/* <h1 className="text-2xl font-semibold">{t("title")}</h1> */}

      {/* Grid for Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {tCards("totalRevenueTitle")}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tCards("totalRevenueValue")}</div>
            <p className="text-xs text-muted-foreground">
              {tCards("totalRevenueDescription")}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {tCards("subscriptionsTitle")}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tCards("subscriptionsValue")}</div>
            <p className="text-xs text-muted-foreground">
              {tCards("subscriptionsDescription")}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{tCards("salesTitle")}</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tCards("salesValue")}</div>
            <p className="text-xs text-muted-foreground">
              {tCards("salesDescription")}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {tCards("activeNowTitle")}
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tCards("activeNowValue")}</div>
            <p className="text-xs text-muted-foreground">
              {tCards("activeNowDescription")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Placeholder for other dashboard sections like charts or tables */}
      <div className="grid gap-4 md:gap-8 lg:grid-cols-2 xl:grid-cols-3">
         <Card className="xl:col-span-2">
           <CardHeader>
             <CardTitle>{t("overviewTitle")}</CardTitle>
           </CardHeader>
           <CardContent>
             {/* Placeholder for a chart component */}
             <div className="h-[300px] w-full bg-muted rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">{t("chartPlaceholder")}</p>
             </div>
           </CardContent>
         </Card>
         <Card>
           <CardHeader>
             <CardTitle>{t("recentActivityTitle")}</CardTitle>
             <CardDescription>{t("recentActivityDescription")}</CardDescription>
           </CardHeader>
           <CardContent className="grid gap-4">
              {/* Placeholder for recent activity list */}
              <div className="flex items-center gap-4">
                <div className="h-9 w-9 rounded-full bg-muted flex items-center justify-center">
                    <Users className="h-5 w-5 text-muted-foreground"/>
                </div>
                <div className="grid gap-1">
                    <p className="text-sm font-medium leading-none">{t("activity1Title")}</p>
                    <p className="text-sm text-muted-foreground">{t("activity1Description")}</p>
                </div>
                <div className="ml-auto font-medium">{t("activity1Value")}</div>
              </div>
              {/* Add more activity items */}
           </CardContent>
         </Card>
       </div>
    </div>
  );
}
