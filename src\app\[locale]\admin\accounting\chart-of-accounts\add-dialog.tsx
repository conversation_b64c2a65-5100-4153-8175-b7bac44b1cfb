"use client";

import { useState, useEffect } from "react"; // Import useEffect
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PlusCircle } from "lucide-react";
import { createChartOfAccount, getChartOfAccounts } from "@/lib/api/admin/accounting/chart-of-accounts";
import { CreateChartOfAccountInput, AccountCategoryEnum, createChartOfAccountSchema } from "@/lib/schemas/admin/accounting/chart-of-accounts";
import { ScrollArea } from "@/components/ui/scroll-area";

export function AddAccountDialog() {
    // Moved state and effect hooks to the top
    const [isOpen, setIsOpen] = useState(false);
    const [hasMounted, setHasMounted] = useState(false);

    useEffect(() => {
        setHasMounted(true);
    }, []);

    // Other hooks follow
    const t = useTranslations("AdminChartOfAccountsPage.AddDialog");
    const tValidation = useTranslations("AdminChartOfAccountsPage.AddDialog.validation");
    const tCategories = useTranslations("AdminChartOfAccountsPage.categories");
    const queryClient = useQueryClient();


    const formSchema = createChartOfAccountSchema(tValidation);
    const form = useForm<CreateChartOfAccountInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            accountNumber: "",
            nameEn: "",
            nameAr: "",
            descriptionEn: "",
            descriptionAr: "",
            category: undefined, // Start with no category selected
            parentAccountId: undefined,
        },
    });

    // Fetch potential parent accounts (optional, consider performance for large COA)
    // Fetch only a limited number or allow search if the list is too long
    const { data: parentAccountsData } = useQuery({
        queryKey: ["chart-of-accounts", { page: 0, size: 100, activeOnly: true }], // Fetch active accounts as potential parents
        queryFn: () => getChartOfAccounts({ page: 0, size: 100, activeOnly: true }),
        enabled: isOpen, // Only fetch when the dialog is open
    });

    const mutation = useMutation({
        mutationFn: createChartOfAccount,
        onSuccess: (data) => {
            toast.success(t("successToast", { accountNumber: data.accountNumber }));
            queryClient.invalidateQueries({ queryKey: ["chart-of-accounts"] }); // Invalidate cache to refetch list
            setIsOpen(false); // Close dialog on success
            form.reset(); // Reset form fields
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message }));
        },
    });

    const onSubmit = (values: CreateChartOfAccountInput) => {
        // Ensure empty string parentAccountId becomes null or undefined as expected by API
        const submissionValues = {
            ...values,
            parentAccountId: values.parentAccountId || undefined,
        };
        console.log("Submitting:", submissionValues);
        mutation.mutate(submissionValues);
    };

    // Prevent rendering the dialog until the component has mounted on the client
    if (!hasMounted) {
        // Render only the trigger button initially to match server render
        return (
            <Button onClick={() => setIsOpen(true)}>
                <PlusCircle className="mr-2 h-4 w-4" />
                {t("triggerButton")}
            </Button>
        );
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                {/* We render the button directly above if not mounted,
                    so just need the trigger wrapper here when mounted */}
                <Button>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t("triggerButton")}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description")}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <ScrollArea className="h-[50vh] pr-6"> {/* Make content scrollable */}
                            <div className="space-y-4 p-1">
                                <FormField
                                    control={form.control}
                                    name="accountNumber"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("accountNumberLabel")}</FormLabel>
                                            <FormControl>
                                                <Input placeholder={t("accountNumberPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <FormField
                                        control={form.control}
                                        name="nameEn"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("nameEnLabel")}</FormLabel>
                                                <FormControl>
                                                    <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="nameAr"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("nameArLabel")}</FormLabel>
                                                <FormControl>
                                                    <Input dir="rtl" placeholder={t("nameArPlaceholder")} {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>
                                <FormField
                                    control={form.control}
                                    name="category"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("categoryLabel")}</FormLabel>
                                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder={t("categoryPlaceholder")} />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {AccountCategoryEnum.options.map((category) => (
                                                        <SelectItem key={category} value={category}>
                                                            {tCategories(category as any)} {/* Use translation key */}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="parentAccountId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("parentAccountLabel")}</FormLabel>
                                            {/* Use field.value directly for defaultValue. Handle "__NONE__" in onValueChange */}
                                            <Select
                                                onValueChange={(value) => {
                                                    // Set to undefined if the special value is selected, otherwise use the actual value
                                                    field.onChange(value === "__NONE__" ? undefined : value);
                                                }}
                                                defaultValue={field.value ?? "__NONE__"} // Use "__NONE__" if value is null/undefined
                                            >
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder={t("parentAccountPlaceholder")} />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {/* Use a non-empty value for the placeholder item */}
                                                    <SelectItem value="__NONE__">{t("noParentValue")}</SelectItem>
                                                    {parentAccountsData?.content?.map((account) => (
                                                        <SelectItem key={account.id} value={account.id}>
                                                            {account.accountNumber} - {account.nameEn} / {account.nameAr}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormDescription>{t("parentAccountDescription")}</FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="descriptionEn"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                            <FormControl>
                                                <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="descriptionAr"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                            <FormControl>
                                                <Textarea dir="rtl" placeholder={t("descriptionArPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </ScrollArea>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={mutation.isPending}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
