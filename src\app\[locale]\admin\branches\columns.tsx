"use client";

import { ColumnDef } from "@tanstack/react-table";
import { useTranslations } from 'next-intl';
import { MoreH<PERSON>zontal, ArrowUpDown } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BranchDto } from "@/lib/dto/admin/branch.dto";
import { EditBranchDialog } from './edit-branch-dialog';
import { DeleteBranchDialog } from './delete-branch-dialog';

// Props interface to pass translation function and potentially callbacks
interface ColumnsProps {
    t: ReturnType<typeof useTranslations<'AdminBranchesPage.table'>>;
    onBranchUpdated?: () => void; // Optional: Callback after update
    onBranchDeleted?: () => void; // Optional: Callback after delete
}

export const getColumns = ({ t, onBranchUpdated, onBranchDeleted }: ColumnsProps): ColumnDef<BranchDto>[] => [
    {
        accessorKey: "nameEn",
        header: ({ column }) => (
            <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                {t('nameEn')}
                <ArrowUpDown className="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => <div className="capitalize">{row.getValue("nameEn")}</div>,
    },
    {
        accessorKey: "nameAr",
        header: ({ column }) => (
            <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                {t('nameAr')}
                <ArrowUpDown className="ml-2 h-4 w-4" />
            </Button>
        ),
        cell: ({ row }) => <div className="capitalize" dir="rtl">{row.getValue("nameAr")}</div>,
    },
    {
        accessorKey: "address",
        header: t('address'),
        cell: ({ row }) => <div>{row.getValue("address") || '---'}</div>, // Display placeholder if no address
    },
    {
        id: "actions",
        header: () => <div className="text-right">{t('actions')}</div>,
        enableHiding: false,
        cell: ({ row }) => {
            const branch = row.original;

            return (
                <div className="text-right">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">{t('actionsOpenMenu')}</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>{t('actions')}</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                             <EditBranchDialog branch={branch} onSuccess={onBranchUpdated}>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                    {t('actionsEdit')}
                                </DropdownMenuItem>
                            </EditBranchDialog>
                            <DeleteBranchDialog branch={branch} onSuccess={onBranchDeleted}>
                                <DropdownMenuItem
                                    onSelect={(e) => e.preventDefault()}
                                    className="text-destructive focus:text-destructive focus:bg-destructive/10"
                                >
                                    {t('actionsDelete')}
                                </DropdownMenuItem>
                            </DeleteBranchDialog>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            );
        },
    },
];
