"use client";

import { ColumnDef } from "@tanstack/react-table";
import { UseTranslations } from "next-intl"; // Import UseTranslations type
import { ChartOfAccountDto } from "@/lib/dto/admin/accounting/chart-of-accounts.dto";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions";

// Accept translation functions as arguments
export const getColumns = (
    t: UseTranslations<"AdminChartOfAccountsPage.table">,
    tShared: UseTranslations<"Shared">
): ColumnDef<ChartOfAccountDto>[] => {
    // Removed useTranslations calls from here

    return [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "accountNumber",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("accountNumber")} />
            ),
            cell: ({ row }) => <div className="w-[100px]">{row.getValue("accountNumber")}</div>,
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "nameEn",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("nameEn")} />
            ),
            cell: ({ row }) => row.getValue("nameEn"),
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "nameAr",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("nameAr")} />
            ),
            cell: ({ row }) => row.getValue("nameAr"),
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "category",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("category")} />
            ),
            cell: ({ row }) => {
                const category = row.getValue("category") as string;
                // Optionally map to translated category names if needed
                return <Badge variant="outline">{category}</Badge>;
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));
            },
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "active",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("status")} />
            ),
            cell: ({ row }) => {
                const isActive = row.getValue("active");
                return (
                    <Badge variant={isActive ? "success" : "destructive"}>
                        {isActive ? tShared("statusActive") : tShared("statusInactive")}
                    </Badge>
                );
            },
            filterFn: (row, id, value) => {
                // Handle boolean filtering
                const isActive = row.getValue(id);
                return value === 'active' ? isActive : !isActive;
            },
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "parentAccount.accountNumber", // Access nested property
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("parentAccount")} />
            ),
            cell: ({ row }) => row.original.parentAccount?.accountNumber || '---', // Display parent number or placeholder
            enableSorting: false, // Sorting might be complex on nested optional fields
            enableHiding: true,
        },
        {
            id: "actions",
            cell: ({ row }) => <DataTableRowActions row={row} />,
            enableSorting: false,
            enableHiding: false,
        },
    ];
};
