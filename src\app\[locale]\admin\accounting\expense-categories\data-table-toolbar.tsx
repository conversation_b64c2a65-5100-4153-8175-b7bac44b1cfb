"use client";

import { Table } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import React from "react"; // Import React for useState
import { Button } from "@/components/ui/button";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options";
import { X } from "lucide-react";
import { AddEditExpenseCategorySheet } from "./add-edit-expense-category-sheet"; // Use the new sheet component

interface DataTableToolbarProps<TData> {
    table: Table<TData>;
    onCategoryAdded?: () => void; // Optional callback
    globalFilter: string;
    setGlobalFilter: (value: string) => void;
}

export function DataTableToolbar<TData>({
    table,
    globalFilter,
    setGlobalFilter,
    onCategoryAdded,
}: DataTableToolbarProps<TData>) {
    const t = useTranslations("AdminExpenseCategoriesPage");
    const isFiltered = globalFilter !== '' || table.getState().columnFilters.length > 0;
    const [isSheetOpen, setIsSheetOpen] = React.useState(false);

    const handleAddSuccess = () => {
        setIsSheetOpen(false); // Close sheet on success
        if (onCategoryAdded) {
            onCategoryAdded(); // Call optional callback if provided
        }
    };

    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
                <Input
                    placeholder={t("searchPlaceholder")}
                    value={globalFilter ?? ""}
                    onChange={(event) => setGlobalFilter(event.target.value)}
                    className="h-8 w-[150px] lg:w-[250px]"
                />
                {/* Add column-specific filters here if needed */}
                {isFiltered && (
                    <Button
                        variant="ghost"
                        onClick={() => {
                            setGlobalFilter('');
                            table.resetColumnFilters();
                        }}
                        className="h-8 px-2 lg:px-3"
                    >
                        {t("resetFilters")}
                        <X className="ml-2 h-4 w-4" />
                    </Button>
                )}
            </div>
            <div className="flex items-center space-x-2">
                <DataTableViewOptions table={table} />
                <AddEditExpenseCategorySheet
                    isOpen={isSheetOpen}
                    onOpenChange={setIsSheetOpen}
                    onSuccess={handleAddSuccess} // Pass success handler
                >
                    <Button size="sm" className="ml-auto h-8 lg:flex">
                        {t("addExpenseCategoryButton")}
                    </Button>
                </AddEditExpenseCategorySheet>
            </div>
        </div>
    );
}
