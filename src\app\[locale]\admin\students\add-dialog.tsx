"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { IdType } from "@/lib/dto/admin/student.dto";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { createStudentProfile } from "@/lib/api/admin/students";
import { format } from "date-fns";
import { getAllEducationalStagesList } from "@/lib/api/admin/educational-stages";
import { getGradeLevelsByStage } from "@/lib/api/admin/grade-levels";
import { getGuardians } from "@/lib/api/admin/guardians";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

interface AddStudentDialogProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  onStudentAdded: () => void;
}

export function AddStudentDialog({
  isOpen,
  setIsOpen,
  onStudentAdded,
}: AddStudentDialogProps) {
  const t = useTranslations("StudentsPage.addDialog");
  const queryClient = useQueryClient();
  const [selectedStageId, setSelectedStageId] = useState<string | null>(null);

  // Create form schema
  const formSchema = z.object({
    firstName: z.string().min(2, t("errors.firstNameRequired")).max(50, t("errors.firstNameTooLong")),
    lastName: z.string().min(2, t("errors.lastNameRequired")).max(50, t("errors.lastNameTooLong")),
    email: z.string().email(t("errors.invalidEmail")).max(100, t("errors.emailTooLong")),
    password: z.string().min(8, t("errors.passwordTooShort")).max(100, t("errors.passwordTooLong")),
    phoneNumber: z.string().max(20, t("errors.phoneNumberTooLong")).optional(),
    dateOfBirth: z.date({
      required_error: t("errors.dateOfBirthRequired"),
    }),
    gender: z.enum(["MALE", "FEMALE"], {
      required_error: t("errors.genderRequired"),
    }),
    address: z.string().max(255, t("errors.addressTooLong")).optional(),
    nationalId: z.string().min(1, t("errors.nationalIdRequired")).max(50, t("errors.nationalIdTooLong")),
    idType: z.enum(["NATIONAL_ID", "PASSPORT"] as const),
    guardianType: z.enum(["FATHER", "MOTHER", "BROTHER", "SISTER", "UNCLE", "AUNT", "GRANDFATHER", "GRANDMOTHER", "OTHER", "SELF"], {
      required_error: t("errors.guardianTypeRequired"),
    }),
    guardianId: z.string().uuid(t("errors.invalidGuardian")).optional(),
    gradeLevelId: z.string().uuid(t("errors.invalidGradeLevel")).optional(),
    branchId: z.string().uuid(t("errors.invalidBranch")).optional(),
  });

  type FormValues = z.infer<typeof formSchema>;

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      phoneNumber: "",
      gender: "MALE",
      address: "",
      nationalId: "",
      idType: "NATIONAL_ID",
      guardianType: "SELF", // Default to SELF
      guardianId: undefined,
      gradeLevelId: undefined,
      branchId: undefined,
    },
  });

  // Fetch educational stages
  const { data: stagesData } = useQuery({
    queryKey: ["educational-stages"],
    queryFn: getAllEducationalStagesList,
    enabled: isOpen,
  });

  // Fetch grade levels based on selected stage
  const { data: gradeLevelsData } = useQuery({
    queryKey: ["grade-levels", selectedStageId],
    queryFn: () => getGradeLevelsByStage({ stageId: selectedStageId! }),
    enabled: !!selectedStageId && isOpen,
  });

  // Fetch guardians for dropdown
  const { data: guardiansData } = useQuery({
    queryKey: ["guardians-for-student-creation"],
    queryFn: () => getGuardians(),
    enabled: isOpen,
  });

  // Create student mutation
  const mutation = useMutation({
    mutationFn: (data: any) => {
      return createStudentProfile(data);
    },
    onSuccess: () => {
      toast.success(t("successToast"));
      queryClient.invalidateQueries({ queryKey: ["students"] });
      setIsOpen(false);
      form.reset();
      onStudentAdded();
    },
    onError: (error) => {
      toast.error(t("errorToast", { error: error instanceof Error ? error.message : String(error) }));
    },
  });

  // Form submission handler
  const onSubmit = (values: FormValues) => {
    // Format date to YYYY-MM-DD string before sending
    const formattedData = {
      ...values,
      dateOfBirth: values.dateOfBirth.toISOString().split('T')[0], // Format to YYYY-MM-DD
    };

    // If guardianType is SELF, remove guardianId
    if (formattedData.guardianType === "SELF") {
      formattedData.guardianId = undefined;
    }

    mutation.mutate(formattedData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Personal Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">{t("personalInfo")}</h3>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("firstName")}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("lastName")}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("email")}</FormLabel>
                      <FormControl>
                        <Input type="email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("password")}</FormLabel>
                      <FormControl>
                        <Input type="password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("phoneNumber")}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("gender")}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("selectGender")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="MALE">{t("genders.male")}</SelectItem>
                          <SelectItem value="FEMALE">{t("genders.female")}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="dateOfBirth"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>{t("dateOfBirth")}</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>{t("selectDate")}</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("address")}</FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* ID Information Section */}
            <div className="space-y-4 pt-4">
              <h3 className="text-lg font-medium">{t("idInfo")}</h3>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="nationalId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("nationalId")}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="idType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("idType")}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("selectIdType")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="NATIONAL_ID">{t("idTypes.national_id")}</SelectItem>
                          <SelectItem value="PASSPORT">{t("idTypes.passport")}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* School Information Section */}
            <div className="space-y-4 pt-4">
              <h3 className="text-lg font-medium">{t("schoolInfo")}</h3>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <FormLabel>{t("educationalStage")}</FormLabel>
                  <Select
                    onValueChange={(value) => setSelectedStageId(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectStage")} />
                    </SelectTrigger>
                    <SelectContent>
                      {stagesData?.map((stage) => (
                        <SelectItem key={stage.id} value={stage.id}>
                          {stage.nameEn}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <FormField
                  control={form.control}
                  name="gradeLevelId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("gradeLevel")}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={!selectedStageId}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("selectGradeLevel")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {gradeLevelsData?.map((grade) => (
                            <SelectItem key={grade.id} value={grade.id}>
                              {grade.nameEn}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="guardianType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("guardianType")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("selectGuardianType")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="FATHER">{t("guardianTypes.father")}</SelectItem>
                        <SelectItem value="MOTHER">{t("guardianTypes.mother")}</SelectItem>
                        <SelectItem value="BROTHER">{t("guardianTypes.brother")}</SelectItem>
                        <SelectItem value="SISTER">{t("guardianTypes.sister")}</SelectItem>
                        <SelectItem value="UNCLE">{t("guardianTypes.uncle")}</SelectItem>
                        <SelectItem value="AUNT">{t("guardianTypes.aunt")}</SelectItem>
                        <SelectItem value="GRANDFATHER">{t("guardianTypes.grandfather")}</SelectItem>
                        <SelectItem value="GRANDMOTHER">{t("guardianTypes.grandmother")}</SelectItem>
                        <SelectItem value="OTHER">{t("guardianTypes.other")}</SelectItem>
                        <SelectItem value="SELF">{t("guardianTypes.self")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="guardianId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("guardian")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={form.watch("guardianType") === "SELF"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("selectGuardian")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* Don't use an empty string as a value */}
                        {guardiansData?.map((guardian) => (
                          <SelectItem key={guardian.id} value={guardian.id}>
                            {guardian.userAccount.firstName} {guardian.userAccount.lastName} ({guardian.userAccount.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                    <p className="text-sm text-muted-foreground mt-1">{t("guardianOptional")}</p>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
              >
                {t("cancel")}
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? t("creating") : t("create")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
