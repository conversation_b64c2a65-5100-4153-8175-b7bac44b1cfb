import Image from 'next/image';
import React from 'react';

interface LogoProps {
  src?: string;
  alt?: string;
  width?: number;
  height?: number;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({
  src = '/images/logo_icon.png',
  alt = 'Logo',
  width = 100,
  height = 40,
  className,
}) => {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
    />
  );
};

export default Logo;
