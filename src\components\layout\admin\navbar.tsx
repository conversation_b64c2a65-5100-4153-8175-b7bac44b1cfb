"use client"; // Needed for Sheet (mobile menu) and DropdownMenu (user menu)

import { Circle<PERSON>ser, Menu, PanelLeftClose, PanelLeftOpen } from "lucide-react"; // Removed unused icons
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

import { Button } from "@/components/ui/button";
import { LanguageSwitcher } from "@/components/common/language-switcher";
import { Sidebar } from "./sidebar"; // Import Sidebar
import { ThemeToggleButton } from "@/components/common/theme-toggle-button"; // Import the theme toggle button
import { toast } from "sonner"; // For showing toast notifications
import { useAuthStore } from "@/stores/auth"; // Import auth store for logout
import { useParams } from "next/navigation"; // For getting current locale
import { useRouter } from "@/i18n/navigation"; // Use i18n router for proper locale handling
import { useTranslations } from "next-intl";
import { useUiStore } from "@/stores/ui"; // Import the UI store
import { redirectToLogin } from "@/lib/auth-helpers"; // Import auth helpers

export function Navbar() {
  const t = useTranslations("AdminLayout.Navbar");
  // Removed tSidebar as it's now handled within the Sidebar component
  const { toggleSidebar, isSidebarOpen } = useUiStore(); // Get state and action from store
  const logout = useAuthStore((state) => state.logout); // Get logout function from auth store
  const router = useRouter(); // For navigation after logout
  const params = useParams(); // To get current locale
  const locale = params.locale as string; // Extract locale from params

  // Handle logout action
  const handleLogout = () => {
    // Show success message first
    toast.success(t("logoutSuccess") || "Logged out successfully");

    // Clear auth state (this will trigger a redirect in the logout function)
    logout();

    // As a fallback, also redirect using our helper
    setTimeout(() => {
      redirectToLogin(locale);
    }, 100);
  };

  // Removed navItems as we are now using the full Sidebar component

  return (
    // Add border-b and shadow-sm for visual separation
    <header className="sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4 shadow-sm sm:px-6 lg:h-[60px]">
      {/* Sidebar Toggle Button (Visible on lg screens) */}
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleSidebar}
        className="hidden lg:inline-flex" // Only show on large screens
      >
        {isSidebarOpen ? <PanelLeftClose className="h-5 w-5" /> : <PanelLeftOpen className="h-5 w-5" />}
        <span className="sr-only">{t("toggleSidebar")}</span>
      </Button>

      {/* Mobile Menu Trigger (Visible on screens smaller than lg) */}
      <Sheet>
        <SheetTrigger asChild>
          {/* Use ghost variant for less visual weight */}
          <Button size="icon" variant="ghost" className="lg:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">{t("toggleMenu")}</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="sm:max-w-xs p-0"> {/* Added p-0 to SheetContent */}
          {/* Render the full Sidebar component for mobile */}
          <Sidebar variant="mobile" />
        </SheetContent>
      </Sheet>

      {/* Optional: Breadcrumbs or Page Title Area */}
      <div className="flex-1">
        {/* <h1 className="text-lg font-semibold">Dashboard</h1> */}
      </div>

      {/* Optional: Search Bar */}
      {/* <div className="relative ml-auto flex-1 md:grow-0">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder={t('searchPlaceholder')}
          className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[320px]"
        />
      </div> */}

      <div className="ml-auto flex items-center gap-2"> {/* Group buttons */}
        <LanguageSwitcher />
        <ThemeToggleButton /> {/* Add the theme toggle button */}
      </div>

      {/* User Menu */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          {/* Use ghost variant for user icon button */}
          <Button variant="ghost" size="icon" className="rounded-full">
            <CircleUser className="h-5 w-5" />
            <span className="sr-only">{t("toggleUserMenu")}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>{t("myAccount")}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem>{t("settings")}</DropdownMenuItem>
          <DropdownMenuItem>{t("support")}</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout}>{t("logout")}</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </header>
  );
}
