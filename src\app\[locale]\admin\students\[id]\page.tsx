"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { getStudentById } from "@/lib/api/admin/students";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Edit, UserCog } from "lucide-react";
import Link from "next/link";

export default function StudentDetailPage() {
  const t = useTranslations("StudentDetailPage");
  const params = useParams();
  const router = useRouter();
  const studentId = params.id as string;

  const { data: student, isLoading, isError, error } = useQuery({
    queryKey: ["student", studentId],
    queryFn: () => getStudentById(studentId),
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/4 mb-2" />
            <Skeleton className="h-4 w-1/3" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <Terminal className="h-4 w-4" />
        <AlertTitle>{t("errorTitle")}</AlertTitle>
        <AlertDescription>
          {error instanceof Error ? error.message : t("unknownError")}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">
            {student.userAccount.firstName} {student.userAccount.lastName}
          </h1>
        </div>
        <div className="flex space-x-2">
          <Button asChild variant="outline">
            <Link href={`/admin/students/${studentId}/guardians`}>
              <UserCog className="mr-2 h-4 w-4" />
              {t("manageGuardians")}
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/admin/students/${studentId}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              {t("editStudent")}
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{t("personalInfo")}</CardTitle>
            <CardDescription>{t("personalInfoDesc")}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("firstName")}</p>
                <p>{student.userAccount.firstName}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("lastName")}</p>
                <p>{student.userAccount.lastName}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("email")}</p>
                <p>{student.userAccount.email}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("phone")}</p>
                <p>{student.userAccount.phoneNumber || t("notProvided")}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("admissionNumber")}</p>
                <p>{student.admissionNumber}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("nationalId")}</p>
                <p>
                  {student.nationalId}{" "}
                  <Badge variant="outline" className="ml-2">
                    {t(`idTypes.${student.idType.toLowerCase()}`)}
                  </Badge>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("academicInfo")}</CardTitle>
            <CardDescription>{t("academicInfoDesc")}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("branch")}</p>
                <p>{student.branch ? student.branch.nameEn : t("notAssigned")}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t("gradeLevel")}</p>
                <p>{student.gradeLevel ? student.gradeLevel.nameEn : t("notAssigned")}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>{t("guardians")}</CardTitle>
            <CardDescription>{t("guardiansDesc")}</CardDescription>
          </CardHeader>
          <CardContent>
            {student.guardians.length > 0 ? (
              <div className="space-y-4">
                {student.guardians.map((guardian, index) => (
                  <div key={guardian.id}>
                    {index > 0 && <Separator className="my-4" />}
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">{t("name")}</p>
                        <p>
                          {guardian.firstName} {guardian.lastName}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">{t("email")}</p>
                        <p>{guardian.email}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">{t("phone")}</p>
                        <p>{guardian.phoneNumber || t("notProvided")}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">{t("occupation")}</p>
                        <p>{guardian.occupation}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">{t("nationalId")}</p>
                        <p>
                          {guardian.nationalId}{" "}
                          <Badge variant="outline" className="ml-2">
                            {t(`idTypes.${guardian.idType.toLowerCase()}`)}
                          </Badge>
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">{t("noGuardians")}</p>
            )}
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline">
              <Link href={`/admin/students/${studentId}/guardians`}>
                <UserCog className="mr-2 h-4 w-4" />
                {t("manageGuardians")}
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
