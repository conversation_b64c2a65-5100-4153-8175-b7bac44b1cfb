import { GradeLevelDto } from './grade-level.dto'; // Assuming GradeLevelDto exists
import { SimpleBranchDto } from './branch.dto'; // Assuming SimpleBranchDto exists

export interface EducationalStageDto {
    id: string;
    nameEn: string;
    nameAr: string;
    sortOrder: number;
    gradeLevels?: GradeLevelDto[]; // Optional based on API response structure
    branches?: SimpleBranchDto[]; // Optional based on API response structure
    createdAt: string; // ISO DateTime string
    updatedAt: string; // ISO DateTime string
}

export interface CreateEducationalStageRequest {
    nameEn: string;
    nameAr: string;
    sortOrder: number;
}

export interface UpdateEducationalStageRequest {
    nameEn?: string;
    nameAr?: string;
    sortOrder?: number;
}

// No Page DTO needed as the API returns a simple array
// export type PageEducationalStageDto = Page<EducationalStageDto>;

// No specific params DTO needed for getAll as it takes no parameters
// export interface GetEducationalStagesParams { ... }
