import { z } from "zod";
import type { UseTranslations } from "next-intl";

// Define a type for the translation function based on the expected keys
// This improves type safety when calling t()
type LoginTranslations = UseTranslations<
  | "invalidEmailError"
  | "passwordRequiredError"
>;

export const getLoginSchema = (t: LoginTranslations) =>
  z.object({
    email: z.string().email({ message: t("invalidEmailError") }),
    password: z.string().min(1, { message: t("passwordRequiredError") }), // API spec doesn't specify min length for login, just for reset/register
  });

export type LoginSchema = z.infer<ReturnType<typeof getLoginSchema>>;
