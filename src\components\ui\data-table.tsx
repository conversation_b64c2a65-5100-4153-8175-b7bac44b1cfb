"use client";

import * as React from "react";
import {
  type ColumnDef,
  type Table, // Import Table type
  flexRender,
} from "@tanstack/react-table";

import {
  Table as ShadcnTable, // Rename to avoid conflict with @tanstack/react-table Table type
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface DataTableProps<TData> { // Update props to accept table instance
  table: Table<TData>;
  columns: ColumnDef<TData, any>[]; // Keep columns prop for rendering
}

export function DataTable<TData>({
  table,
  columns, // Keep columns prop for rendering
}: DataTableProps<TData>) {

  return (
    <div className="rounded-md border">
      <ShadcnTable>{/* Use renamed ShadcnTable */}
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id} colSpan={header.colSpan}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(
                      cell.column.columnDef.cell,
                      cell.getContext()
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={columns.length}
                className="h-24 text-center"
              >
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </ShadcnTable>{/* Use renamed ShadcnTable */}
    </div>
  );
}
