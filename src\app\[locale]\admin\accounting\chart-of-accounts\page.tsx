// Removed getColumns import
import { ChartOfAccountsDataTable } from "./data-table";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default async function ChartOfAccountsPage() {
    const t = await getTranslations("AdminChartOfAccountsPage");
    // Removed columns definition here

    return (
        <Card>
            <CardHeader>
                <CardTitle>{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent>
                <ChartOfAccountsDataTable />
            </CardContent>
        </Card>
    );
}
