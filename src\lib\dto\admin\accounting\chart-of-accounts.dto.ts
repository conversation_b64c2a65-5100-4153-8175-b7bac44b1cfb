import { PageableObject, SortObject } from "@/lib/dto/common.dto";

// Based on #/components/schemas/ChartOfAccountDto
export interface ChartOfAccountDto {
    id: string;
    accountNumber: string;
    nameEn: string;
    nameAr: string;
    descriptionEn?: string;
    descriptionAr?: string;
    category: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'EXPENSE';
    active: boolean;
    parentAccount?: SimpleChartOfAccountDto;
    createdDate?: string; // Assuming ISO date string
    lastModifiedDate?: string; // Assuming ISO date string
}

// Based on #/components/schemas/SimpleChartOfAccountDto
export interface SimpleChartOfAccountDto {
    id: string;
    accountNumber: string;
    nameEn: string;
    nameAr: string;
}

// Based on #/components/schemas/CreateChartOfAccountRequest
export interface CreateChartOfAccountRequest {
    accountNumber: string;
    nameEn: string;
    nameAr: string;
    descriptionEn?: string;
    descriptionAr?: string;
    category: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'EXPENSE';
    parentAccountId?: string; // UUID
}

// Based on #/components/schemas/UpdateChartOfAccountRequest
export interface UpdateChartOfAccountRequest {
    nameEn?: string|null;
    nameAr?: string|null;
    descriptionEn?: string|null;
    descriptionAr?: string|null;
    category?: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'EXPENSE';
    active?: boolean;
    parentAccountId?: string|null; // UUID
}

// Based on #/components/schemas/PageChartOfAccountDto
export interface PageChartOfAccountDto {
    totalElements: number;
    totalPages: number;
    size: number;
    content: ChartOfAccountDto[];
    number: number;
    sort: SortObject[];
    numberOfElements: number;
    pageable: PageableObject;
    first: boolean;
    last: boolean;
    empty: boolean;
}

// Params for fetching data
export interface GetChartOfAccountsParams {
    page?: number;
    size?: number;
    sort?: string[]; // e.g., ["accountNumber,asc", "nameEn,desc"]
    accountNumber?: string;
    nameEn?: string;
    nameAr?: string;
    category?: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'EXPENSE'; // Add category filter
    activeOnly?: boolean; // Custom param to control fetching active/all
}
