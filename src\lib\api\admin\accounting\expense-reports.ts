import { API_BASE_URL } from "@/lib/constants";
import { ExpenseReportDto } from "@/lib/dto/admin/accounting/expense-reports.dto";
import { ErrorResponse } from "@/lib/dto/error-response.dto";
import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { useQuery } from "@tanstack/react-query";

const EXPENSE_REPORTS_API_PATH = `${API_BASE_URL}/accounting/reports/expenses`;

/**
 * Fetches the expense report for a given date range
 */
export async function getExpenseReport(
  startDate?: string,
  endDate?: string
): Promise<ExpenseReportDto> {
  const params = new URLSearchParams();
  if (startDate) {
    params.append("startDate", startDate);
  }
  if (endDate) {
    params.append("endDate", endDate);
  }

  const url = `${EXPENSE_REPORTS_API_PATH}${params.toString() ? `?${params.toString()}` : ""}`;

  const response = await fetchWithAuth(url, {
    method: "GET",
  });

  const data = await response.json();

  if (!response.ok) {
    // Throw the error response directly
    throw data as ErrorResponse;
  }

  return data as ExpenseReportDto;
}

/**
 * React Query hook for fetching expense report
 */
export function useExpenseReport(startDate?: string, endDate?: string) {
  return useQuery<ExpenseReportDto, ErrorResponse>({
    queryKey: ["expenseReport", startDate, endDate],
    queryFn: async () => {
      return await getExpenseReport(startDate, endDate);
    },
    enabled: !!startDate && !!endDate, // Only fetch when both dates are provided
  });
}
