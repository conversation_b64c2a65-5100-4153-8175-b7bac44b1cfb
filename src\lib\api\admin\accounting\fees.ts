import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { API_BASE_URL } from "@/lib/constants";
import {
    FeeDto,
    CreateFeeRequest,
    UpdateFeeRequest,
    PageFeeDto,
    GetFeesParams
} from "@/lib/dto/admin/accounting/fees.dto";
import { handleApiError } from "@/lib/api-error-handler";

const FEES_API_PATH = "/accounting/fees";

/**
 * Fetches a paginated list of fees based on provided parameters.
 */
export async function getFees(params: GetFeesParams): Promise<PageFeeDto> {
    const { activeOnly, ...apiParams } = params; // Separate client-side flag if used
    const url = new URL(`${API_BASE_URL}${FEES_API_PATH}`);

    // Append query parameters from apiParams
    Object.entries(apiParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
                value.forEach(val => url.searchParams.append(key, val));
            } else {
                url.searchParams.append(key, String(value));
            }
        }
    });

    console.log("Fetching fees from:", url.toString());

    try {
        const response = await fetchWithAuth(url.toString());
        if (!response.ok) {
            await handleApiError(response, 'Failed to fetch fees');
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed (e.g., using Zod)
        // Client-side filtering for activeOnly if needed
        // if (activeOnly && data && (data as PageFeeDto).content) {
        //     (data as PageFeeDto).content = (data as PageFeeDto).content.filter(fee => fee.active);
        // }
        return data as PageFeeDto;
    } catch (error) {
        console.error("Error fetching fees:", error);
        throw error; // Re-throw the error to be handled by the caller
    }
}

/**
 * Fetches a single fee by its ID.
 */
export async function getFeeById(id: string): Promise<FeeDto> {
    const url = `${API_BASE_URL}${FEES_API_PATH}/${id}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            await handleApiError(response, `Failed to fetch fee ${id}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed
        return data as FeeDto;
    } catch (error) {
        console.error(`Error fetching fee ${id}:`, error);
        throw error;
    }
}

/**
 * Creates a new fee.
 */
export async function createFee(request: CreateFeeRequest): Promise<FeeDto> {
    const url = `${API_BASE_URL}${FEES_API_PATH}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request),
        });

        if (!response.ok) { // API Spec says 201 Created
             if (response.status !== 201) {
                await handleApiError(response, 'Failed to create fee');
             }
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed
        return data as FeeDto;
    } catch (error) {
        console.error("Error creating fee:", error);
        throw error;
    }
}

/**
 * Updates an existing fee.
 */
export async function updateFee(id: string, request: UpdateFeeRequest): Promise<FeeDto> {
    const url = `${API_BASE_URL}${FEES_API_PATH}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request),
        });

        if (!response.ok) {
            await handleApiError(response, `Failed to update fee ${id}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed
        return data as FeeDto;
    } catch (error) {
        console.error(`Error updating fee ${id}:`, error);
        throw error;
    }
}

/**
 * Deletes a fee by its ID.
 */
export async function deleteFee(id: string): Promise<void> {
    const url = `${API_BASE_URL}${FEES_API_PATH}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'DELETE',
        });

        if (!response.ok && response.status !== 204) { // 204 No Content is success
            await handleApiError(response, `Failed to delete fee ${id}`);
        }
        // No content expected on successful delete
    } catch (error) {
        console.error(`Error deleting fee ${id}:`, error);
        throw error;
    }
}

/**
 * Activates a fee.
 */
export async function activateFee(id: string): Promise<FeeDto> {
    const url = `${API_BASE_URL}${FEES_API_PATH}/${id}/activate`;
    try {
        const response = await fetchWithAuth(url, { method: 'PUT' }); // API uses PUT for activation
        if (!response.ok) {
            await handleApiError(response, `Failed to activate fee ${id}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed
        return data as FeeDto;
    } catch (error) {
        console.error(`Error activating fee ${id}:`, error);
        throw error;
    }
}

/**
 * Deactivates a fee.
 */
export async function deactivateFee(id: string): Promise<FeeDto> {
    const url = `${API_BASE_URL}${FEES_API_PATH}/${id}/deactivate`;
    try {
        const response = await fetchWithAuth(url, { method: 'PUT' }); // API uses PUT for deactivation
        if (!response.ok) {
            await handleApiError(response, `Failed to deactivate fee ${id}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed
        return data as FeeDto;
    } catch (error) {
        console.error(`Error deactivating fee ${id}:`, error);
        throw error;
    }
}
