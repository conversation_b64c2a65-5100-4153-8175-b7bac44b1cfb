// import {createNavigation} from 'next-intl/navigation';
import {defineRouting} from 'next-intl/routing';

export const locales =  ['ar', 'en']

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['en', 'ar'],

  // Used when no locale matches
  defaultLocale: 'en'
});

// export const routing = defineRouting({
//   // A list of all locales that are supported
//   locales,

//   // Used when no locale matches
//   defaultLocale: 'en',
// localePrefix: 'always',
// });

// // Lightweight wrappers around Next.js' navigation APIs
// export const {Link, redirect, usePathname, useRouter} =
//   createNavigation( routing );
