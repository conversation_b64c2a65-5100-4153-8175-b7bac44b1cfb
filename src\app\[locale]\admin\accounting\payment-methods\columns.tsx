"use client";

import { ColumnDef } from "@tanstack/react-table";
import { UseTranslations } from "next-intl";
import { PaymentMethodDto } from "@/lib/dto/admin/accounting/payment-methods.dto"; // Import correct DTO
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions"; // Import correct actions

// Accept translation functions as arguments
export const getColumns = (
    t: UseTranslations<"AdminPaymentMethodsPage.table">, // Use correct translation scope
    tShared: UseTranslations<"Shared">,
    tTypes: UseTranslations<"AdminPaymentMethodsPage.types"> // Add types translations
): ColumnDef<PaymentMethodDto>[] => {

    return [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "nameEn",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("nameEn")} />
            ),
            cell: ({ row }) => row.getValue("nameEn"),
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "nameAr",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("nameAr")} />
            ),
            cell: ({ row }) => row.getValue("nameAr"),
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "type",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("type")} />
            ),
            cell: ({ row }) => {
                const type = row.getValue("type") as string;
                // Use translation key for type
                return <Badge variant="secondary">{tTypes(type as any)}</Badge>;
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));
            },
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "active",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("status")} />
            ),
            cell: ({ row }) => {
                const isActive = row.getValue("active");
                return (
                    <Badge variant={isActive ? "success" : "destructive"}>
                        {isActive ? tShared("statusActive") : tShared("statusInactive")}
                    </Badge>
                );
            },
            filterFn: (row, id, value) => {
                const isActive = row.getValue(id);
                return value === 'active' ? isActive : !isActive;
            },
            enableSorting: true,
            enableHiding: true,
        },
        {
            id: "actions",
            cell: ({ row }) => <DataTableRowActions row={row} />, // Use correct actions component
            enableSorting: false,
            enableHiding: false,
        },
    ];
};
