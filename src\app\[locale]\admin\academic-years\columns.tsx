"use client";

import { ColumnDef } from "@tanstack/react-table";
import { UseTranslations } from "next-intl";
import { AcademicYearDto } from "@/lib/dto/admin/academic-year.dto";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions";
import { Badge } from "@/components/ui/badge";
import { format } from 'date-fns'; // For formatting dates

// Accept translation functions as arguments
export const getColumns = (
    t: UseTranslations<"AdminAcademicYearsPage.table">,
    tShared: UseTranslations<"Shared">
): ColumnDef<AcademicYearDto>[] => {

    return [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("name")} />
            ),
            cell: ({ row }) => row.getValue("name"),
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "startDate",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("startDate")} />
            ),
            cell: ({ row }) => {
                const dateString = row.getValue("startDate") as string | null;
                try {
                    // Add 'T00:00:00' to parse as local date, avoiding timezone issues
                    return dateString ? format(new Date(dateString + 'T00:00:00'), 'PPP') : '---';
                } catch {
                    return dateString ?? '---'; // Fallback if date is invalid
                }
            },
            enableSorting: true, // Keep sorting enabled
            enableHiding: true,
        },
        {
            accessorKey: "endDate",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("endDate")} />
            ),
            cell: ({ row }) => {
                 const dateString = row.getValue("endDate") as string | null;
                try {
                    // Add 'T00:00:00' to parse as local date
                    return dateString ? format(new Date(dateString + 'T00:00:00'), 'PPP') : '---';
                } catch {
                    return dateString ?? '---'; // Fallback if date is invalid
                }
            },
            enableSorting: true, // Keep sorting enabled
            enableHiding: true,
        },
        {
            accessorKey: "active",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("status")} />
            ),
            cell: ({ row }) => {
                const isActive = row.getValue("active");
                return (
                    <Badge variant={isActive ? "success" : "outline"}>
                        {isActive ? tShared("statusActive") : tShared("statusInactive")}
                    </Badge>
                );
            },
            filterFn: (row, id, value) => {
                const isActive = row.getValue(id);
                return value === 'active' ? isActive : !isActive;
            },
            enableSorting: true,
            enableHiding: true,
        },
        {
            id: "actions",
            cell: ({ row }) => <DataTableRowActions row={row} />,
            enableSorting: false,
            enableHiding: false,
        },
    ];
};
