import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { API_BASE_URL } from "@/lib/constants";
import {
    ChartOfAccountDto,
    CreateChartOfAccountRequest,
    GetChartOfAccountsParams,
    PageChartOfAccountDto,
    UpdateChartOfAccountRequest
} from "@/lib/dto/admin/accounting/chart-of-accounts.dto";

const CHART_OF_ACCOUNTS_API_PATH = "/accounting/chart-of-accounts";

/**
 * Fetches a paginated list of chart of accounts based on provided parameters.
 * Handles fetching either all accounts or only active ones based on `activeOnly`.
 */
export async function getChartOfAccounts(params: GetChartOfAccountsParams): Promise<PageChartOfAccountDto> {
    const { activeOnly, ...apiParams } = params;
    const endpoint = activeOnly ? `${CHART_OF_ACCOUNTS_API_PATH}/active` : CHART_OF_ACCOUNTS_API_PATH;
    const url = new URL(`${API_BASE_URL}${endpoint}`);

    // Append query parameters
    Object.entries(apiParams).forEach(([key, value]) => {
        // Also check for empty strings, especially for search params
        if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
                value.forEach(val => url.searchParams.append(key, val));
            } else {
                url.searchParams.append(key, String(value));
            }
        }
    });

    try {
        const response = await fetchWithAuth(url.toString());
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch chart of accounts: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed (e.g., using Zod)
        return data as PageChartOfAccountDto;
    } catch (error) {
        console.error("Error fetching chart of accounts:", error);
        throw error; // Re-throw the error to be handled by the caller (e.g., react-query)
    }
}

/**
 * Creates a new chart of account.
 */
export async function createChartOfAccount(request: CreateChartOfAccountRequest): Promise<ChartOfAccountDto> {
    const url = `${API_BASE_URL}${CHART_OF_ACCOUNTS_API_PATH}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(request),
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Create Chart of Account Error Data:", errorData); // Log detailed error
            throw new Error(errorData?.message || `Failed to create chart of account: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed
        return data as ChartOfAccountDto;
    } catch (error) {
        console.error("Error creating chart of account:", error);
        throw error;
    }
}

/**
 * Fetches a single chart of account by its ID.
 */
export async function getChartOfAccountById(id: string): Promise<ChartOfAccountDto> {
    const url = `${API_BASE_URL}${CHART_OF_ACCOUNTS_API_PATH}/${id}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch chart of account ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed
        return data as ChartOfAccountDto;
    } catch (error) {
        console.error(`Error fetching chart of account ${id}:`, error);
        throw error;
    }
}

/**
 * Updates an existing chart of account.
 */
export async function updateChartOfAccount(id: string, request: UpdateChartOfAccountRequest): Promise<ChartOfAccountDto> {
    const url = `${API_BASE_URL}${CHART_OF_ACCOUNTS_API_PATH}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(request),
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Update Chart of Account Error Data:", errorData); // Log detailed error
            throw new Error(errorData?.message || `Failed to update chart of account ${id}: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add robust validation here if needed
        return data as ChartOfAccountDto;
    } catch (error) {
        console.error(`Error updating chart of account ${id}:`, error);
        throw error;
    }
}


/**
 * Fetches a simple list of all *active* chart of accounts (non-paginated), optionally filtered by category.
 * Ideal for populating dropdowns/comboboxes.
 */
export async function findActiveChartOfAccountsSimple(
    params?: Pick<GetChartOfAccountsParams, 'category'> // Accept optional category filter
): Promise<SimpleChartOfAccountDto[]> {
    const queryParams = new URLSearchParams({ size: '2000' }); // Fetch a large number to simulate no pagination
    if (params?.category) {
        queryParams.append('category', params.category);
    }

    const url = `${API_BASE_URL}${CHART_OF_ACCOUNTS_API_PATH}/active?${queryParams.toString()}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch active chart of accounts: ${response.statusText}`);
        }
        const pageData: unknown = await response.json();
        // Assuming the response is a Page object, extract the content
        // TODO: Add validation if the API might return a direct list instead of a Page
        if (pageData && typeof pageData === 'object' && 'content' in pageData && Array.isArray(pageData.content)) {
             return pageData.content as SimpleChartOfAccountDto[];
        }
        // Fallback if the structure is unexpected or directly an array
        if (Array.isArray(pageData)) {
            return pageData as SimpleChartOfAccountDto[];
        }
        console.warn("Unexpected response structure for active chart of accounts:", pageData);
        return []; // Return empty array if structure is wrong
    } catch (error) {
        console.error("Error fetching active chart of accounts:", error);
        throw error;
    }
}


/**
 * Deletes a chart of account by its ID.
 */
export async function deleteChartOfAccount(id: string): Promise<void> {
    const url = `${API_BASE_URL}${CHART_OF_ACCOUNTS_API_PATH}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'DELETE',
        });

        if (!response.ok && response.status !== 204) { // 204 No Content is a success for DELETE
            const errorData = await response.json().catch(() => ({}));
            console.error("Delete Chart of Account Error Data:", errorData); // Log detailed error
            throw new Error(errorData?.message || `Failed to delete chart of account ${id}: ${response.statusText}`);
        }
        // No content expected on successful delete
    } catch (error) {
        console.error(`Error deleting chart of account ${id}:`, error);
        throw error;
    }
}
