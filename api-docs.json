{"openapi": "3.0.1", "info": {"title": "MaaliSchool Management System API", "description": "API documentation for the MaaliSchool Management System", "version": "v1.0"}, "servers": [{"url": "http://localhost:8081", "description": "Generated server url"}], "security": [{"Bearer Authentication": []}], "tags": [{"name": "Budgets", "description": "APIs for managing Budgets"}, {"name": "Permission Management", "description": "Endpoints for managing permissions (Requires ADMIN role)"}, {"name": "Holiday Management", "description": "Endpoints for managing holidays within terms (Requires ADMIN role)"}, {"name": "Section Management", "description": "Endpoints for managing class sections (Requires ADMIN role)"}, {"name": "Expense Categories", "description": "APIs for managing Expense Categories"}, {"name": "General <PERSON><PERSON>", "description": "API for managing General Ledger"}, {"name": "Guardian Management", "description": "Endpoints for managing guardian profiles"}, {"name": "Journal Entries", "description": "APIs for managing Journal Entries"}, {"name": "Term Management", "description": "Endpoints for managing terms within academic years (Requires ADMIN role)"}, {"name": "Authentication", "description": "Endpoints for user registration, login, activation, and password management"}, {"name": "Expense Reports", "description": "APIs for generating expense reports"}, {"name": "Grade Level Management", "description": "Endpoints for managing grade levels (Requires ADMIN role)"}, {"name": "Fixed Assets", "description": "APIs for managing Fixed Assets"}, {"name": "Receipts", "description": "APIs for managing Receipts and Payment Allocations"}, {"name": "Student Fees", "description": "APIs for managing fees assigned to students"}, {"name": "Branch Management", "description": "Endpoints for managing school branches (Requires ADMIN role)"}, {"name": "AI Reports", "description": "APIs for generating AI-powered accounting reports for non-accounting staff"}, {"name": "Inventory Items", "description": "APIs for managing Inventory Items"}, {"name": "Fees", "description": "APIs for managing Fees"}, {"name": "Fee Categories", "description": "APIs for managing Fee Categories"}, {"name": "Accounting Audit Logs", "description": "Endpoints for viewing accounting audit logs (Requires ADMIN role)"}, {"name": "Tax Management", "description": "APIs for managing taxes"}, {"name": "Expenses", "description": "APIs for managing Expenses"}, {"name": "User Management (Admin)", "description": "Endpoints for managing users (Requires ADMIN role)"}, {"name": "Discounts", "description": "APIs for managing Discounts"}, {"name": "Payment Methods", "description": "APIs for managing Payment Methods"}, {"name": "Educational Stage Management", "description": "Endpoints for managing educational stages (Requires ADMIN role)"}, {"name": "Student Management", "description": "Endpoints for managing student profiles"}, {"name": "Academic Year Management", "description": "Endpoints for managing academic years (Requires ADMIN role)"}, {"name": "Role Management", "description": "Endpoints for managing roles and their permissions (Requires ADMIN role)"}, {"name": "Inventory Management", "description": "APIs for managing Inventory stock levels and transactions"}, {"name": "Chart of Accounts", "description": "APIs for managing the Chart of Accounts"}], "paths": {"/api/v1/students/{studentId}": {"get": {"tags": ["Student Management"], "summary": "Get student profile by ID", "description": "Requires ADMIN role or being the student/guardian.", "operationId": "getStudentById", "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Student not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Student found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentDto"}}}}}, "security": [{"Bearer Authentication": []}]}, "put": {"tags": ["Student Management"], "summary": "Update student profile", "description": "Updates student profile details. Admission number cannot be changed as it is auto-generated. Requires ADMIN role.", "operationId": "updateStudentProfile", "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStudentProfileRequest"}}}, "required": true}, "responses": {"400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Student not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Student profile updated", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentDto"}}}}, "409": {"description": "National ID conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/guardians/{guardianId}": {"get": {"tags": ["Guardian Management"], "summary": "Get guardian profile by ID", "description": "Requires ADMIN role or being the guardian.", "operationId": "getGuardianById", "parameters": [{"name": "guardianId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Guardian not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Guardian found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GuardianDto"}}}}}, "security": [{"Bearer Authentication": []}]}, "put": {"tags": ["Guardian Management"], "summary": "Update guardian profile", "description": "Requires ADMIN role or being the guardian.", "operationId": "updateGuardianProfile", "parameters": [{"name": "guardianId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGuardianProfileRequest"}}}, "required": true}, "responses": {"400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Guardian not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Guardian profile updated", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GuardianDto"}}}}, "409": {"description": "National ID conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/terms/{id}": {"get": {"tags": ["Term Management"], "summary": "Get term by ID", "operationId": "getTermById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TermDto"}}}}}}, "put": {"tags": ["Term Management"], "summary": "Update a term", "operationId": "updateTerm", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTermRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TermDto"}}}}}}, "delete": {"tags": ["Term Management"], "summary": "Delete a term", "operationId": "deleteTerm", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/sections/{id}": {"get": {"tags": ["Section Management"], "summary": "Get section by ID", "operationId": "getSectionById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SectionDto"}}}}}}, "put": {"tags": ["Section Management"], "summary": "Update a section", "operationId": "updateSection", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSectionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SectionDto"}}}}}}, "delete": {"tags": ["Section Management"], "summary": "Delete a section", "operationId": "deleteSection", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/roles/{id}": {"get": {"tags": ["Role Management"], "summary": "Get role by ID", "operationId": "getRoleById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Role found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RoleDto"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Role not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}, "put": {"tags": ["Role Management"], "summary": "Update an existing role", "operationId": "updateRole", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleRequest"}}}, "required": true}, "responses": {"400": {"description": "Invalid input data or permission not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Updated role name conflicts with an existing one", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Role updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RoleDto"}}}}, "404": {"description": "Role not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}, "delete": {"tags": ["Role Management"], "summary": "Delete a role", "operationId": "deleteRole", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Role deleted successfully"}, "409": {"description": "Conflict - Role might be in use", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Role not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/permissions/{id}": {"get": {"tags": ["Permission Management"], "summary": "Get permission by ID", "operationId": "getPermissionById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Permission not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Permission found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}}}}, "security": [{"Bearer Authentication": []}]}, "put": {"tags": ["Permission Management"], "summary": "Update an existing permission", "operationId": "updatePermission", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Updated permission name conflicts with an existing one", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Permission not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Permission updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}}}}, "security": [{"Bearer Authentication": []}]}, "delete": {"tags": ["Permission Management"], "summary": "Delete a permission", "operationId": "deletePermission", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Permission deleted successfully"}, "404": {"description": "Permission not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/holidays/{id}": {"get": {"tags": ["Holiday Management"], "summary": "Get holiday by ID", "operationId": "getHolidayById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HolidayDto"}}}}}}, "put": {"tags": ["Holiday Management"], "summary": "Update a holiday", "operationId": "updateHoliday", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHolidayRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HolidayDto"}}}}}}, "delete": {"tags": ["Holiday Management"], "summary": "Delete a holiday", "operationId": "deleteHoliday", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/grade-levels/{id}": {"get": {"tags": ["Grade Level Management"], "summary": "Get grade level by ID", "operationId": "getGradeLevelById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GradeLevelDto"}}}}}}, "put": {"tags": ["Grade Level Management"], "summary": "Update a grade level", "operationId": "updateGradeLevel", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGradeLevelRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GradeLevelDto"}}}}}}, "delete": {"tags": ["Grade Level Management"], "summary": "Delete a grade level", "operationId": "deleteGradeLevel", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/educational-stages/{id}": {"get": {"tags": ["Educational Stage Management"], "summary": "Get educational stage by ID", "operationId": "getEducationalStageById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EducationalStageDto"}}}}}}, "put": {"tags": ["Educational Stage Management"], "summary": "Update an educational stage", "operationId": "updateEducationalStage", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEducationalStageRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EducationalStageDto"}}}}}}, "delete": {"tags": ["Educational Stage Management"], "summary": "Delete an educational stage", "operationId": "deleteEducationalStage", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/branches/{id}": {"get": {"tags": ["Branch Management"], "summary": "Get branch by ID", "operationId": "getBranchById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BranchDto"}}}}}}, "put": {"tags": ["Branch Management"], "summary": "Update a branch", "operationId": "updateBranch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBranchRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BranchDto"}}}}}}, "delete": {"tags": ["Branch Management"], "summary": "Delete a branch", "operationId": "deleteBranch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/academic-years/{id}": {"get": {"tags": ["Academic Year Management"], "summary": "Get academic year by ID", "operationId": "getAcademicYearById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AcademicYearDto"}}}}}}, "put": {"tags": ["Academic Year Management"], "summary": "Update an academic year", "operationId": "updateAcademicYear", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAcademicYearRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AcademicYearDto"}}}}}}, "delete": {"tags": ["Academic Year Management"], "summary": "Delete an academic year", "operationId": "deleteAcademicYear", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/accounting/taxes/{id}": {"get": {"tags": ["Tax Management"], "summary": "Get a tax by ID", "operationId": "getTaxById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Tax not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Tax found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaxDto"}}}}}}, "put": {"tags": ["Tax Management"], "summary": "Update an existing tax", "operationId": "updateTax", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTaxRequest"}}}, "required": true}, "responses": {"404": {"description": "Tax not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Tax updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaxDto"}}}}}}, "delete": {"tags": ["Tax Management"], "summary": "Delete a tax by ID", "operationId": "deleteTax", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Tax not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Tax deleted successfully"}}}}, "/api/v1/accounting/student-fees/{id}": {"get": {"tags": ["Student Fees"], "summary": "Get Student Fee by ID", "operationId": "getStudentFeeById", "parameters": [{"name": "id", "in": "path", "description": "ID of the student fee record", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Student Fee not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Student Fee found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentFeeDto"}}}}}}, "put": {"tags": ["Student Fees"], "summary": "Update a Student Fee record", "description": "Updates details of an assigned fee (e.g., amount paid, status, notes).", "operationId": "updateStudentFee", "parameters": [{"name": "id", "in": "path", "description": "ID of the student fee record to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStudentFeeRequest"}}}, "required": true}, "responses": {"404": {"description": "Student Fee or Discount not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Student Fee updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentFeeDto"}}}}}}}, "/api/v1/accounting/payment-methods/{id}": {"get": {"tags": ["Payment Methods"], "summary": "Get Payment Method by ID", "operationId": "getPaymentMethodById", "parameters": [{"name": "id", "in": "path", "description": "ID of the payment method", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Payment Method not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Payment Method found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaymentMethodDto"}}}}}}, "put": {"tags": ["Payment Methods"], "summary": "Update a Payment Method", "operationId": "updatePaymentMethod", "parameters": [{"name": "id", "in": "path", "description": "ID of the payment method to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentMethodRequest"}}}, "required": true}, "responses": {"404": {"description": "Payment Method not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Payment Method name conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Payment Method updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaymentMethodDto"}}}}}}, "delete": {"tags": ["Payment Methods"], "summary": "Delete a Payment Method", "operationId": "deletePaymentMethod", "parameters": [{"name": "id", "in": "path", "description": "ID of the payment method to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Payment Method not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Cannot delete method (e.g., used in receipts)", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Payment Method deleted successfully"}}}}, "/api/v1/accounting/payment-methods/{id}/deactivate": {"put": {"tags": ["Payment Methods"], "summary": "Deactivate a Payment Method", "operationId": "deactivatePaymentMethod", "parameters": [{"name": "id", "in": "path", "description": "ID of the payment method to deactivate", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Payment Method not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Payment Method deactivated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaymentMethodDto"}}}}}}}, "/api/v1/accounting/payment-methods/{id}/activate": {"put": {"tags": ["Payment Methods"], "summary": "Activate a Payment Method", "operationId": "activatePaymentMethod", "parameters": [{"name": "id", "in": "path", "description": "ID of the payment method to activate", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Payment Method not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Payment Method activated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaymentMethodDto"}}}}}}}, "/api/v1/accounting/journal-entries/{id}": {"get": {"tags": ["Journal Entries"], "summary": "Get Journal Entry by ID", "description": "Retrieves a specific journal entry by its unique ID.", "operationId": "getJournalEntryById", "parameters": [{"name": "id", "in": "path", "description": "ID of the journal entry to retrieve", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Journal Entry not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Journal Entry found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JournalEntryDto"}}}}}}, "put": {"tags": ["Journal Entries"], "summary": "Update a Journal Entry", "description": "Updates an existing journal entry. Only possible if the entry is not yet posted.", "operationId": "updateJournalEntry", "parameters": [{"name": "id", "in": "path", "description": "ID of the journal entry to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateJournalEntryRequest"}}}, "required": true}, "responses": {"404": {"description": "Journal Entry not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict - Entry is already posted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Journal Entry updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JournalEntryDto"}}}}}}, "delete": {"tags": ["Journal Entries"], "summary": "Delete a Journal Entry", "description": "Deletes a journal entry. Only possible if the entry is not yet posted.", "operationId": "deleteJournalEntry", "parameters": [{"name": "id", "in": "path", "description": "ID of the journal entry to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Journal Entry not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict - Entry is already posted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Journal Entry deleted successfully"}}}}, "/api/v1/accounting/items/{id}": {"get": {"tags": ["Inventory Items"], "summary": "Get Item by ID", "operationId": "getItemById", "parameters": [{"name": "id", "in": "path", "description": "ID of the item", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Item not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Item found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ItemDto"}}}}}}, "put": {"tags": ["Inventory Items"], "summary": "Update an Item", "description": "Updates item details. Does not update quantity on hand.", "operationId": "updateItem", "parameters": [{"name": "id", "in": "path", "description": "ID of the item to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateItemRequest"}}}, "required": true}, "responses": {"404": {"description": "Item not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data or duplicate SKU", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Item updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ItemDto"}}}}}}, "delete": {"tags": ["Inventory Items"], "summary": "Delete an Item", "description": "[<PERSON><PERSON><PERSON>] Deletes an item definition. Only possible if stock is zero and no transactions exist.", "operationId": "deleteItem", "parameters": [{"name": "id", "in": "path", "description": "ID of the item to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Item not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Cannot delete item (e.g., has stock or transactions)", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Item deleted successfully"}}}}, "/api/v1/accounting/items/{id}/deactivate": {"put": {"tags": ["Inventory Items"], "summary": "Deactivate an Item", "operationId": "deactivateItem", "parameters": [{"name": "id", "in": "path", "description": "ID of the item to deactivate", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Item not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Item deactivated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ItemDto"}}}}}}}, "/api/v1/accounting/items/{id}/activate": {"put": {"tags": ["Inventory Items"], "summary": "Activate an Item", "operationId": "activateItem", "parameters": [{"name": "id", "in": "path", "description": "ID of the item to activate", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Item not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Item activated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ItemDto"}}}}}}}, "/api/v1/accounting/fixed-assets/{id}": {"get": {"tags": ["Fixed Assets"], "summary": "Get Fixed Asset by ID", "operationId": "getFixedAssetById", "parameters": [{"name": "id", "in": "path", "description": "ID of the fixed asset", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Asset not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "<PERSON><PERSON> found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FixedAssetDto"}}}}}}, "put": {"tags": ["Fixed Assets"], "summary": "Update a Fixed Asset", "description": "Updates an existing fixed asset. Cannot update disposed assets.", "operationId": "updateFixedAsset", "parameters": [{"name": "id", "in": "path", "description": "ID of the asset to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFixedAssetRequest"}}}, "required": true}, "responses": {"404": {"description": "Asset or related Chart of Account not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data or asset is disposed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Asset updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FixedAssetDto"}}}}}}}, "/api/v1/accounting/fees/{id}": {"get": {"tags": ["Fees"], "summary": "Get Fee by ID", "operationId": "getFeeById", "parameters": [{"name": "id", "in": "path", "description": "ID of the fee", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Fee not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Fee found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FeeDto"}}}}}}, "put": {"tags": ["Fees"], "summary": "Update a Fee", "operationId": "updateFee", "parameters": [{"name": "id", "in": "path", "description": "ID of the fee to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFeeRequest"}}}, "required": true}, "responses": {"404": {"description": "Fee or Fee Category not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Fee name conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Fee updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FeeDto"}}}}}}, "delete": {"tags": ["Fees"], "summary": "Delete a Fee", "operationId": "deleteFee", "parameters": [{"name": "id", "in": "path", "description": "ID of the fee to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Fee not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Cannot delete fee (e.g., assigned to students)", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Fee deleted successfully"}}}}, "/api/v1/accounting/fees/{id}/deactivate": {"put": {"tags": ["Fees"], "summary": "Deactivate a Fee", "operationId": "deactivateFee", "parameters": [{"name": "id", "in": "path", "description": "ID of the fee to deactivate", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Fee not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Fee deactivated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FeeDto"}}}}}}}, "/api/v1/accounting/fees/{id}/activate": {"put": {"tags": ["Fees"], "summary": "Activate a Fee", "operationId": "activateFee", "parameters": [{"name": "id", "in": "path", "description": "ID of the fee to activate", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Fee not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Fee activated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FeeDto"}}}}}}}, "/api/v1/accounting/fee-categories/{id}": {"get": {"tags": ["Fee Categories"], "summary": "Get Fee Category by ID", "operationId": "getFeeCategoryById", "parameters": [{"name": "id", "in": "path", "description": "ID of the category", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Category not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Category found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FeeCategoryDto"}}}}}}, "put": {"tags": ["Fee Categories"], "summary": "Update a Fee Category", "operationId": "updateFeeCategory", "parameters": [{"name": "id", "in": "path", "description": "ID of the category to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFeeCategoryRequest"}}}, "required": true}, "responses": {"404": {"description": "Category not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Category name already exists", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Category updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FeeCategoryDto"}}}}}}, "delete": {"tags": ["Fee Categories"], "summary": "Delete a Fee Category", "operationId": "deleteFeeCategory", "parameters": [{"name": "id", "in": "path", "description": "ID of the category to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Category not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Cannot delete category (e.g., due to associated fees)", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Category deleted successfully"}}}}, "/api/v1/accounting/expenses/{id}": {"get": {"tags": ["Expenses"], "summary": "Get Expense by ID", "operationId": "getExpenseById", "parameters": [{"name": "id", "in": "path", "description": "ID of the expense", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Expense not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Expense found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExpenseDto"}}}}}}, "put": {"tags": ["Expenses"], "summary": "Update an Expense", "operationId": "updateExpense", "parameters": [{"name": "id", "in": "path", "description": "ID of the expense to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateExpenseRequest"}}}, "required": true}, "responses": {"404": {"description": "Expense, Category, or Payment Method not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Expense updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExpenseDto"}}}}}}, "delete": {"tags": ["Expenses"], "summary": "Delete an Expense", "operationId": "deleteExpense", "parameters": [{"name": "id", "in": "path", "description": "ID of the expense to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Expense not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Expense deleted successfully"}}}}, "/api/v1/accounting/expense-categories/{id}": {"get": {"tags": ["Expense Categories"], "summary": "Get Expense Category by ID", "operationId": "getExpenseCategoryById", "parameters": [{"name": "id", "in": "path", "description": "ID of the category", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Category not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Category found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExpenseCategoryDto"}}}}}}, "put": {"tags": ["Expense Categories"], "summary": "Update an Expense Category", "operationId": "updateExpenseCategory", "parameters": [{"name": "id", "in": "path", "description": "ID of the category to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateExpenseCategoryRequest"}}}, "required": true}, "responses": {"404": {"description": "Category not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data or duplicate name", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Category updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExpenseCategoryDto"}}}}}}, "delete": {"tags": ["Expense Categories"], "summary": "Delete an Expense Category", "operationId": "deleteExpenseCategory", "parameters": [{"name": "id", "in": "path", "description": "ID of the category to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Category not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Cannot delete category (e.g., due to associated expenses)", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Category deleted successfully"}}}}, "/api/v1/accounting/discounts/{id}": {"get": {"tags": ["Discounts"], "summary": "Get Discount by ID", "operationId": "getDiscountById", "parameters": [{"name": "id", "in": "path", "description": "ID of the discount", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Discount not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Discount found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DiscountDto"}}}}}}, "put": {"tags": ["Discounts"], "summary": "Update a Discount", "operationId": "updateDiscount", "parameters": [{"name": "id", "in": "path", "description": "ID of the discount to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDiscountRequest"}}}, "required": true}, "responses": {"404": {"description": "Discount not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data or duplicate code", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Discount updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DiscountDto"}}}}}}, "delete": {"tags": ["Discounts"], "summary": "Delete a Discount", "operationId": "deleteDiscount", "parameters": [{"name": "id", "in": "path", "description": "ID of the discount to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Discount not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Cannot delete discount (e.g., assigned to student fees)", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Discount deleted successfully"}}}}, "/api/v1/accounting/discounts/{id}/deactivate": {"put": {"tags": ["Discounts"], "summary": "Deactivate a Discount", "operationId": "deactivateDiscount", "parameters": [{"name": "id", "in": "path", "description": "ID of the discount to deactivate", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Discount not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Discount deactivated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DiscountDto"}}}}}}}, "/api/v1/accounting/discounts/{id}/activate": {"put": {"tags": ["Discounts"], "summary": "Activate a Discount", "operationId": "activateDiscount", "parameters": [{"name": "id", "in": "path", "description": "ID of the discount to activate", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Discount not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Discount activated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DiscountDto"}}}}}}}, "/api/v1/accounting/chart-of-accounts/{id}": {"get": {"tags": ["Chart of Accounts"], "summary": "Get Chart of Account by ID", "description": "Retrieves a specific account by its unique ID.", "operationId": "getChartOfAccountById", "parameters": [{"name": "id", "in": "path", "description": "ID of the account to retrieve", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Account not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Account found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChartOfAccountDto"}}}}}}, "put": {"tags": ["Chart of Accounts"], "summary": "Update a Chart of Account", "description": "Updates an existing account by its ID.", "operationId": "updateChartOfAccount", "parameters": [{"name": "id", "in": "path", "description": "ID of the account to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChartOfAccountRequest"}}}, "required": true}, "responses": {"404": {"description": "Account not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Account updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChartOfAccountDto"}}}}}}, "delete": {"tags": ["Chart of Accounts"], "summary": "Delete a Chart of Account", "description": "Deletes an account by its ID. Note: Consider using soft delete (deactivation) instead for data integrity.", "operationId": "deleteChartOfAccount", "parameters": [{"name": "id", "in": "path", "description": "ID of the account to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Account not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Cannot delete account (e.g., due to dependencies)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Account deleted successfully"}}}}, "/api/v1/accounting/budgets/{id}": {"get": {"tags": ["Budgets"], "summary": "Get Budget entry by ID", "operationId": "getBudgetById", "parameters": [{"name": "id", "in": "path", "description": "ID of the budget entry", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Budget not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Budget found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BudgetDto"}}}}}}, "put": {"tags": ["Budgets"], "summary": "Update a Budget entry", "description": "Updates the amount or notes for an existing budget entry.", "operationId": "updateBudget", "parameters": [{"name": "id", "in": "path", "description": "ID of the budget entry to update", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBudgetRequest"}}}, "required": true}, "responses": {"404": {"description": "Budget not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data or attempt to change account/year", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Budget updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BudgetDto"}}}}}}, "delete": {"tags": ["Budgets"], "summary": "Delete a Budget entry", "operationId": "deleteBudget", "parameters": [{"name": "id", "in": "path", "description": "ID of the budget entry to delete", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Budget not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Budget deleted successfully"}}}}, "/api/v1/students/{studentId}/link-guardian/{guardianId}": {"post": {"tags": ["Student Management"], "summary": "Link student to guardian", "description": "Requires ADMIN role.", "operationId": "linkStudentTo<PERSON><PERSON><PERSON>", "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "guardianId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Student linked to guardian", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentDto"}}}}, "404": {"description": "Student or Guardian not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/students/profiles": {"post": {"tags": ["Student Management"], "summary": "Create student profile with new user account", "description": "Creates a new user account with ROLE_STUDENT and links it to a new student profile. Admission number is auto-generated in the format yyyyxxxx. Requires ADMIN role.", "operationId": "createStudentProfile", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStudentProfileRequest"}}}, "required": true}, "responses": {"201": {"description": "Student profile created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentDto"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Guardian not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Email already exists or national ID conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/guardians/register": {"post": {"tags": ["Guardian Management"], "summary": "Create guardian profile with new user account", "description": "Creates a new user account with ROLE_GUARDIAN and a guardian profile. Requires ADMIN role.", "operationId": "createGuardianWithUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGuardianWithUserRequest"}}}, "required": true}, "responses": {"400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Guardian profile created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GuardianDto"}}}}, "409": {"description": "Email already exists or National ID conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/guardians/profiles": {"post": {"tags": ["Guardian Management"], "summary": "Create guardian profile for a user", "description": "Links a guardian profile to an existing user account. Requires ADMIN role.", "operationId": "createGuardianProfile", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGuardianProfileRequest"}}}, "required": true}, "responses": {"409": {"description": "Profile already exists for user or National ID conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "User not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Guardian profile created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GuardianDto"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/auth/reset-password": {"post": {"tags": ["Authentication"], "summary": "Reset user password", "description": "Sets a new password using a valid reset token.", "operationId": "resetPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Password reset successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Invalid/expired token or invalid new password", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/v1/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user", "description": "Creates a new user account. Requires email activation.", "operationId": "register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}, "responses": {"201": {"description": "User registered successfully (requires activation)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "409": {"description": "Email already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/v1/auth/login": {"post": {"tags": ["Authentication"], "summary": "Authenticate user", "description": "Logs in a user and returns an access token.", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"401": {"description": "Invalid credentials or user not found/enabled", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/v1/auth/forgot-password": {"post": {"tags": ["Authentication"], "summary": "Initiate password reset", "description": "Sends a password reset link/token to the user's email.", "operationId": "forgotPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Password reset email sent (if user exists)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Invalid email format", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/v1/admin/terms": {"get": {"tags": ["Term Management"], "summary": "Get all terms for a specific academic year", "operationId": "getTermsByAcademicYear", "parameters": [{"name": "academicYearId", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TermDto"}}}}}}}, "post": {"tags": ["Term Management"], "summary": "Create a new term for an academic year", "operationId": "createTerm", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTermRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TermDto"}}}}}}}, "/api/v1/admin/sections": {"get": {"tags": ["Section Management"], "summary": "Get all sections filtered by year, branch, and grade", "operationId": "getSections", "parameters": [{"name": "academicYearId", "in": "query", "description": "ID of the Academic Year", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "branchId", "in": "query", "description": "ID of the Branch", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "gradeLevelId", "in": "query", "description": "ID of the Grade Level", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SectionDto"}}}}}}}, "post": {"tags": ["Section Management"], "summary": "Create a new section", "operationId": "createSection", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSectionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SectionDto"}}}}}}}, "/api/v1/admin/roles": {"get": {"tags": ["Role Management"], "summary": "Get all roles", "operationId": "getAllRoles", "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "List of roles retrieved", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}}}}}}, "security": [{"Bearer Authentication": []}]}, "post": {"tags": ["Role Management"], "summary": "Create a new role", "operationId": "createRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleRequest"}}}, "required": true}, "responses": {"400": {"description": "Invalid input data or permission not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Role name already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Role created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RoleDto"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/roles/{roleId}/permissions": {"post": {"tags": ["Role Management"], "summary": "Assign permissions to a role", "description": "Replaces all existing permissions for the role with the provided list.", "operationId": "assignPermissionsToRole", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Role or one of the Permissions not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data (e.g., empty permission list)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Permissions assigned successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RoleDto"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/roles/users/{userId}/remove": {"post": {"tags": ["Role Management"], "summary": "Remove a role from a user", "operationId": "removeRoleFromUser", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "User or Role not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Role removed successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/roles/users/{userId}/assign": {"post": {"tags": ["Role Management"], "summary": "Assign a role to a user", "operationId": "assignRoleToUser", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Role assigned successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "User or Role not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/permissions": {"get": {"tags": ["Permission Management"], "summary": "Get all permissions", "operationId": "getAllPermissions", "responses": {"200": {"description": "List of permissions retrieved", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionDto"}}}}}, "403": {"description": "Forbidden - User does not have ADMIN role", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}, "post": {"tags": ["Permission Management"], "summary": "Create a new permission", "operationId": "createPermission", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Permission created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PermissionDto"}}}}, "409": {"description": "Permission name already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/holidays": {"get": {"tags": ["Holiday Management"], "summary": "Get all holidays for a specific term", "operationId": "getHolidaysByTerm", "parameters": [{"name": "termId", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HolidayDto"}}}}}}}, "post": {"tags": ["Holiday Management"], "summary": "Create a new holiday for a term", "operationId": "createHoliday", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateHolidayRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HolidayDto"}}}}}}}, "/api/v1/admin/grade-levels": {"get": {"tags": ["Grade Level Management"], "summary": "Get all grade levels for a specific educational stage", "operationId": "getGradeLevelsByStage", "parameters": [{"name": "stageId", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GradeLevelDto"}}}}}}}, "post": {"tags": ["Grade Level Management"], "summary": "Create a new grade level for an educational stage", "operationId": "createGradeLevel", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGradeLevelRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GradeLevelDto"}}}}}}}, "/api/v1/admin/educational-stages": {"get": {"tags": ["Educational Stage Management"], "summary": "Get all educational stages", "operationId": "getAllEducationalStages", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EducationalStageDto"}}}}}}}, "post": {"tags": ["Educational Stage Management"], "summary": "Create a new educational stage", "operationId": "createEducationalStage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEducationalStageRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EducationalStageDto"}}}}}}}, "/api/v1/admin/branches": {"get": {"tags": ["Branch Management"], "summary": "Get all branches", "operationId": "getAllBranches", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BranchDto"}}}}}}}, "post": {"tags": ["Branch Management"], "summary": "Create a new branch", "operationId": "createBranch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBranchRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BranchDto"}}}}}}}, "/api/v1/admin/branches/{branchId}/stages/{stageId}": {"post": {"tags": ["Branch Management"], "summary": "Assign an educational stage to a branch", "operationId": "assignStageToBranch", "parameters": [{"name": "branchId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "stageId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BranchDto"}}}}}}, "delete": {"tags": ["Branch Management"], "summary": "Remove an educational stage from a branch", "operationId": "removeStageFromBranch", "parameters": [{"name": "branchId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "stageId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BranchDto"}}}}}}}, "/api/v1/admin/academic-years": {"get": {"tags": ["Academic Year Management"], "summary": "Search or Get all academic years (paginated)", "description": "Retrieves a paginated list of all academic years. Optionally filters by name (search term).", "operationId": "searchAcademicYears", "parameters": [{"name": "search", "in": "query", "description": "Search term to filter by name (case-insensitive, partial match). Leave blank to get all.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageAcademicYearDto"}}}}}}, "post": {"tags": ["Academic Year Management"], "summary": "Create a new academic year", "operationId": "createAcademicYear", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAcademicYearRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AcademicYearDto"}}}}}}}, "/api/v1/admin/academic-years/{id}/activate": {"post": {"tags": ["Academic Year Management"], "summary": "Set an academic year as active", "operationId": "setActiveAcademicYear", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AcademicYearDto"}}}}}}}, "/api/v1/accounting/taxes": {"get": {"tags": ["Tax Management"], "summary": "Get all taxes (paginated)", "operationId": "getAllTaxes", "parameters": [{"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Taxes retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageTaxDto"}}}}}}, "post": {"tags": ["Tax Management"], "summary": "Create a new tax", "operationId": "createTax", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTaxRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Tax created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaxDto"}}}}}}}, "/api/v1/accounting/student-fees": {"get": {"tags": ["Student Fees"], "summary": "Find Student Fees (Paginated)", "description": "Retrieves a paginated list of student fees, with various filters.", "operationId": "findStudentFees", "parameters": [{"name": "studentId", "in": "query", "description": "Filter by Student ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "feeId", "in": "query", "description": "Filter by <PERSON><PERSON>", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "description": "Filter by Fee Status (e.g., UNPAID, PAID)", "required": false, "schema": {"type": "string", "enum": ["UNPAID", "PARTIALLY_PAID", "PAID", "OVERDUE", "WAIVED", "CANCELLED"]}}, {"name": "dueDateStart", "in": "query", "description": "Filter by due date start (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "dueDateEnd", "in": "query", "description": "Filter by due date end (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Student Fees retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageStudentFeeDto"}}}}}}, "post": {"tags": ["Student Fees"], "summary": "Assign a Fee to a Student", "operationId": "assignFeeToStudent", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignFeeRequest"}}}, "required": true}, "responses": {"404": {"description": "Student, Fee, or Discount not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Fe<PERSON> already assigned to this student for this period", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Fee assigned successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentFeeDto"}}}}}}}, "/api/v1/accounting/receipts": {"get": {"tags": ["Receipts"], "summary": "Find Receipts (Paginated)", "description": "Retrieves a paginated list of receipts, optionally filtered.", "operationId": "findReceipts", "parameters": [{"name": "startDate", "in": "query", "description": "Filter by start date (inclusive) in YYYY-MM-DD format", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "Filter by end date (inclusive) in YYYY-MM-DD format", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "studentId", "in": "query", "description": "Filter by student ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "paymentMethodId", "in": "query", "description": "Filter by payment method ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Receipts retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageReceiptDto"}}}}}}, "post": {"tags": ["Receipts"], "summary": "Create a new Receipt", "description": "Records an incoming payment and allocates it to student fees.", "operationId": "createReceipt", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReceiptRequest"}}}, "required": true}, "responses": {"404": {"description": "Student, Payment Method, or Student Fee not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input, allocation error (overpayment, insufficient amount)", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Receipt created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReceiptDto"}}}}}}}, "/api/v1/accounting/receipts/{id}/cancel": {"post": {"tags": ["Receipts"], "summary": "Cancel a Receipt", "description": "Marks a receipt as cancelled and reverses its payment allocations.", "operationId": "cancelReceipt", "parameters": [{"name": "id", "in": "path", "description": "ID of the receipt to cancel", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"404": {"description": "Receipt not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Receipt already cancelled or cancellation failed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Receipt cancelled successfully"}}}}, "/api/v1/accounting/payment-methods": {"get": {"tags": ["Payment Methods"], "summary": "Get all Payment Methods (Paginated)", "description": "Retrieves a paginated list of payment methods. Supports searching by English or Arabic name and sorting.", "operationId": "getAllPaymentMethods", "parameters": [{"name": "search", "in": "query", "description": "Search by English or Arabic name (partial match, case-insensitive)", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Payment Methods retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagePaymentMethodDto"}}}}}}, "post": {"tags": ["Payment Methods"], "summary": "Create a new Payment Method", "operationId": "createPaymentMethod", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentMethodRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Payment Method name already exists", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Payment Method created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaymentMethodDto"}}}}}}}, "/api/v1/accounting/journal-entries": {"get": {"tags": ["Journal Entries"], "summary": "Find Journal Entries (Paginated)", "description": "Retrieves a paginated list of journal entries, optionally filtered by date range, reference number, and posted status.", "operationId": "findJournalEntries", "parameters": [{"name": "startDate", "in": "query", "description": "Start date (inclusive) in YYYY-MM-DD format", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "End date (inclusive) in YYYY-MM-DD format", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "referenceNumber", "in": "query", "description": "Reference number to search for (case-insensitive, partial match)", "required": false, "schema": {"type": "string"}}, {"name": "isPosted", "in": "query", "description": "Filter by posted status (true=posted, false=unposted, omit=both)", "required": false, "schema": {"type": "boolean"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Journal Entries retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageJournalEntryDto"}}}}}}, "post": {"tags": ["Journal Entries"], "summary": "Create a new Journal Entry", "description": "Creates a new journal entry with its lines. Debits must equal credits.", "operationId": "createJournalEntry", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJournalEntryRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data (e.g., unbalanced entry, invalid account)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Journal Entry created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JournalEntryDto"}}}}}}}, "/api/v1/accounting/journal-entries/{id}/post": {"post": {"tags": ["Journal Entries"], "summary": "Post a Journal Entry", "description": "Marks a journal entry as posted, finalizing it. Requires the entry to be balanced.", "operationId": "postJournalEntry", "parameters": [{"name": "id", "in": "path", "description": "ID of the journal entry to post", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Journal Entry not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict - Entry is already posted or unbalanced", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Journal Entry posted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JournalEntryDto"}}}}}}}, "/api/v1/accounting/items": {"get": {"tags": ["Inventory Items"], "summary": "Get all Items (Paginated)", "operationId": "getAllItems", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Items retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageItemDto"}}}}}}, "post": {"tags": ["Inventory Items"], "summary": "Create a new Inventory Item", "operationId": "createItem", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateItemRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data or duplicate SKU", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Item created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ItemDto"}}}}}}}, "/api/v1/accounting/inventory/adjust": {"post": {"tags": ["Inventory Management"], "summary": "Adjust Inventory Stock", "description": "Records an inventory adjustment (e.g., purchase, sale, damage, count adjustment) and updates the item's quantity on hand.", "operationId": "adjustInventory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAdjustmentRequest"}}}, "required": true}, "responses": {"404": {"description": "Item not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input, insufficient stock", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Adjustment recorded successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/InventoryTransactionDto"}}}}}}}, "/api/v1/accounting/fixed-assets": {"get": {"tags": ["Fixed Assets"], "summary": "Get all Fixed Assets (Paginated)", "description": "Retrieves a paginated list of fixed assets.", "operationId": "getAllFixedAssets", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Assets retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageFixedAssetDto"}}}}}}, "post": {"tags": ["Fixed Assets"], "summary": "Create a new Fixed Asset", "operationId": "createFixedAsset", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFixedAssetRequest"}}}, "required": true}, "responses": {"404": {"description": "Related Chart of Account not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Asset created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FixedAssetDto"}}}}}}}, "/api/v1/accounting/fixed-assets/{id}/dispose": {"post": {"tags": ["Fixed Assets"], "summary": "Dispose a Fixed Asset", "description": "Marks a fixed asset as disposed and records disposal notes.", "operationId": "disposeFixedAsset", "parameters": [{"name": "id", "in": "path", "description": "ID of the asset to dispose", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"404": {"description": "Asset not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Asset already disposed or missing notes", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Asset disposed successfully"}}}}, "/api/v1/accounting/fees": {"get": {"tags": ["Fees"], "summary": "Get all Fees (Paginated)", "description": "Retrieves a paginated list of fees. Can filter by academic year and category.", "operationId": "getAllFees", "parameters": [{"name": "academicYear", "in": "query", "description": "Filter by academic year (e.g., 2024-2025)", "required": false, "schema": {"type": "string"}}, {"name": "categoryId", "in": "query", "description": "Filter by fee category ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Fees retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageFeeDto"}}}}}}, "post": {"tags": ["Fees"], "summary": "Create a new Fee", "operationId": "createFee", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFeeRequest"}}}, "required": true}, "responses": {"404": {"description": "Fee Category not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Fee already exists for this name/year/category", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Fe<PERSON> created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FeeDto"}}}}}}}, "/api/v1/accounting/fee-categories": {"get": {"tags": ["Fee Categories"], "summary": "Search or Get all Fee Categories (Paginated)", "description": "Retrieves a paginated list of fee categories. Optionally filters by a search term matching nameEn or nameAr (case-insensitive, partial match).", "operationId": "searchFeeCategories", "parameters": [{"name": "search", "in": "query", "description": "Search term to filter by nameEn or nameAr (case-insensitive, partial match). Leave blank to get all.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Categories retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageFeeCategoryDto"}}}}}}, "post": {"tags": ["Fee Categories"], "summary": "Create a new Fee Category", "operationId": "createFeeCategory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFeeCategoryRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Category name already exists", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Category created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FeeCategoryDto"}}}}}}}, "/api/v1/accounting/expenses": {"get": {"tags": ["Expenses"], "summary": "Get all Expenses (Paginated)", "description": "Retrieves a paginated list of expenses. Can filter by date range and category.", "operationId": "findExpenses", "parameters": [{"name": "startDate", "in": "query", "description": "Filter by start date (inclusive) in YYYY-MM-DD format", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "Filter by end date (inclusive) in YYYY-MM-DD format", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "categoryId", "in": "query", "description": "Filter by expense category ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Expenses retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageExpenseDto"}}}}}}, "post": {"tags": ["Expenses"], "summary": "Create a new Expense", "operationId": "createExpense", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateExpenseRequest"}}}, "required": true}, "responses": {"404": {"description": "Expense Category or Payment Method not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Expense created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExpenseDto"}}}}}}}, "/api/v1/accounting/expense-categories": {"get": {"tags": ["Expense Categories"], "summary": "Get all Expense Categories (Paginated)", "description": "Retrieves a paginated list of expense categories, optionally filtered by a search term matching nameEn or nameAr.", "operationId": "getAllExpenseCategories", "parameters": [{"name": "searchTerm", "in": "query", "description": "Search term for nameEn or nameAr (case-insensitive, partial match)", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Categories retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageExpenseCategoryDto"}}}}}}, "post": {"tags": ["Expense Categories"], "summary": "Create a new Expense Category", "operationId": "createExpenseCategory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateExpenseCategoryRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data or duplicate name", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Category created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExpenseCategoryDto"}}}}}}}, "/api/v1/accounting/discounts": {"get": {"tags": ["Discounts"], "summary": "Get all Discounts (Paginated)", "operationId": "getAllDiscounts", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Discounts retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageDiscountDto"}}}}}}, "post": {"tags": ["Discounts"], "summary": "Create a new Discount", "operationId": "createDiscount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDiscountRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data or duplicate code", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Discount created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DiscountDto"}}}}}}}, "/api/v1/accounting/chart-of-accounts": {"get": {"tags": ["Chart of Accounts"], "summary": "Get all Chart of Accounts (Paginated)", "description": "Retrieves a paginated list of all accounts. Supports searching by account number, English name, Arabic name, category, and sorting.", "operationId": "getAllChartOfAccounts", "parameters": [{"name": "accountNumber", "in": "query", "description": "Search by account number (partial match)", "required": false, "schema": {"type": "string"}}, {"name": "nameEn", "in": "query", "description": "Search by English name (partial match, case-insensitive)", "required": false, "schema": {"type": "string"}}, {"name": "nameAr", "in": "query", "description": "Search by Arabic name (partial match, case-insensitive)", "required": false, "schema": {"type": "string"}}, {"name": "category", "in": "query", "description": "Filter by accounting category (e.g., ASSET, LIABILITY)", "required": false, "schema": {"type": "string", "enum": ["ASSET", "LIABILITY", "EQUITY", "REVENUE", "EXPENSE"]}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Accounts retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageChartOfAccountDto"}}}}}}, "post": {"tags": ["Chart of Accounts"], "summary": "Create a new Chart of Account", "description": "Creates a new account in the chart of accounts.", "operationId": "createChartOfAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChartOfAccountRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Account number already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Account created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChartOfAccountDto"}}}}}}}, "/api/v1/accounting/budgets": {"get": {"tags": ["Budgets"], "summary": "Get all Budget entries (Paginated)", "description": "Retrieves a paginated list of budget entries. Can filter by fiscal year.", "operationId": "findBudgets", "parameters": [{"name": "academicYear", "in": "query", "description": "Filter by academic year (e.g., 2024-2025)", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Budgets retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageBudgetDto"}}}}}}, "post": {"tags": ["Budgets"], "summary": "Create a new Budget entry", "operationId": "createBudget", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBudgetRequest"}}}, "required": true}, "responses": {"404": {"description": "Chart of Account not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid input data or duplicate entry", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Budget created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BudgetDto"}}}}}}}, "/api/v1/accounting/ai-reports": {"get": {"tags": ["AI Reports"], "summary": "List AI Reports", "description": "Retrieves a paginated list of AI-generated reports. Can be filtered by report type. Supports General Led<PERSON>, Expenses, Income Statement, Trial Balance, and Balance Sheet report types.", "operationId": "listReports", "parameters": [{"name": "reportType", "in": "query", "description": "Report type to filter by (e.g., GENERAL_LEDGER, EXPENSES, INCOME_STATEMENT, TRIAL_BALANCE, BALANCE_SHEET). Leave empty to get all types.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Error retrieving reports"}, "200": {"description": "Reports retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageAIReportResponse"}}}}}}, "post": {"tags": ["AI Reports"], "summary": "Generate AI Report", "description": "Generates a human-readable report from accounting data using AI. Supports General Ledger, Expenses, Income Statement, Trial Balance, and Balance Sheet reports. If a report with the same parameters already exists, it will be retrieved from the database. Otherwise, a new report request will be queued for processing and a response with PENDING status will be returned immediately.", "operationId": "generateReport", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AIReportRequest"}}}, "required": true}, "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid request parameters"}, "500": {"description": "Error generating report"}, "202": {"description": "Report request accepted and queued for processing", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AIReportResponse"}}}}, "200": {"description": "Report retrieved from cache", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AIReportResponse"}}}}}}}, "/api/v1/students": {"get": {"tags": ["Student Management"], "summary": "Get students by Guardian ID", "description": "Requires ADMIN role or being the guardian.", "operationId": "getAllStudents_1", "parameters": [{"name": "guardianId", "in": "query", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "List of students retrieved", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StudentDto"}}}}}, "404": {"description": "Guardian not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/students/user/{userId}": {"get": {"tags": ["Student Management"], "summary": "Get student profile by User ID", "description": "Requires ADMIN role or being the user.", "operationId": "getStudentByUserId", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Student profile not found for user", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Student found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentDto"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/guardians": {"get": {"tags": ["Guardian Management"], "summary": "Get all guardians (Admin only)", "description": "Requires ADMIN role. Consider pagination.", "operationId": "getAllGuardians", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "List of guardians retrieved", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GuardianDto"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/guardians/user/{userId}": {"get": {"tags": ["Guardian Management"], "summary": "Get guardian profile by User ID", "description": "Requires ADMIN role or being the user.", "operationId": "getGuardianByUserId", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Guardian found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GuardianDto"}}}}, "404": {"description": "Guardian profile not found for user", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/auth/activate": {"get": {"tags": ["Authentication"], "summary": "Activate user account", "description": "Activates a user account using the provided token.", "operationId": "activateAccount", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Account activated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Invalid or expired activation token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/v1/admin/users": {"get": {"tags": ["User Management (Admin)"], "summary": "Get all users (paginated)", "description": "Retrieves a paginated list of all registered users. Can be filtered by role name.", "operationId": "getAllUsers", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "Optional role name to filter users by (e.g., ROLE_USER, ROLE_ADMIN)", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "List of users retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageUserDto"}}}}, "403": {"description": "Forbidden - User does not have ADMIN role", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/sections/all": {"get": {"tags": ["Section Management"], "summary": "Get all sections with pagination", "operationId": "getAllSections", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageSectionDto"}}}}}}}, "/api/v1/admin/accounting/audit-logs": {"get": {"tags": ["Accounting Audit Logs"], "summary": "Get Accounting Audit Logs (Paginated)", "description": "Retrieves a paginated list of accounting audit logs, optionally filtered by user, entity, or date range.", "operationId": "getAuditLogs", "parameters": [{"name": "userId", "in": "query", "description": "Filter by User ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "entityType", "in": "query", "description": "Filter by Entity Type (e.g., JournalEntry)", "required": false, "schema": {"type": "string"}}, {"name": "entityId", "in": "query", "description": "Filter by En<PERSON>ty ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "startTime", "in": "query", "description": "Start timestamp (inclusive) in ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ)", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "endTime", "in": "query", "description": "End timestamp (inclusive) in ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ)", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Audit logs retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageAccountingAuditLogDto"}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/accounting/audit-logs/entity/{entityType}/{entityId}": {"get": {"tags": ["Accounting Audit Logs"], "summary": "Get All Audit Logs for a Specific Entity", "description": "Retrieves a complete list of audit logs for a given entity type and ID, ordered by timestamp descending.", "operationId": "getAuditLogsForEntity", "parameters": [{"name": "entityType", "in": "path", "description": "Type of the entity (e.g., JournalEntry)", "required": true, "schema": {"type": "string"}}, {"name": "entityId", "in": "path", "description": "ID of the entity", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "No audit logs found for the specified entity"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Audit logs retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountingAuditLogDto"}}}}}}, "security": [{"Bearer Authentication": []}]}}, "/api/v1/admin/academic-years/active": {"get": {"tags": ["Academic Year Management"], "summary": "Search or Get all active academic years (paginated)", "description": "Retrieves a paginated list of active academic years. Optionally filters by name (search term).", "operationId": "searchActiveAcademicYears", "parameters": [{"name": "search", "in": "query", "description": "Search term to filter by name (case-insensitive, partial match). Leave blank to get all active.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageAcademicYearDto"}}}}}}}, "/api/v1/accounting/reports/expenses": {"get": {"tags": ["Expense Reports"], "summary": "Generate Expense Report", "description": "Generates a comprehensive expense report for a specified date range, including detailed expense information and summary statistics grouped by category.", "operationId": "generateExpenseReport", "parameters": [{"name": "startDate", "in": "query", "description": "Start date (inclusive) in YYYY-MM-DD format", "required": true, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "End date (inclusive) in YYYY-MM-DD format", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Invalid date parameters", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Report generated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExpenseReportDto"}}}}}}}, "/api/v1/accounting/receipts/{id}": {"get": {"tags": ["Receipts"], "summary": "Get Receipt by ID", "operationId": "getReceiptById", "parameters": [{"name": "id", "in": "path", "description": "ID of the receipt", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Receipt not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Receipt found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReceiptDto"}}}}}}}, "/api/v1/accounting/receipts/number/{receiptNumber}": {"get": {"tags": ["Receipts"], "summary": "Get Receipt by Number", "operationId": "getReceiptByNumber", "parameters": [{"name": "receiptNumber", "in": "path", "description": "Unique receipt number", "required": true, "schema": {"type": "string"}}], "responses": {"404": {"description": "Receipt not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Receipt found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReceiptDto"}}}}}}}, "/api/v1/accounting/payment-methods/active": {"get": {"tags": ["Payment Methods"], "summary": "Get all active Payment Methods (Paginated)", "description": "Retrieves a paginated list of all currently active payment methods. Supports searching by English or Arabic name and sorting.", "operationId": "getAllActivePaymentMethods", "parameters": [{"name": "search", "in": "query", "description": "Search by English or Arabic name (partial match, case-insensitive)", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Active Payment Methods retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagePaymentMethodDto"}}}}}}}, "/api/v1/accounting/items/code/{itemCode}": {"get": {"tags": ["Inventory Items"], "summary": "Get Item by Item Code", "operationId": "getItemByItemCode", "parameters": [{"name": "itemCode", "in": "path", "description": "Item Code of the item", "required": true, "schema": {"type": "string"}}], "responses": {"404": {"description": "Item not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Item found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ItemDto"}}}}}}}, "/api/v1/accounting/items/active": {"get": {"tags": ["Inventory Items"], "summary": "Get all active Items", "operationId": "getAllActiveItems", "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Active items retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ItemDto"}}}}}}}}, "/api/v1/accounting/inventory/transactions": {"get": {"tags": ["Inventory Management"], "summary": "Get Inventory Transaction History", "description": "Retrieves a paginated list of inventory transactions, optionally filtered by item and date range.", "operationId": "getTransactionHistory", "parameters": [{"name": "itemId", "in": "query", "description": "Filter by item ID", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "startDate", "in": "query", "description": "Filter by start date (inclusive) in YYYY-MM-DD format", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "Filter by end date (inclusive) in YYYY-MM-DD format", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Item not found (if itemId is provided)", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Transactions retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageInventoryTransactionDto"}}}}}}}, "/api/v1/accounting/inventory/items/{itemId}/stock": {"get": {"tags": ["Inventory Management"], "summary": "Get Quantity On Hand", "description": "Retrieves the current quantity on hand for a specific item.", "operationId": "getQuantityOnHand", "parameters": [{"name": "itemId", "in": "path", "description": "ID of the item", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Item not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Quantity retrieved successfully", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "number"}}}}}}}}, "/api/v1/accounting/income-statement": {"get": {"tags": ["income-statement-controller"], "operationId": "getIncomeStatement", "parameters": [{"name": "startDate", "in": "query", "required": true, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/IncomeStatementDto"}}}}}}}, "/api/v1/accounting/general-ledger": {"get": {"tags": ["General <PERSON><PERSON>"], "summary": "Get General Ledger entries", "description": "Retrieves a paginated list of General Ledger entries within a specified date range, with optional filtering by account IDs. Defaults to the current month if dates are not provided.", "operationId": "getGeneralLedger", "parameters": [{"name": "startDate", "in": "query", "description": "Start date of the period (YYYY-MM-DD). Defaults to the start of the current month if not provided.", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "End date of the period (YYYY-MM-DD). Defaults to today's date if not provided.", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "accountIds", "in": "query", "description": "Optional list of account IDs to filter by", "required": false, "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, {"name": "isPosted", "in": "query", "description": "Optional filter for posted entries. True for posted, False for unposted, null for all.", "required": false, "schema": {"type": "boolean"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageGeneralLedgerEntryDto"}}}}}}}, "/api/v1/accounting/general-ledger/trial-balance": {"get": {"tags": ["General <PERSON><PERSON>"], "summary": "Generate Trial Balance", "description": "Generates a Trial Balance report for a specified date range.", "operationId": "getTrialBalance", "parameters": [{"name": "startDate", "in": "query", "description": "Start date of the period (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "End date of the period (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialBalanceDto"}}}}}}}, "/api/v1/accounting/fees/applicable": {"get": {"tags": ["Fees"], "summary": "Find applicable Fees for a student context", "description": "Retrieves fees applicable based on academic year, grade, stage, and branch.", "operationId": "findApplicableFees", "parameters": [{"name": "academicYear", "in": "query", "description": "Academic year", "required": true, "schema": {"type": "string"}}, {"name": "gradeId", "in": "query", "description": "Student's Grade Level ID", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "stageId", "in": "query", "description": "Student's Educational Stage ID", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "branchId", "in": "query", "description": "Student's Branch ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Applicable fees retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FeeDto"}}}}}}}}, "/api/v1/accounting/fee-categories/all": {"get": {"tags": ["Fee Categories"], "summary": "Get all Fee Categories (Simple List)", "description": "Retrieves a list of all fee categories with basic information (ID, names).", "operationId": "getAllFeeCategoriesSimple", "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Categories retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleFeeCategoryDto"}}}}}}}}, "/api/v1/accounting/expense-categories/name/{name}": {"get": {"tags": ["Expense Categories"], "summary": "Get Expense Category by Name", "operationId": "getExpenseCategoryByName", "parameters": [{"name": "name", "in": "path", "description": "Name of the category", "required": true, "schema": {"type": "string"}}], "responses": {"404": {"description": "Category not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Category found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExpenseCategoryDto"}}}}}}}, "/api/v1/accounting/expense-categories/all": {"get": {"tags": ["Expense Categories"], "summary": "Get all Expense Categories (List)", "operationId": "getAllExpenseCategoriesList", "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Categories retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExpenseCategoryDto"}}}}}}}}, "/api/v1/accounting/discounts/active": {"get": {"tags": ["Discounts"], "summary": "Get all active Discounts", "operationId": "getAllActiveDiscounts", "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Active discounts retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DiscountDto"}}}}}}}}, "/api/v1/accounting/chart-of-accounts/number/{accountNumber}": {"get": {"tags": ["Chart of Accounts"], "summary": "Get Chart of Account by Account Number", "description": "Retrieves a specific account by its unique account number.", "operationId": "getChartOfAccountByNumber", "parameters": [{"name": "accountNumber", "in": "path", "description": "Account number to retrieve", "required": true, "schema": {"type": "string"}}], "responses": {"404": {"description": "Account not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Account found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChartOfAccountDto"}}}}}}}, "/api/v1/accounting/chart-of-accounts/active": {"get": {"tags": ["Chart of Accounts"], "summary": "Get all active Chart of Accounts (Paginated)", "description": "Retrieves a paginated list of all currently active accounts. Supports searching by account number, English name, Arabic name, category, and sorting.", "operationId": "getAllActiveChartOfAccounts", "parameters": [{"name": "accountNumber", "in": "query", "description": "Search by account number (partial match)", "required": false, "schema": {"type": "string"}}, {"name": "nameEn", "in": "query", "description": "Search by English name (partial match, case-insensitive)", "required": false, "schema": {"type": "string"}}, {"name": "nameAr", "in": "query", "description": "Search by Arabic name (partial match, case-insensitive)", "required": false, "schema": {"type": "string"}}, {"name": "category", "in": "query", "description": "Filter by accounting category (e.g., ASSET, LIABILITY)", "required": false, "schema": {"type": "string", "enum": ["ASSET", "LIABILITY", "EQUITY", "REVENUE", "EXPENSE"]}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"minimum": 0, "type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"minimum": 1, "type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Active accounts retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageChartOfAccountDto"}}}}}}}, "/api/v1/accounting/budgets/account/{chartOfAccountId}/year/{fiscalYear}": {"get": {"tags": ["Budgets"], "summary": "Get Budget by Account and Fiscal Year", "operationId": "getBudgetByAccountAndPeriod", "parameters": [{"name": "chartOfAccountId", "in": "path", "description": "ID of the Chart of Account", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "academicYear", "in": "query", "description": "Academic year (e.g., 2024-2025)", "required": true, "schema": {"type": "string"}}, {"name": "period", "in": "query", "description": "Budget period (e.g., ANNUAL, Q1)", "required": true, "schema": {"type": "string"}}], "responses": {"404": {"description": "Budget not found for this combination", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Budget found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BudgetDto"}}}}}}}, "/api/v1/accounting/balance-sheet": {"get": {"tags": ["balance-sheet-controller"], "operationId": "getBalanceSheet", "parameters": [{"name": "fromDate", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "toDate", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BalanceSheetDto"}}}}}}}, "/api/v1/accounting/ai-reports/{id}": {"get": {"tags": ["AI Reports"], "summary": "Get AI Report by ID", "description": "Retrieves a single AI-generated report by its ID.", "operationId": "getReportById", "parameters": [{"name": "id", "in": "path", "description": "ID of the report to retrieve", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"404": {"description": "Report not found"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Error retrieving report"}, "200": {"description": "Report retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AIReportResponse"}}}}}}}, "/api/v1/students/{studentId}/unlink-guardian": {"delete": {"tags": ["Student Management"], "summary": "Unlink student from guardian", "description": "Requires ADMIN role.", "operationId": "unlink<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Student not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Student unlinked from guardian", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentDto"}}}}}, "security": [{"Bearer Authentication": []}]}}}, "components": {"schemas": {"ErrorResponse": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "status": {"type": "integer", "format": "int32"}, "error": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}, "fieldErrors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}, "UpdateStudentProfileRequest": {"type": "object", "properties": {"dateOfBirth": {"type": "string", "format": "date"}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}, "address": {"maxLength": 255, "minLength": 0, "type": "string"}, "nationalId": {"maxLength": 50, "minLength": 0, "type": "string"}, "idType": {"type": "string", "enum": ["NATIONAL_ID", "PASSPORT"]}, "guardianType": {"type": "string", "enum": ["FATHER", "MOTHER", "BROTHER", "SISTER", "UNCLE", "AUNT", "GRANDFATHER", "GRANDMOTHER", "OTHER", "SELF"]}}}, "SimpleGuardianDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "occupation": {"type": "string"}, "nationalId": {"type": "string"}, "idType": {"type": "string", "enum": ["NATIONAL_ID", "PASSPORT"]}}}, "StudentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userAccount": {"$ref": "#/components/schemas/UserDto"}, "admissionNumber": {"type": "string"}, "dateOfBirth": {"type": "string", "format": "date"}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}, "address": {"type": "string"}, "nationalId": {"type": "string"}, "idType": {"type": "string", "enum": ["NATIONAL_ID", "PASSPORT"]}, "guardian": {"$ref": "#/components/schemas/SimpleGuardianDto"}, "guardianType": {"type": "string", "enum": ["FATHER", "MOTHER", "BROTHER", "SISTER", "UNCLE", "AUNT", "GRANDFATHER", "GRANDMOTHER", "OTHER", "SELF"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "enabled": {"type": "boolean"}, "accountLocked": {"type": "boolean"}, "accountExpired": {"type": "boolean"}, "credentialsExpired": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UpdateGuardianProfileRequest": {"type": "object", "properties": {"occupation": {"maxLength": 100, "minLength": 0, "type": "string"}, "address": {"maxLength": 255, "minLength": 0, "type": "string"}, "nationalId": {"maxLength": 50, "minLength": 0, "type": "string"}, "idType": {"type": "string", "enum": ["NATIONAL_ID", "PASSPORT"]}}}, "GuardianDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userAccount": {"$ref": "#/components/schemas/UserDto"}, "occupation": {"type": "string"}, "address": {"type": "string"}, "nationalId": {"type": "string"}, "idType": {"type": "string", "enum": ["NATIONAL_ID", "PASSPORT"]}, "students": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleStudentDto"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "SimpleStudentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "admissionNumber": {"type": "string"}, "dateOfBirth": {"type": "string", "format": "date"}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}}}, "UpdateTermRequest": {"type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}, "HolidayDto": {"required": ["endDate", "nameAr", "nameEn", "startDate", "termId"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"maxLength": 150, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 150, "minLength": 0, "type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "termId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "TermDto": {"required": ["academicYearId", "endDate", "name", "startDate"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"maxLength": 100, "minLength": 0, "type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "academicYearId": {"type": "string", "format": "uuid"}, "holidays": {"type": "array", "items": {"$ref": "#/components/schemas/HolidayDto"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UpdateSectionRequest": {"type": "object", "properties": {"name": {"maxLength": 50, "minLength": 0, "type": "string"}, "capacity": {"type": "integer", "format": "int32"}}}, "SectionDto": {"required": ["academicYearId", "branchId", "gradeLevelId", "name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "gradeLevelId": {"type": "string", "format": "uuid"}, "branchId": {"type": "string", "format": "uuid"}, "academicYearId": {"type": "string", "format": "uuid"}, "capacity": {"type": "integer", "format": "int32"}, "gradeLevelNameEn": {"type": "string"}, "gradeLevelNameAr": {"type": "string"}, "branchNameEn": {"type": "string"}, "branchNameAr": {"type": "string"}, "academicYearName": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UpdateRoleRequest": {"type": "object", "properties": {"name": {"maxLength": 50, "minLength": 3, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string"}, "permissionNames": {"type": "array", "items": {"type": "string"}}}}, "PermissionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "RoleDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionDto"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UpdatePermissionRequest": {"type": "object", "properties": {"name": {"maxLength": 100, "minLength": 3, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string"}}}, "UpdateHolidayRequest": {"type": "object", "properties": {"nameEn": {"maxLength": 150, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 150, "minLength": 0, "type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}, "UpdateGradeLevelRequest": {"type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "levelOrder": {"type": "integer", "format": "int32"}}}, "GradeLevelDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "levelOrder": {"type": "integer", "format": "int32"}, "educationalStageId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UpdateEducationalStageRequest": {"type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "sortOrder": {"type": "integer", "format": "int32"}}}, "EducationalStageDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "sortOrder": {"type": "integer", "format": "int32"}, "gradeLevels": {"type": "array", "items": {"$ref": "#/components/schemas/GradeLevelDto"}}, "branches": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleBranchDto"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "SimpleBranchDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}}}, "UpdateBranchRequest": {"type": "object", "properties": {"nameEn": {"maxLength": 150, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 150, "minLength": 0, "type": "string"}, "address": {"maxLength": 255, "minLength": 0, "type": "string"}}}, "BranchDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "address": {"type": "string"}, "educationalStages": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleEducationalStageDto"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "SimpleEducationalStageDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "sortOrder": {"type": "integer", "format": "int32"}}}, "UpdateAcademicYearRequest": {"type": "object", "properties": {"name": {"maxLength": 50, "minLength": 0, "type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}, "AcademicYearDto": {"required": ["endDate", "name", "startDate"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "active": {"type": "boolean"}, "terms": {"type": "array", "items": {"$ref": "#/components/schemas/TermDto"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UpdateTaxRequest": {"required": ["chartOfAccountId", "nameAr", "nameEn", "percent"], "type": "object", "properties": {"nameAr": {"type": "string"}, "nameEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "percent": {"maximum": 100.0, "exclusiveMaximum": false, "minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "chartOfAccountId": {"type": "string", "format": "uuid"}}}, "SimpleChartOfAccountDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "accountNumber": {"type": "string"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}}}, "TaxDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameAr": {"type": "string"}, "nameEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "percent": {"type": "number"}, "chartOfAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}}}, "UpdateStudentFeeRequest": {"required": ["status"], "type": "object", "properties": {"dueDate": {"type": "string", "format": "date"}, "amount": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "amountPaid": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "discountIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "status": {"type": "string", "enum": ["UNPAID", "PARTIALLY_PAID", "PAID", "OVERDUE", "WAIVED", "CANCELLED"]}, "notesEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "notesAr": {"maxLength": 500, "minLength": 0, "type": "string"}}}, "SimpleDiscountDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}, "type": {"type": "string", "enum": ["PERCENTAGE", "FIXED_AMOUNT"]}, "value": {"type": "number"}}}, "SimpleFeeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "amount": {"type": "number"}, "academicYear": {"type": "string"}, "dueDate": {"type": "string", "format": "date"}}}, "StudentFeeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "student": {"$ref": "#/components/schemas/SimpleStudentDto"}, "fee": {"$ref": "#/components/schemas/SimpleFeeDto"}, "dueDate": {"type": "string", "format": "date"}, "amount": {"type": "number"}, "amountPaid": {"type": "number"}, "amountDue": {"type": "number"}, "appliedDiscounts": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleDiscountDto"}}, "discountAmount": {"type": "number"}, "status": {"type": "string", "enum": ["UNPAID", "PARTIALLY_PAID", "PAID", "OVERDUE", "WAIVED", "CANCELLED"]}, "notesEn": {"type": "string"}, "notesAr": {"type": "string"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "UpdatePaymentMethodRequest": {"required": ["active", "nameAr", "nameEn", "type"], "type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "type": {"type": "string", "enum": ["CASH", "BANK_TRANSFER", "CHEQUE", "CREDIT_CARD", "DEBIT_CARD", "ONLINE_PAYMENT_GATEWAY", "MOBILE_MONEY"]}, "descriptionEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 500, "minLength": 0, "type": "string"}, "active": {"type": "boolean"}}}, "PaymentMethodDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "type": {"type": "string", "enum": ["CASH", "BANK_TRANSFER", "CHEQUE", "CREDIT_CARD", "DEBIT_CARD", "ONLINE_PAYMENT_GATEWAY", "MOBILE_MONEY"]}, "descriptionEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "active": {"type": "boolean"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "JournalEntryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "entryDate": {"type": "string", "format": "date"}, "description": {"type": "string"}, "referenceNumber": {"type": "string"}, "lines": {"type": "array", "items": {"$ref": "#/components/schemas/JournalEntryLineDto"}}, "postedDate": {"type": "string", "format": "date-time"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "JournalEntryLineDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "chartOfAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "type": {"type": "string", "enum": ["DEBIT", "CREDIT"]}, "amount": {"type": "number"}, "description": {"type": "string"}}}, "CreateJournalEntryLineRequest": {"required": ["amount", "chartOfAccountId", "type"], "type": "object", "properties": {"chartOfAccountId": {"type": "string", "format": "uuid"}, "type": {"type": "string", "enum": ["DEBIT", "CREDIT"]}, "amount": {"minimum": 0.0, "exclusiveMinimum": true, "type": "number"}, "description": {"maxLength": 500, "minLength": 0, "type": "string"}}}, "UpdateJournalEntryRequest": {"required": ["description", "entryDate"], "type": "object", "properties": {"entryDate": {"type": "string", "format": "date"}, "description": {"maxLength": 500, "minLength": 0, "type": "string"}, "referenceNumber": {"maxLength": 100, "minLength": 0, "type": "string"}, "lines": {"type": "array", "items": {"$ref": "#/components/schemas/CreateJournalEntryLineRequest"}}}}, "UpdateItemRequest": {"required": ["active", "costPrice", "nameAr", "nameEn", "sellingPrice", "type", "unitOfMeasure"], "type": "object", "properties": {"nameEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 1000, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 1000, "minLength": 0, "type": "string"}, "type": {"type": "string", "enum": ["CONSUMABLE", "NON_CONSUMABLE", "FIXED_ASSET", "SERVICE"]}, "unitOfMeasure": {"maxLength": 50, "minLength": 0, "type": "string"}, "costPrice": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "sellingPrice": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "inventoryAccountId": {"type": "string", "format": "uuid"}, "cogsAccountId": {"type": "string", "format": "uuid"}, "salesAccountId": {"type": "string", "format": "uuid"}, "reorderLevel": {"type": "integer", "format": "int32"}, "supplierInfo": {"maxLength": 255, "minLength": 0, "type": "string"}, "active": {"type": "boolean"}}}, "ItemDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "itemCode": {"type": "string"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "type": {"type": "string", "enum": ["CONSUMABLE", "NON_CONSUMABLE", "FIXED_ASSET", "SERVICE"]}, "unitOfMeasure": {"type": "string"}, "costPrice": {"type": "number"}, "sellingPrice": {"type": "number"}, "inventoryAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "cogsAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "salesAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "reorderLevel": {"type": "integer", "format": "int32"}, "supplierInfo": {"type": "string"}, "active": {"type": "boolean"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "UpdateFixedAssetRequest": {"required": ["assetCode", "nameAr", "nameEn", "status"], "type": "object", "properties": {"nameEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "assetCode": {"maxLength": 50, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 1000, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 1000, "minLength": 0, "type": "string"}, "assetAccountId": {"type": "string", "format": "uuid"}, "accumulatedDepreciationAccountId": {"type": "string", "format": "uuid"}, "depreciationExpenseAccountId": {"type": "string", "format": "uuid"}, "usefulLifeYears": {"type": "integer", "format": "int32"}, "salvageValue": {"type": "number"}, "depreciationMethod": {"type": "string", "enum": ["STRAIGHT_LINE", "DOUBLE_DECLINING_BALANCE", "SUM_OF_YEARS_DIGITS", "UNITS_OF_PRODUCTION"]}, "location": {"maxLength": 255, "minLength": 0, "type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "UNDER_MAINTENANCE", "DISPOSED", "SOLD", "RETIRED"]}, "disposalDate": {"type": "string", "format": "date"}, "disposalValue": {"type": "number"}}}, "FixedAssetDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "assetCode": {"type": "string"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "acquisitionDate": {"type": "string", "format": "date"}, "acquisitionCost": {"type": "number"}, "assetAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "accumulatedDepreciationAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "depreciationExpenseAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "usefulLifeYears": {"type": "integer", "format": "int32"}, "salvageValue": {"type": "number"}, "depreciationMethod": {"type": "string", "enum": ["STRAIGHT_LINE", "DOUBLE_DECLINING_BALANCE", "SUM_OF_YEARS_DIGITS", "UNITS_OF_PRODUCTION"]}, "location": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "UNDER_MAINTENANCE", "DISPOSED", "SOLD", "RETIRED"]}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "UpdateFeeRequest": {"required": ["academicYear", "active", "amount", "dueDate", "feeCategoryId", "nameAr", "nameEn"], "type": "object", "properties": {"nameEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 1000, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 1000, "minLength": 0, "type": "string"}, "amount": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "academicYear": {"maxLength": 50, "minLength": 0, "type": "string"}, "dueDate": {"type": "string", "format": "date"}, "feeCategoryId": {"type": "string", "format": "uuid"}, "applicableGradeId": {"type": "string", "format": "uuid"}, "applicableStageId": {"type": "string", "format": "uuid"}, "applicableBranchId": {"type": "string", "format": "uuid"}, "active": {"type": "boolean"}}}, "FeeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "amount": {"type": "number"}, "academicYear": {"type": "string"}, "dueDate": {"type": "string", "format": "date"}, "feeCategory": {"$ref": "#/components/schemas/SimpleFeeCategoryDto"}, "applicableGradeId": {"type": "string", "format": "uuid"}, "applicableStageId": {"type": "string", "format": "uuid"}, "applicableBranchId": {"type": "string", "format": "uuid"}, "active": {"type": "boolean"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "SimpleFeeCategoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}}}, "UpdateFeeCategoryRequest": {"required": ["nameAr", "nameEn"], "type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 500, "minLength": 0, "type": "string"}}}, "FeeCategoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "UpdateExpenseRequest": {"required": ["amount", "categoryId", "descriptionAr", "descriptionEn", "expenseDate", "paymentAccountId"], "type": "object", "properties": {"expenseDate": {"type": "string", "format": "date"}, "amount": {"minimum": 0.0, "exclusiveMinimum": true, "type": "number"}, "descriptionEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 500, "minLength": 0, "type": "string"}, "vendor": {"maxLength": 255, "minLength": 0, "type": "string"}, "referenceNumber": {"maxLength": 100, "minLength": 0, "type": "string"}, "categoryId": {"type": "string", "format": "uuid"}, "paymentAccountId": {"type": "string", "format": "uuid"}, "taxId": {"type": "string", "format": "uuid"}}}, "ExpenseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "expenseDate": {"type": "string", "format": "date"}, "amountBeforeTax": {"type": "number"}, "taxAmount": {"type": "number"}, "totalAmount": {"type": "number"}, "descriptionEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "vendor": {"type": "string"}, "referenceNumber": {"type": "string"}, "category": {"$ref": "#/components/schemas/SimpleExpenseCategoryDto"}, "tax": {"$ref": "#/components/schemas/SimpleTaxDto"}, "paymentAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "SimpleExpenseCategoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}}}, "SimpleTaxDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "percent": {"type": "number"}}}, "UpdateExpenseCategoryRequest": {"required": ["expenseAccountId", "nameAr", "nameEn"], "type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 500, "minLength": 0, "type": "string"}, "parentCategoryId": {"type": "string", "format": "uuid"}, "expenseAccountId": {"type": "string", "format": "uuid"}}}, "ExpenseCategoryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "parentCategory": {"$ref": "#/components/schemas/SimpleExpenseCategoryDto"}, "expenseAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "UpdateDiscountRequest": {"required": ["active", "code", "descriptionAr", "descriptionEn", "type", "value"], "type": "object", "properties": {"code": {"maxLength": 50, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "type": {"type": "string", "enum": ["PERCENTAGE", "FIXED_AMOUNT"]}, "value": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "validFrom": {"type": "string", "format": "date"}, "validUntil": {"type": "string", "format": "date"}, "applicableFeeId": {"type": "string", "format": "uuid"}, "applicableStudentId": {"type": "string", "format": "uuid"}, "active": {"type": "boolean"}}}, "DiscountDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}, "descriptionEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "type": {"type": "string", "enum": ["PERCENTAGE", "FIXED_AMOUNT"]}, "value": {"type": "number"}, "validFrom": {"type": "string", "format": "date"}, "validUntil": {"type": "string", "format": "date"}, "applicableFeeId": {"type": "string", "format": "uuid"}, "applicableStudentId": {"type": "string", "format": "uuid"}, "active": {"type": "boolean"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "ChartOfAccountDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "accountNumber": {"type": "string"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "category": {"type": "string", "enum": ["ASSET", "LIABILITY", "EQUITY", "REVENUE", "EXPENSE"]}, "active": {"type": "boolean"}, "parentAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "UpdateChartOfAccountRequest": {"required": ["active", "category", "nameAr", "nameEn"], "type": "object", "properties": {"nameEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 1000, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 1000, "minLength": 0, "type": "string"}, "category": {"type": "string", "enum": ["ASSET", "LIABILITY", "EQUITY", "REVENUE", "EXPENSE"]}, "active": {"type": "boolean"}, "parentAccountId": {"type": "string", "format": "uuid"}}}, "UpdateBudgetRequest": {"required": ["budgetedAmount"], "type": "object", "properties": {"budgetedAmount": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "notes": {"maxLength": 500, "minLength": 0, "type": "string"}}}, "BudgetDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "academicYear": {"type": "string"}, "period": {"type": "string"}, "chartOfAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}, "expenseCategory": {"$ref": "#/components/schemas/SimpleExpenseCategoryDto"}, "budgetedAmount": {"type": "number"}, "notes": {"type": "string"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "CreateStudentProfileRequest": {"required": ["dateOfBirth", "email", "firstName", "gender", "guardianType", "idType", "lastName", "nationalId", "password"], "type": "object", "properties": {"firstName": {"maxLength": 50, "minLength": 2, "type": "string"}, "lastName": {"maxLength": 50, "minLength": 2, "type": "string"}, "email": {"maxLength": 100, "minLength": 0, "type": "string"}, "password": {"maxLength": 100, "minLength": 8, "type": "string"}, "phoneNumber": {"maxLength": 20, "minLength": 0, "type": "string"}, "dateOfBirth": {"type": "string", "format": "date"}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}, "address": {"maxLength": 255, "minLength": 0, "type": "string"}, "nationalId": {"maxLength": 50, "minLength": 0, "type": "string"}, "idType": {"type": "string", "enum": ["NATIONAL_ID", "PASSPORT"]}, "guardianId": {"type": "string", "format": "uuid"}, "guardianType": {"type": "string", "enum": ["FATHER", "MOTHER", "BROTHER", "SISTER", "UNCLE", "AUNT", "GRANDFATHER", "GRANDMOTHER", "OTHER", "SELF"]}}}, "CreateGuardianWithUserRequest": {"required": ["email", "firstName", "idType", "lastName", "nationalId", "occupation", "password"], "type": "object", "properties": {"firstName": {"maxLength": 50, "minLength": 2, "type": "string"}, "lastName": {"maxLength": 50, "minLength": 2, "type": "string"}, "email": {"maxLength": 100, "minLength": 0, "type": "string"}, "password": {"maxLength": 100, "minLength": 8, "type": "string"}, "phoneNumber": {"maxLength": 20, "minLength": 0, "type": "string"}, "occupation": {"maxLength": 100, "minLength": 0, "type": "string"}, "address": {"maxLength": 255, "minLength": 0, "type": "string"}, "nationalId": {"maxLength": 50, "minLength": 0, "type": "string"}, "idType": {"type": "string", "enum": ["NATIONAL_ID", "PASSPORT"]}}}, "CreateGuardianProfileRequest": {"required": ["idType", "nationalId", "occupation", "userId"], "type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "occupation": {"maxLength": 100, "minLength": 0, "type": "string"}, "address": {"maxLength": 255, "minLength": 0, "type": "string"}, "nationalId": {"maxLength": 50, "minLength": 0, "type": "string"}, "idType": {"type": "string", "enum": ["NATIONAL_ID", "PASSPORT"]}}}, "ResetPasswordRequest": {"required": ["newPassword", "token"], "type": "object", "properties": {"token": {"type": "string"}, "newPassword": {"maxLength": 100, "minLength": 8, "type": "string"}}}, "ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}, "RegisterRequest": {"required": ["email", "firstName", "lastName", "password"], "type": "object", "properties": {"firstName": {"maxLength": 50, "minLength": 2, "type": "string"}, "lastName": {"maxLength": 50, "minLength": 2, "type": "string"}, "email": {"maxLength": 100, "minLength": 0, "type": "string"}, "password": {"maxLength": 100, "minLength": 8, "type": "string"}, "phoneNumber": {"maxLength": 20, "minLength": 0, "type": "string"}, "roleName": {"maxLength": 50, "minLength": 0, "type": "string"}}}, "LoginRequest": {"required": ["email", "password"], "type": "object", "properties": {"email": {"maxLength": 100, "minLength": 0, "type": "string"}, "password": {"type": "string"}}}, "AuthResponse": {"type": "object", "properties": {"accessToken": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}}, "permissions": {"type": "array", "items": {"type": "string"}}}}, "ForgotPasswordRequest": {"required": ["email"], "type": "object", "properties": {"email": {"maxLength": 100, "minLength": 0, "type": "string"}}}, "CreateTermRequest": {"required": ["academicYearId", "endDate", "name", "startDate"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "academicYearId": {"type": "string", "format": "uuid"}}}, "CreateSectionRequest": {"required": ["academicYearId", "branchId", "gradeLevelId", "name"], "type": "object", "properties": {"name": {"maxLength": 50, "minLength": 0, "type": "string"}, "gradeLevelId": {"type": "string", "format": "uuid"}, "branchId": {"type": "string", "format": "uuid"}, "academicYearId": {"type": "string", "format": "uuid"}, "capacity": {"type": "integer", "format": "int32"}}}, "CreateRoleRequest": {"required": ["name", "permissionNames"], "type": "object", "properties": {"name": {"maxLength": 50, "minLength": 3, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string"}, "permissionNames": {"type": "array", "items": {"type": "string"}}}}, "CreatePermissionRequest": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 3, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string"}}}, "CreateHolidayRequest": {"required": ["endDate", "nameAr", "nameEn", "startDate", "termId"], "type": "object", "properties": {"nameEn": {"maxLength": 150, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 150, "minLength": 0, "type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "termId": {"type": "string", "format": "uuid"}}}, "CreateGradeLevelRequest": {"required": ["educationalStageId", "levelOrder", "nameAr", "nameEn"], "type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "levelOrder": {"type": "integer", "format": "int32"}, "educationalStageId": {"type": "string", "format": "uuid"}}}, "CreateEducationalStageRequest": {"required": ["nameAr", "nameEn", "sortOrder"], "type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "sortOrder": {"type": "integer", "format": "int32"}}}, "CreateBranchRequest": {"required": ["nameAr", "nameEn"], "type": "object", "properties": {"nameEn": {"maxLength": 150, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 150, "minLength": 0, "type": "string"}, "address": {"maxLength": 255, "minLength": 0, "type": "string"}}}, "CreateAcademicYearRequest": {"required": ["endDate", "name", "startDate"], "type": "object", "properties": {"name": {"maxLength": 50, "minLength": 0, "type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}, "CreateTaxRequest": {"required": ["chartOfAccountId", "nameAr", "nameEn", "percent"], "type": "object", "properties": {"nameAr": {"type": "string"}, "nameEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "percent": {"maximum": 100.0, "exclusiveMaximum": false, "minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "chartOfAccountId": {"type": "string", "format": "uuid"}}}, "AssignFeeRequest": {"required": ["feeId", "studentId"], "type": "object", "properties": {"studentId": {"type": "string", "format": "uuid"}, "feeId": {"type": "string", "format": "uuid"}, "dueDate": {"type": "string", "format": "date"}, "amount": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "discountId": {"type": "string", "format": "uuid"}, "notesEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "notesAr": {"maxLength": 500, "minLength": 0, "type": "string"}}}, "CreatePaymentDetailRequest": {"required": ["allocatedAmount", "studentFeeId"], "type": "object", "properties": {"studentFeeId": {"type": "string", "format": "uuid"}, "allocatedAmount": {"minimum": 0.0, "exclusiveMinimum": true, "type": "number"}}}, "CreateReceiptRequest": {"required": ["paymentMethodId", "payments", "receiptDate", "studentId", "totalAmountReceived"], "type": "object", "properties": {"studentId": {"type": "string", "format": "uuid"}, "receiptDate": {"type": "string", "format": "date"}, "paymentMethodId": {"type": "string", "format": "uuid"}, "totalAmountReceived": {"minimum": 0.0, "exclusiveMinimum": true, "type": "number"}, "referenceNumber": {"maxLength": 100, "minLength": 0, "type": "string"}, "notes": {"maxLength": 1000, "minLength": 0, "type": "string"}, "payments": {"type": "array", "items": {"$ref": "#/components/schemas/CreatePaymentDetailRequest"}}}}, "PaymentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "receiptId": {"type": "string", "format": "uuid"}, "studentFee": {"$ref": "#/components/schemas/SimpleStudentFeeDto"}, "paymentDate": {"type": "string", "format": "date"}, "amount": {"type": "number"}, "createdDate": {"type": "string", "format": "date-time"}}}, "ReceiptDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "receiptNumber": {"type": "string"}, "student": {"$ref": "#/components/schemas/SimpleStudentDto"}, "receiptDate": {"type": "string", "format": "date"}, "paymentMethod": {"$ref": "#/components/schemas/SimplePaymentMethodDto"}, "totalAmountReceived": {"type": "number"}, "referenceNumber": {"type": "string"}, "notes": {"type": "string"}, "payments": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentDto"}}, "cancelled": {"type": "boolean"}, "cancellationReason": {"type": "string"}, "cancellationDate": {"type": "string", "format": "date"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "SimplePaymentMethodDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "type": {"type": "string", "enum": ["CASH", "BANK_TRANSFER", "CHEQUE", "CREDIT_CARD", "DEBIT_CARD", "ONLINE_PAYMENT_GATEWAY", "MOBILE_MONEY"]}}}, "SimpleStudentFeeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "studentId": {"type": "string", "format": "uuid"}, "fee": {"$ref": "#/components/schemas/SimpleFeeDto"}, "amountDue": {"type": "number"}, "status": {"type": "string", "enum": ["UNPAID", "PARTIALLY_PAID", "PAID", "OVERDUE", "WAIVED", "CANCELLED"]}}}, "CreatePaymentMethodRequest": {"required": ["nameAr", "nameEn", "type"], "type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "type": {"type": "string", "enum": ["CASH", "BANK_TRANSFER", "CHEQUE", "CREDIT_CARD", "DEBIT_CARD", "ONLINE_PAYMENT_GATEWAY", "MOBILE_MONEY"]}, "descriptionEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 500, "minLength": 0, "type": "string"}}}, "CreateJournalEntryRequest": {"required": ["description", "entryDate", "lines"], "type": "object", "properties": {"entryDate": {"type": "string", "format": "date"}, "description": {"maxLength": 500, "minLength": 0, "type": "string"}, "referenceNumber": {"maxLength": 100, "minLength": 0, "type": "string"}, "lines": {"type": "array", "items": {"$ref": "#/components/schemas/CreateJournalEntryLineRequest"}}}}, "CreateItemRequest": {"required": ["cogsAccountId", "costPrice", "inventoryAccountId", "itemCode", "nameAr", "nameEn", "salesAccountId", "sellingPrice", "type", "unitOfMeasure"], "type": "object", "properties": {"itemCode": {"maxLength": 50, "minLength": 0, "type": "string"}, "nameEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 1000, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 1000, "minLength": 0, "type": "string"}, "type": {"type": "string", "enum": ["CONSUMABLE", "NON_CONSUMABLE", "FIXED_ASSET", "SERVICE"]}, "unitOfMeasure": {"maxLength": 50, "minLength": 0, "type": "string"}, "costPrice": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "sellingPrice": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "inventoryAccountId": {"type": "string", "format": "uuid"}, "cogsAccountId": {"type": "string", "format": "uuid"}, "salesAccountId": {"type": "string", "format": "uuid"}, "reorderLevel": {"type": "integer", "format": "int32"}, "supplierInfo": {"maxLength": 255, "minLength": 0, "type": "string"}}}, "InventoryAdjustmentRequest": {"required": ["itemId", "quantity", "transactionDate", "transactionType"], "type": "object", "properties": {"itemId": {"type": "string", "format": "uuid"}, "transactionDate": {"type": "string", "format": "date"}, "transactionType": {"type": "string", "enum": ["PURCHASE_RECEIPT", "USAGE", "SALE", "TRANSFER_IN", "TRANSFER_OUT", "ADJUSTMENT_POSITIVE", "ADJUSTMENT_NEGATIVE", "RETURN_TO_SUPPLIER", "OPENING_BALANCE"]}, "quantity": {"minimum": 0.0, "exclusiveMinimum": true, "type": "number"}, "unitCost": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "notes": {"maxLength": 1000, "minLength": 0, "type": "string"}, "referenceId": {"maxLength": 100, "minLength": 0, "type": "string"}}}, "InventoryTransactionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "item": {"$ref": "#/components/schemas/SimpleItemDto"}, "transactionDate": {"type": "string", "format": "date"}, "transactionType": {"type": "string", "enum": ["PURCHASE_RECEIPT", "USAGE", "SALE", "TRANSFER_IN", "TRANSFER_OUT", "ADJUSTMENT_POSITIVE", "ADJUSTMENT_NEGATIVE", "RETURN_TO_SUPPLIER", "OPENING_BALANCE"]}, "quantityChange": {"type": "number"}, "quantityAfterTransaction": {"type": "number"}, "unitCost": {"type": "number"}, "notes": {"type": "string"}, "referenceId": {"type": "string"}, "createdDate": {"type": "string", "format": "date-time"}}}, "SimpleItemDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "itemCode": {"type": "string"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}, "unitOfMeasure": {"type": "string"}}}, "CreateFixedAssetRequest": {"required": ["accumulatedDepreciationAccountId", "acquisitionCost", "acquisitionDate", "assetAccountId", "assetCode", "depreciationExpenseAccountId", "depreciationMethod", "nameAr", "nameEn", "salvageValue", "status", "usefulLifeYears"], "type": "object", "properties": {"assetCode": {"maxLength": 50, "minLength": 0, "type": "string"}, "nameEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 1000, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 1000, "minLength": 0, "type": "string"}, "acquisitionDate": {"type": "string", "format": "date"}, "acquisitionCost": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "assetAccountId": {"type": "string", "format": "uuid"}, "accumulatedDepreciationAccountId": {"type": "string", "format": "uuid"}, "depreciationExpenseAccountId": {"type": "string", "format": "uuid"}, "usefulLifeYears": {"type": "integer", "format": "int32"}, "salvageValue": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "depreciationMethod": {"type": "string", "enum": ["STRAIGHT_LINE", "DOUBLE_DECLINING_BALANCE", "SUM_OF_YEARS_DIGITS", "UNITS_OF_PRODUCTION"]}, "location": {"maxLength": 255, "minLength": 0, "type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "UNDER_MAINTENANCE", "DISPOSED", "SOLD", "RETIRED"]}}}, "CreateFeeRequest": {"required": ["academicYear", "amount", "dueDate", "feeCategoryId", "nameAr", "nameEn"], "type": "object", "properties": {"nameEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 1000, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 1000, "minLength": 0, "type": "string"}, "amount": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "academicYear": {"maxLength": 50, "minLength": 0, "type": "string"}, "dueDate": {"type": "string", "format": "date"}, "feeCategoryId": {"type": "string", "format": "uuid"}, "applicableGradeId": {"type": "string", "format": "uuid"}, "applicableStageId": {"type": "string", "format": "uuid"}, "applicableBranchId": {"type": "string", "format": "uuid"}}}, "CreateFeeCategoryRequest": {"required": ["nameAr", "nameEn"], "type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 500, "minLength": 0, "type": "string"}}}, "CreateExpenseRequest": {"required": ["amount", "categoryId", "descriptionAr", "descriptionEn", "expenseDate", "paymentAccountId"], "type": "object", "properties": {"expenseDate": {"type": "string", "format": "date"}, "amount": {"minimum": 0.0, "exclusiveMinimum": true, "type": "number"}, "descriptionEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 500, "minLength": 0, "type": "string"}, "vendor": {"maxLength": 255, "minLength": 0, "type": "string"}, "referenceNumber": {"maxLength": 100, "minLength": 0, "type": "string"}, "categoryId": {"type": "string", "format": "uuid"}, "paymentAccountId": {"type": "string", "format": "uuid"}, "taxId": {"type": "string", "format": "uuid"}}}, "CreateExpenseCategoryRequest": {"required": ["expenseAccountId", "nameAr", "nameEn"], "type": "object", "properties": {"nameEn": {"maxLength": 100, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 500, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 500, "minLength": 0, "type": "string"}, "parentCategoryId": {"type": "string", "format": "uuid"}, "expenseAccountId": {"type": "string", "format": "uuid"}}}, "CreateDiscountRequest": {"required": ["code", "descriptionAr", "descriptionEn", "type", "value"], "type": "object", "properties": {"code": {"maxLength": 50, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "type": {"type": "string", "enum": ["PERCENTAGE", "FIXED_AMOUNT"]}, "value": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "validFrom": {"type": "string", "format": "date"}, "validUntil": {"type": "string", "format": "date"}, "applicableFeeId": {"type": "string", "format": "uuid"}, "applicableStudentId": {"type": "string", "format": "uuid"}}}, "CreateChartOfAccountRequest": {"required": ["accountNumber", "category", "nameAr", "nameEn"], "type": "object", "properties": {"accountNumber": {"maxLength": 50, "minLength": 0, "type": "string"}, "nameEn": {"maxLength": 255, "minLength": 0, "type": "string"}, "nameAr": {"maxLength": 255, "minLength": 0, "type": "string"}, "descriptionEn": {"maxLength": 1000, "minLength": 0, "type": "string"}, "descriptionAr": {"maxLength": 1000, "minLength": 0, "type": "string"}, "category": {"type": "string", "enum": ["ASSET", "LIABILITY", "EQUITY", "REVENUE", "EXPENSE"]}, "parentAccountId": {"type": "string", "format": "uuid"}}}, "CreateBudgetRequest": {"required": ["academicYear", "budgetedAmount", "period"], "type": "object", "properties": {"academicYear": {"maxLength": 50, "minLength": 0, "type": "string"}, "period": {"maxLength": 50, "minLength": 0, "type": "string"}, "chartOfAccountId": {"type": "string", "format": "uuid"}, "expenseCategoryId": {"type": "string", "format": "uuid"}, "budgetedAmount": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "notes": {"maxLength": 500, "minLength": 0, "type": "string"}}}, "AIReportRequest": {"required": ["endDate", "reportType", "startDate"], "type": "object", "properties": {"reportType": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "filters": {"type": "object", "additionalProperties": {"type": "object"}}}, "description": "Report request parameters"}, "AIReportResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "reportType": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "filters": {"type": "object", "additionalProperties": {"type": "object"}}, "content": {"type": "string"}, "status": {"type": "string", "enum": ["PENDING", "PROCESSING", "COMPLETED", "FAILED"]}, "fromCache": {"type": "boolean"}, "createdDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "PageUserDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "unpaged": {"type": "boolean"}, "paged": {"type": "boolean"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}}, "SortObject": {"type": "object", "properties": {"direction": {"type": "string"}, "nullHandling": {"type": "string"}, "ascending": {"type": "boolean"}, "property": {"type": "string"}, "ignoreCase": {"type": "boolean"}}}, "PageSectionDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/SectionDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "AccountingAuditLogDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier of the audit log entry", "format": "uuid", "example": "a1b2c3d4-e5f6-7890-1234-567890abcdef"}, "actionTimestamp": {"type": "string", "description": "Timestamp when the action occurred", "format": "date-time", "example": "2024-04-10T10:15:30Z"}, "userId": {"type": "string", "description": "ID of the user who performed the action", "format": "uuid", "example": "f0e9d8c7-b6a5-4321-fedc-ba9876543210"}, "actionType": {"type": "string", "description": "Type of action performed", "example": "CREATE", "enum": ["CREATE", "UPDATE", "DELETE", "ACTIVATE", "DEACTIVATE", "POST_JOURNAL_ENTRY", "UNPOST_JOURNAL_ENTRY", "RECORD_PAYMENT", "ALLOCATE_PAYMENT", "UNALLOCATE_PAYMENT", "ASSIGN_FEE", "UPDATE_FEE_STATUS", "APPLY_DISCOUNT", "REMOVE_DISCOUNT", "RECORD_EXPENSE", "UPDATE_BUDGET", "CREATE_FIXED_ASSET", "UPDATE_FIXED_ASSET_STATUS", "DEPRECIATE_ASSET", "RECORD_INVENTORY_TRANSACTION", "ADJUST_INVENTORY", "GENERATE_REPORT", "EXPORT_DATA", "IMPORT_DATA", "SYSTEM_PROCESS", "DISPOSE_ASSET"]}, "entityType": {"type": "string", "description": "Type of the entity affected (e.g., JournalEntry)", "example": "JournalEntry"}, "entityId": {"type": "string", "description": "ID of the entity affected", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}, "details": {"type": "string", "description": "Details about the action performed", "example": "Created Journal Entry ID: 123e4567-e89b-12d3-a456-************"}}, "description": "Data Transfer Object for Accounting Audit Log entries"}, "PageAccountingAuditLogDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AccountingAuditLogDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageAcademicYearDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AcademicYearDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "Pageable": {"type": "object", "properties": {"page": {"minimum": 0, "type": "integer", "format": "int32"}, "size": {"minimum": 1, "type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "PageTaxDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/TaxDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageStudentFeeDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/StudentFeeDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "ExpenseReportDto": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "expenses": {"type": "array", "items": {"$ref": "#/components/schemas/ExpenseDto"}}, "categorySummaries": {"type": "array", "items": {"$ref": "#/components/schemas/ExpenseReportSummaryDto"}}, "totalAmount": {"type": "number"}, "totalTaxAmount": {"type": "number"}, "totalAmountWithTax": {"type": "number"}, "totalExpenseCount": {"type": "integer", "format": "int32"}}}, "ExpenseReportSummaryDto": {"type": "object", "properties": {"categoryId": {"type": "string", "format": "uuid"}, "categoryNameEn": {"type": "string"}, "categoryNameAr": {"type": "string"}, "totalAmount": {"type": "number"}, "totalTaxAmount": {"type": "number"}, "totalAmountWithTax": {"type": "number"}, "expenseCount": {"type": "integer", "format": "int32"}, "percentageOfTotal": {"type": "number"}}}, "PageReceiptDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ReceiptDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PagePaymentMethodDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentMethodDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageJournalEntryDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/JournalEntryDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageItemDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ItemDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageInventoryTransactionDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryTransactionDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "AccountBalanceDto": {"type": "object", "properties": {"accountId": {"type": "string", "format": "uuid"}, "accountNumber": {"type": "string"}, "accountName": {"type": "string"}, "balance": {"type": "number"}}}, "IncomeStatementDto": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "incomeAccounts": {"type": "array", "items": {"$ref": "#/components/schemas/AccountBalanceDto"}}, "expenseAccounts": {"type": "array", "items": {"$ref": "#/components/schemas/AccountBalanceDto"}}, "totalRevenue": {"type": "number"}, "totalExpenses": {"type": "number"}, "netIncome": {"type": "number"}}}, "GeneralLedgerEntryDto": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "journalEntryId": {"type": "string", "format": "uuid"}, "accountCode": {"type": "string"}, "accountName": {"type": "string"}, "description": {"type": "string"}, "debit": {"type": "number"}, "credit": {"type": "number"}, "runningBalance": {"type": "number"}}}, "PageGeneralLedgerEntryDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/GeneralLedgerEntryDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "TrialBalanceAccountDto": {"type": "object", "properties": {"accountCode": {"type": "string"}, "accountName": {"type": "string"}, "debitBalance": {"type": "number"}, "creditBalance": {"type": "number"}}}, "TrialBalanceDto": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "accounts": {"type": "array", "items": {"$ref": "#/components/schemas/TrialBalanceAccountDto"}}, "totalDebits": {"type": "number"}, "totalCredits": {"type": "number"}}}, "PageFixedAssetDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/FixedAssetDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageFeeDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/FeeDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageFeeCategoryDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/FeeCategoryDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageExpenseDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ExpenseDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageExpenseCategoryDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ExpenseCategoryDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageDiscountDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/DiscountDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageChartOfAccountDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ChartOfAccountDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageBudgetDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/BudgetDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "BalanceSheetAccountDto": {"type": "object", "properties": {"accountId": {"type": "string", "format": "uuid"}, "accountNumber": {"type": "string"}, "accountNameEn": {"type": "string"}, "accountNameAr": {"type": "string"}, "debit": {"type": "number"}, "credit": {"type": "number"}, "balance": {"type": "number"}}}, "BalanceSheetDto": {"type": "object", "properties": {"reportDate": {"type": "string", "format": "date"}, "assets": {"$ref": "#/components/schemas/BalanceSheetSectionDto"}, "liabilities": {"$ref": "#/components/schemas/BalanceSheetSectionDto"}, "equity": {"$ref": "#/components/schemas/BalanceSheetSectionDto"}}}, "BalanceSheetSectionDto": {"type": "object", "properties": {"category": {"type": "string", "enum": ["ASSET", "LIABILITY", "EQUITY", "REVENUE", "EXPENSE"]}, "accounts": {"type": "array", "items": {"$ref": "#/components/schemas/BalanceSheetAccountDto"}}, "totalDebit": {"type": "number"}, "totalCredit": {"type": "number"}, "totalBalance": {"type": "number"}}}, "PageAIReportResponse": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AIReportResponse"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}}, "securitySchemes": {"Bearer Authentication": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}