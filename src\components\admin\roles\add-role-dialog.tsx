"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose, // Import DialogClose
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea"; // For description
import { createRole } from "@/lib/api/admin/roles";
import { type CreateRoleRequest } from "@/lib/dto/admin/role.dto";
import { PlusCircle } from "lucide-react";

// Schema for the Add Role form
const addRoleSchema = z.object({
  name: z.string().min(3, { message: "Role name must be at least 3 characters." }).max(50),
  description: z.string().max(255).optional(),
});
type AddRoleSchema = z.infer<typeof addRoleSchema>;

export function AddRoleDialog() {
  const t = useTranslations("AdminRolesPage.AddDialog");
  const queryClient = useQueryClient();
  const [isOpen, setIsOpen] = React.useState(false);

  const form = useForm<AddRoleSchema>({
    resolver: zodResolver(addRoleSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  const addRoleMutation = useMutation({
    mutationFn: createRole,
    onSuccess: (newRole) => {
      toast.success(t("successToast", { roleName: newRole.name }));
      queryClient.invalidateQueries({ queryKey: ["adminRoles"] }); // Refetch roles list
      form.reset();
      setIsOpen(false); // Close dialog on success
    },
    onError: (error) => {
      toast.error(t("errorToast", { error: error.message }));
    },
  });

  function onSubmit(values: AddRoleSchema) {
    const roleData: CreateRoleRequest = {
      ...values,
      permissionNames: [], // Initially create role with no permissions
    };
    addRoleMutation.mutate(roleData);
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <PlusCircle className="mr-2 h-4 w-4" /> {t("triggerButton")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("nameLabel")}</FormLabel>
                  <FormControl>
                    <Input placeholder={t("namePlaceholder")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("descriptionLabel")}</FormLabel>
                  <FormControl>
                    <Textarea placeholder={t("descriptionPlaceholder")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
             <DialogFooter>
                {/* Use DialogClose for the Cancel button */}
                <DialogClose asChild>
                    <Button type="button" variant="outline" onClick={() => form.reset()}>
                    {t("cancelButton")}
                    </Button>
                </DialogClose>
                <Button type="submit" disabled={addRoleMutation.isPending}>
                    {addRoleMutation.isPending ? t("savingButton") : t("saveButton")}
                </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
