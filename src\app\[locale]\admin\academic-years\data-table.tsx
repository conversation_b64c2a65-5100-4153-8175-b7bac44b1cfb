"use client";

import * as React from "react";
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar";
import { searchAcademicYears } from "@/lib/api/admin/academic-year"; // Use the correct API function
import { AcademicYearDto } from "@/lib/dto/admin/academic-year.dto";
import { Skeleton } from "@/components/ui/skeleton";
import { getColumns } from "./columns";

interface DataTableProps<TData, TValue> {
    // No columns prop needed
}

// Debounce hook (assuming it's available or defined elsewhere)
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

    React.useEffect(() => {
        const timer = setTimeout(() => setDebouncedValue(value), delay);
        return () => clearTimeout(timer);
    }, [value, delay]);

    return debouncedValue;
}

export function AcademicYearsDataTable<TData extends AcademicYearDto, TValue>({}: DataTableProps<TData, TValue>) {
    const tPage = useTranslations("AdminAcademicYearsPage");
    const tTable = useTranslations("AdminAcademicYearsPage.table");
    const tShared = useTranslations("Shared");

    const columns = React.useMemo(() => getColumns(tTable, tShared), [tTable, tShared]);

    const [rowSelection, setRowSelection] = React.useState({});
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [pagination, setPagination] = React.useState({
        pageIndex: 0,
        pageSize: 10,
    });
    const [globalFilter, setGlobalFilter] = React.useState('');
    const [showActiveOnly, setShowActiveOnly] = React.useState(true); // State for the toggle

    const debouncedGlobalFilter = useDebounce(globalFilter, 300);

    const queryKey = [
        "academic-years",
        {
            page: pagination.pageIndex,
            size: pagination.pageSize,
            sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
            search: debouncedGlobalFilter,
            activeOnly: showActiveOnly, // Include activeOnly in the query key
        }
    ];

    const { data, isLoading, isError, error } = useQuery({
        queryKey,
        queryFn: () => searchAcademicYears({ // Use the correct API function
            page: pagination.pageIndex,
            size: pagination.pageSize,
            sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
            search: debouncedGlobalFilter,
            activeOnly: showActiveOnly, // Pass activeOnly to the API call
        }),
        placeholderData: (previousData) => previousData,
        // keepPreviousData: true, // Consider enabling for smoother UX
    });

    const table = useReactTable({
        data: data?.content ?? [],
        columns,
        state: {
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
            pagination,
            globalFilter,
        },
        enableRowSelection: true,
        manualPagination: true,
        manualSorting: true,
        manualFiltering: true, // Server-side filtering via 'search' and 'activeOnly' params
        pageCount: data?.totalPages ?? -1,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onPaginationChange: setPagination,
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    });

    const renderTableContent = () => {
        if (isLoading) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        <div className="space-y-2">
                            {Array.from({ length: 5 }).map((_, i) => (
                                <Skeleton key={i} className="h-8 w-full" />
                            ))}
                        </div>
                    </TableCell>
                </TableRow>
            );
        }

        if (isError) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center text-destructive">
                        {tPage("errorLoading", { error: error?.message || 'Unknown error' })}
                    </TableCell>
                </TableRow>
            );
        }

        if (!table.getRowModel().rows?.length) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        {tPage("noResults")}
                    </TableCell>
                </TableRow>
            );
        }

        return table.getRowModel().rows.map((row) => (
            <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
            >
                {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                ))}
            </TableRow>
        ));
    };

    return (
        <div className="space-y-4">
            <DataTableToolbar
                table={table}
                globalFilter={globalFilter}
                setGlobalFilter={setGlobalFilter}
                showActiveOnly={showActiveOnly}
                setShowActiveOnly={setShowActiveOnly} // Pass state and setter
            />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead key={header.id} colSpan={header.colSpan}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {renderTableContent()}
                    </TableBody>
                </Table>
            </div>
            <DataTablePagination table={table} />
        </div>
    );
}
