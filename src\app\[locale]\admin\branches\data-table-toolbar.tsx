"use client";

import { Table } from "@tanstack/react-table";
import { useTranslations } from 'next-intl';

import { AddBranchDialog } from "./add-branch-dialog";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options"; // Assuming this exists

interface DataTableToolbarProps<TData> {
    table: Table<TData>;
    onBranchAdded?: () => void; // Optional callback after adding
}

export function DataTableToolbar<TData>({
    table,
    onBranchAdded,
}: DataTableToolbarProps<TData>) {
    const t = useTranslations('AdminBranchesPage'); // Use the main page translations

    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
                {/* No search input needed based on API */}
            </div>
            <div className="flex items-center space-x-2">
                 <DataTableViewOptions table={table} />
                 <AddBranchDialog onSuccess={onBranchAdded} />
            </div>
        </div>
    );
}
