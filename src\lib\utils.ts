import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Extracts a user-friendly error message from various error types.
 * @param error The error object or value.
 * @returns A string representing the error message.
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    // Standard Error object
    return error.message;
  } else if (typeof error === 'string') {
    // Plain string error
    return error;
  } else if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string') {
    // Object with a message property (like custom error objects or API error responses)
    return error.message;
  } else if (error && typeof error === 'object' && 'error' in error && typeof error.error === 'string') {
     // Object with an error property (another common pattern)
     return error.error;
  }
  // Fallback for unknown error types
  return 'An unknown error occurred';
}

/**
 * Builds a URL query string from an object of parameters.
 * Handles arrays by repeating the key for each value (e.g., sort=name,asc&sort=date,desc).
 * Skips undefined, null, and empty string values.
 *
 * @param params - An object containing query parameters.
 * @returns A URL-encoded query string.
 */
export function buildQueryString(params: Record<string, any>): string {
  const queryParts: string[] = [];

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        // Handle arrays (e.g., for sorting)
        value.forEach(item => {
          if (item !== undefined && item !== null && item !== '') {
            queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(item))}`);
          }
        });
      } else {
        // Handle single values
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`);
      }
    }
  });

  return queryParts.join('&');
}

/**
 * Formats a number as currency according to locale and currency code.
 * Defaults to SAR (Saudi Riyal) and 'ar-SA' locale.
 * @param amount The number to format.
 * @param currency The ISO 4217 currency code (default: 'SAR').
 * @param locale The locale string (default: 'ar-SA').
 * @returns The formatted currency string.
 */
export function formatCurrency(amount: number, currency = 'SAR', locale = 'ar-SA'): string {
    return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
    }).format(amount);
}

/**
 * Formats a Date object into a "YYYY-MM-DD" string.
 * Returns an empty string if the input is not a valid Date.
 * @param date The Date object to format.
 * @returns The formatted date string or an empty string.
 */
export function formatIsoDate(date: Date | undefined | null): string {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
        return '';
    }
    return date.toISOString().split('T')[0];
}

/**
 * Compares two objects and returns an object containing only the key-value pairs
 * where the value has changed. Handles null/undefined comparisons.
 * @param currentValues The current state of the object (e.g., form values).
 * @param initialValues The initial state of the object (e.g., default form values).
 * @returns An object containing only the changed key-value pairs.
 */
export function getChangedValues<T extends Record<string, any>>(
  currentValues: T,
  initialValues: T
): Partial<T> {
  const changedValues: Partial<T> = {};

  for (const key in currentValues) {
    if (Object.prototype.hasOwnProperty.call(currentValues, key)) {
      const currentValue = currentValues[key];
      const initialValue = initialValues ? initialValues[key] : undefined;

      // Basic comparison, handles primitives, null, undefined.
      // Consider deep comparison for nested objects/arrays if needed.
      if (currentValue !== initialValue) {
        // Special handling for empty strings vs null/undefined if desired
        // Example: Treat '' the same as null/undefined for optional fields
        // if (currentValue === '' && (initialValue === null || initialValue === undefined)) {
        //   continue; // Skip if empty string is considered unchanged from null/undefined
        // }
        changedValues[key] = currentValue;
      }
    }
  }

  return changedValues;
}
