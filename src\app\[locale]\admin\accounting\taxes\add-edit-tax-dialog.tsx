"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger, // Keep if triggered externally, remove if only controlled by isOpen
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AccountCombobox } from "@/components/ui/combobox-coa"; // Import AccountCombobox
import { TaxDto, CreateTaxRequest, UpdateTaxRequest } from "@/lib/dto/admin/accounting/taxes.dto";
import { createTaxSchema, updateTaxSchema, CreateTaxInput, UpdateTaxInput } from "@/lib/schemas/admin/accounting/taxes";
import { createTax, updateTax } from "@/lib/api/admin/accounting/taxes";
import { getChangedValues } from "@/lib/utils";
import { AccountCategoryEnum } from "@/lib/schemas/admin/accounting/chart-of-accounts"; // Import enum for filtering

// Remove t and tShared from props
interface AddEditTaxDialogProps {
    tax?: TaxDto | null; // Tax data for editing, null/undefined for adding
    isOpen: boolean;
    onOpenChange: (isOpen: boolean) => void;
    onSuccess?: (tax: TaxDto) => void; // Optional callback on successful add/edit
    children?: React.ReactNode; // To allow triggering the dialog with a button if needed
    // t: ReturnType<typeof useTranslations<'AdminTaxesPage'>>; // Removed
    // tShared: ReturnType<typeof useTranslations<'Shared'>>; // Removed
}

export function AddEditTaxDialog({
    tax,
    isOpen,
    onOpenChange,
    onSuccess,
    children,
    // t, // Removed
    // tShared, // Removed
}: AddEditTaxDialogProps) {
    // Get translations internally
    const t = useTranslations("AdminTaxesPage");
    // const tShared = useTranslations("Shared"); // Not currently used directly here, but could be added if needed

    const queryClient = useQueryClient();
    const isEditMode = !!tax;

    // Choose the correct schema based on mode
    const currentSchema = isEditMode ? updateTaxSchema(t) : createTaxSchema(t);
    type CurrentFormInput = isEditMode extends true ? UpdateTaxInput : CreateTaxInput;

    const form = useForm<CurrentFormInput>({
        resolver: zodResolver(currentSchema),
        defaultValues: isEditMode
            ? {
                nameEn: tax?.nameEn ?? "",
                nameAr: tax?.nameAr ?? "",
                percent: tax?.percent ?? 0,
                descriptionEn: tax?.descriptionEn ?? "",
                descriptionAr: tax?.descriptionAr ?? "",
                chartOfAccountId: tax?.chartOfAccount?.id ?? undefined, // Add chartOfAccountId
            }
            : {
                nameEn: "",
                nameAr: "",
                percent: 0,
                descriptionEn: "",
                descriptionAr: "",
                chartOfAccountId: undefined, // Add chartOfAccountId
            },
    });

    // Reset form when dialog opens or tax data changes
    React.useEffect(() => {
        if (isOpen) {
            form.reset(
                isEditMode
                    ? {
                        nameEn: tax?.nameEn ?? "",
                        nameAr: tax?.nameAr ?? "",
                        percent: tax?.percent ?? 0,
                        descriptionEn: tax?.descriptionEn ?? "",
                        descriptionAr: tax?.descriptionAr ?? "",
                        chartOfAccountId: tax?.chartOfAccount?.id ?? undefined, // Add chartOfAccountId
                    }
                    : {
                        nameEn: "",
                        nameAr: "",
                        percent: 0,
                        descriptionEn: "",
                        descriptionAr: "",
                        chartOfAccountId: undefined, // Add chartOfAccountId
                    }
            );
        }
    }, [isOpen, tax, isEditMode, form]);

    const mutation = useMutation({
        mutationFn: async (data: CurrentFormInput) => {
            const taxName = data.nameEn || tax?.nameEn || 'New Tax'; // Use name for toast messages
            const toastId = toast.loading(isEditMode ? t("EditDialog.savingButton") : t("AddDialog.savingButton"));

            try {
                let result: TaxDto;
                if (isEditMode && tax) {
                    // Compare current data with initial default values to detect changes
                    const initialValues = form.formState.defaultValues as UpdateTaxInput;
                    const hasChanges = Object.keys(data).some(key =>
                        data[key as keyof CurrentFormInput] !== initialValues[key as keyof UpdateTaxInput]
                    );

                    if (!hasChanges) {
                        toast.info(t("EditDialog.noChanges"), { id: toastId });
                        return null; // Indicate no actual mutation occurred
                    }
                    // Send the full data object for update (PUT replaces the resource)
                    result = await updateTax(tax.id, data as UpdateTaxRequest);
                    toast.success(t("EditDialog.successToast", { name: result.nameEn }), { id: toastId });
                } else {
                    // Create uses the full data object
                    result = await createTax(data as CreateTaxRequest);
                    toast.success(t("AddDialog.successToast", { name: result.nameEn }), { id: toastId });
                }
                // Invalidate queries to refetch data
                queryClient.invalidateQueries({ queryKey: ['taxes'] });
                return result;
            } catch (error: any) {
                console.error("Failed to save tax:", error);
                toast.error(
                    isEditMode
                        ? t("EditDialog.errorToast", { error: error?.message || "Unknown error" })
                        : t("AddDialog.errorToast", { error: error?.message || "Unknown error" }),
                    { id: toastId }
                );
                throw error; // Re-throw to keep mutation in error state
            }
        },
        onSuccess: (data) => {
            if (data && onSuccess) {
                onSuccess(data); // Call the onSuccess callback if provided and mutation occurred
            }
            if (data) { // Only close if mutation occurred (not just 'no changes')
                 onOpenChange(false); // Close dialog on success
            }
        },
        // onError: handled by toast in mutationFn
    });

    const onSubmit = (data: CurrentFormInput) => {
        mutation.mutate(data);
    };

    // Handle closing the dialog
    const handleOpenChange = (open: boolean) => {
        if (!open && mutation.isPending) return; // Prevent closing while submitting
        onOpenChange(open);
        if (!open) {
            form.reset(); // Reset form on close
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleOpenChange}>
            {/* Optional: If you need an external trigger button */}
            {/* {children && <DialogTrigger asChild>{children}</DialogTrigger>} */}
            <DialogContent className="sm:max-w-[480px]">
                <DialogHeader>
                    <DialogTitle>
                        {isEditMode ? t("EditDialog.title", { name: tax?.nameEn }) : t("AddDialog.title")}
                    </DialogTitle>
                    <DialogDescription>
                        {isEditMode ? t("EditDialog.description", { name: tax?.nameEn }) : t("AddDialog.description")}
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            {/* Name (English) */}
                            <FormField
                                control={form.control}
                                name="nameEn"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("AddDialog.nameEnLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("AddDialog.nameEnPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Name (Arabic) */}
                            <FormField
                                control={form.control}
                                name="nameAr"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("AddDialog.nameArLabel")}</FormLabel>
                                        <FormControl>
                                            <Input dir="rtl" placeholder={t("AddDialog.nameArPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        {/* Chart of Account */}
                        <FormField
                            control={form.control}
                            name="chartOfAccountId"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("AddDialog.chartOfAccountLabel")}</FormLabel>
                                    <FormControl>
                                        <AccountCombobox
                                            value={field.value}
                                            onChange={field.onChange}
                                            // Filter for Liability accounts, as taxes are typically liabilities
                                            // filterCategory={AccountCategoryEnum.Enum.LIABILITY}          // filterCategory={AccountCategoryEnum.Enum.LIABILITY}
                                            placeholder={t("AddDialog.chartOfAccountPlaceholder")}
                                            searchPlaceholder={t("AddDialog.searchAccountPlaceholder")}
                                            noResultsText={t("AddDialog.noAccountFound")}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Percentage */}
                        <FormField
                            control={form.control}
                            name="percent"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("AddDialog.percentLabel")}</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="number"
                                            step="0.01" // Allow decimals
                                            min="0"
                                            max="100"
                                            placeholder={t("AddDialog.percentPlaceholder")}
                                            {...field}
                                            // Ensure value is treated as number
                                            onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))}
                                            value={field.value ?? ''}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Description (English) */}
                        <FormField
                            control={form.control}
                            name="descriptionEn"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("AddDialog.descriptionEnLabel")}</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder={t("AddDialog.descriptionEnPlaceholder")}
                                            className="resize-none"
                                            {...field}
                                            value={field.value ?? ''} // Handle null/undefined
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Description (Arabic) */}
                        <FormField
                            control={form.control}
                            name="descriptionAr"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("AddDialog.descriptionArLabel")}</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            dir="rtl"
                                            placeholder={t("AddDialog.descriptionArPlaceholder")}
                                            className="resize-none"
                                            {...field}
                                            value={field.value ?? ''} // Handle null/undefined
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => handleOpenChange(false)}
                                disabled={mutation.isPending}
                            >
                                {t("AddDialog.cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending || !form.formState.isDirty}>
                                {mutation.isPending
                                    ? (isEditMode ? t("EditDialog.savingButton") : t("AddDialog.savingButton"))
                                    : (isEditMode ? t("EditDialog.saveButton") : t("AddDialog.saveButton"))}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
