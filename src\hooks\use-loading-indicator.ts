"use client";

import { useContext } from "react";
import { RouteChangeContext, useRouteChange } from "@/components/providers/route-change-provider";

/**
 * Hook to access and control the loading indicator state
 * @returns Object containing isLoading state and methods to control it
 */
export function useLoadingIndicator() {
  // Get the current loading state from context
  const { isRouteChanging } = useRouteChange();
  
  // Return the loading state
  return {
    isLoading: isRouteChanging,
  };
}
