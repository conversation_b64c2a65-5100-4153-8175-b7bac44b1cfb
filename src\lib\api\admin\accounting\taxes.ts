import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { API_BASE_URL } from "@/lib/constants";
import {
    CreateTaxRequest,
    GetTaxesParams,
    PageTaxDto,
    TaxDto,
    UpdateTaxRequest,
} from "@/lib/dto/admin/accounting/taxes.dto";
import { buildQueryString } from "@/lib/utils"; // Corrected import name
import { ErrorResponse } from "@/lib/dto/error-response.dto";

const TAX_API_URL = `${API_BASE_URL}/accounting/taxes`;

/**
 * Fetches a paginated list of taxes.
 * @param params - Parameters for pagination, sorting, and filtering.
 * @returns A promise resolving to PageTaxDto.
 */
export async function getTaxes(params: GetTaxesParams): Promise<PageTaxDto> {
    const queryParams = buildQueryString(params); // Corrected function name usage
    const url = `${TAX_API_URL}?${queryParams}`;

    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch taxes: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary (e.g., using Zod)
        return data as PageTaxDto;
    } catch (error) {
        console.error("Error fetching taxes:", error);
        throw error; // Re-throw the error for the caller to handle
    }
}

/**
 * Fetches a single tax by its ID.
 * @param id - The UUID of the tax.
 * @returns A promise resolving to TaxDto.
 */
export async function getTaxById(id: string): Promise<TaxDto> {
    const url = `${TAX_API_URL}/${id}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            if (response.status === 404) {
                throw new Error(errorData?.message || `Tax with ID ${id} not found.`);
            }
            throw new Error(errorData?.message || `Failed to fetch tax: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as TaxDto;
    } catch (error) {
        console.error(`Error fetching tax with ID ${id}:`, error);
        throw error;
    }
}

/**
 * Creates a new tax.
 * @param request - The data for the new tax.
 * @returns A promise resolving to the created TaxDto.
 */
export async function createTax(request: CreateTaxRequest): Promise<TaxDto> {
    try {
        const response = await fetchWithAuth(TAX_API_URL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(request),
        });
        if (!response.ok) {
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to create tax: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as TaxDto;
    } catch (error) {
        console.error("Error creating tax:", error);
        throw error;
    }
}

/**
 * Updates an existing tax.
 * @param id - The UUID of the tax to update.
 * @param request - The data to update.
 * @returns A promise resolving to the updated TaxDto.
 */
export async function updateTax(id: string, request: UpdateTaxRequest): Promise<TaxDto> {
    const url = `${TAX_API_URL}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(request),
        });
        if (!response.ok) {
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to update tax: ${response.statusText}`);
        }
        const data: unknown = await response.json();
        // TODO: Add type validation if necessary
        return data as TaxDto;
    } catch (error) {
        console.error(`Error updating tax with ID ${id}:`, error);
        throw error;
    }
}

/**
 * Fetches all active taxes suitable for a dropdown list.
 * Note: This currently fetches a large page size as a workaround.
 * A dedicated API endpoint `/api/v1/accounting/taxes/active` would be more efficient.
 * @returns A promise resolving to an array of TaxDto.
 */
export async function getAllActiveTaxesForSelect(): Promise<TaxDto[]> {
    // Workaround: Fetch a large number of taxes assuming it covers most active ones.
    // Replace with a dedicated endpoint call if available.
    const params: GetTaxesParams = { page: 0, size: 1000, sort: ["nameEn,asc"] }; // Fetch up to 1000
    const url = `${TAX_API_URL}?${buildQueryString(params)}`;

    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch active taxes: ${response.statusText}`);
        }
        const pageData: PageTaxDto = await response.json() as PageTaxDto;
        // TODO: Ideally, filter for active status if the DTO includes it, or rely on the (missing) dedicated endpoint.
        return pageData.content;
    } catch (error) {
        console.error("Error fetching active taxes:", error);
        throw error;
    }
}


/**
 * Deletes a tax by its ID.
 * @param id - The UUID of the tax to delete.
 * @returns A promise resolving when the deletion is successful.
 */
export async function deleteTax(id: string): Promise<void> {
    const url = `${TAX_API_URL}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: "DELETE",
        });
        if (!response.ok) {
            // Handle cases where the response might not have a body (like 204 No Content on success)
            if (response.status === 204) {
                return; // Successfully deleted
            }
            const errorData: ErrorResponse = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to delete tax: ${response.statusText}`);
        }
        // Check if the response status indicates success but has no content
        if (response.status === 204) {
            return;
        }
        // If there's unexpected content for a successful delete, log it but proceed.
        if (response.body) {
            console.warn("Unexpected response body for successful DELETE request:", await response.text());
        }
    } catch (error) {
        console.error(`Error deleting tax with ID ${id}:`, error);
        throw error;
    }
}
