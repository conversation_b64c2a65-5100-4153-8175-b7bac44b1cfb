"use client"

import * as React from "react"

import { Check, ChevronsUpDown } from "lucide-react"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useLocale, useTranslations } from "next-intl"; // Import useLocale

import { AccountCategoryEnum } from "@/lib/schemas/admin/accounting/chart-of-accounts"; // Import enum for type safety
import { Button } from "@/components/ui/button"
import { SimpleChartOfAccountDto } from "@/lib/dto/admin/accounting/chart-of-accounts.dto"; // Import DTO
import { Skeleton } from "./skeleton";
import { cn } from "@/lib/utils"
import { findActiveChartOfAccountsSimple } from "@/lib/api/admin/accounting/chart-of-accounts"; // Import the new API function
import { useQuery } from "@tanstack/react-query"; // Import useQuery

interface AccountComboboxProps {
  value: string | undefined;
  onChange: (value: string | undefined) => void;
  disabled?: boolean;
  filterCategory?: z.infer<typeof AccountCategoryEnum>; // Optional category filter prop
  placeholder?: string; // Allow custom placeholder
  searchPlaceholder?: string; // Allow custom search placeholder
  noResultsText?: string; // Allow custom no results text
}

// Define the structure for options used in the combobox
interface AccountOption {
  value: string; // ID
  label: string; // e.g., "1010 - Cash"
}

export function AccountCombobox({
  value,
  onChange,
  disabled,
  filterCategory, // Destructure the new prop
  placeholder,
  searchPlaceholder,
  noResultsText,
}: AccountComboboxProps) {
  const [open, setOpen] = React.useState(false);
  // Use provided texts or fall back to defaults (assuming defaults are in AdminJournalEntriesPage.form)
  const tDefault = useTranslations("AdminJournalEntriesPage.form");
  const t = {
    selectAccountPlaceholder: placeholder ?? tDefault("selectAccountPlaceholder"),
    searchAccountPlaceholder: searchPlaceholder ?? tDefault("searchAccountPlaceholder"),
    noAccountFound: noResultsText ?? tDefault("noAccountFound"),
    errorLoadingAccounts: tDefault("errorLoadingAccounts"), // Keep using default for error
  };
  const locale = useLocale(); // Use useLocale() to get the current locale ('en' or 'ar')

  // Fetch active accounts using TanStack Query, include filterCategory in the key
  const queryKey = ["activeChartOfAccountsSimple", filterCategory]; // Add filterCategory to query key
  const { data: accounts, isLoading, isError } = useQuery<SimpleChartOfAccountDto[]>({
    queryKey,
    queryFn: () => findActiveChartOfAccountsSimple({ category: filterCategory }), // Pass filter to API function
  });

  // Memoize the formatted options for the combobox
  const accountOptions: AccountOption[] = React.useMemo(() => {
    return accounts?.map(acc => ({
        value: acc.id,
        // Combine number and name based on current locale
        label: `${acc.accountNumber} - ${locale === 'ar' ? acc.nameAr : acc.nameEn}`
    })) ?? [];
  }, [accounts, locale]); // Depend on accounts data and locale

  const selectedLabel = accountOptions?.find((acc) => acc.value === value)?.label;

  if (isLoading) {
    return <Skeleton className="h-8 w-full" />;
  }

  if (isError) {
    // Handle error state appropriately, maybe show a disabled input or error message
    return <Input value={t.errorLoadingAccounts} disabled className="h-8" />; // Access property
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className=" justify-between h-8 font-normal" // Consistent height
          disabled={disabled || isLoading || isError}
        >
          {value
            ? selectedLabel ?? t.selectAccountPlaceholder // Access property
            : t.selectAccountPlaceholder} {/* Access property */}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
        <Command>
          <CommandInput placeholder={t.searchAccountPlaceholder} /> {/* Access property */}
          <CommandList>
            <CommandEmpty>{t.noAccountFound}</CommandEmpty> {/* Access property */}
            <CommandGroup>
              {accountOptions.map((account) => (
                <CommandItem
                  key={account.value}
                  value={account.label} // Search based on the formatted label
                  onSelect={(currentValue) => {
                    // Find the account value corresponding to the selected label
                    const selectedValue = accountOptions.find(acc => acc.label.toLowerCase() === currentValue.toLowerCase())?.value;
                    onChange(selectedValue === value ? undefined : selectedValue);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === account.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {account.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
