"use client";

import { ColumnDef } from "@tanstack/react-table";
import { UseTranslations } from "next-intl";
import { ExpenseCategoryDto } from "@/lib/dto/admin/accounting/expense-categories.dto";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions"; // Will create this next
// Removed Badge import as 'active' status is not available

// Accept translation functions as arguments
export const getColumns = (
    t: UseTranslations<"AdminExpenseCategoriesPage.table">,
    tShared: UseTranslations<"Shared"> // Keep shared if needed for other statuses later
): ColumnDef<ExpenseCategoryDto>[] => {

    return [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "nameEn",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("nameEn")} />
            ),
            cell: ({ row }) => row.getValue("nameEn"),
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "nameAr",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("nameAr")} />
            ),
            cell: ({ row }) => row.getValue("nameAr"),
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "descriptionEn",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("descriptionEn")} />
            ),
            cell: ({ row }) => row.getValue("descriptionEn") || '---',
            enableSorting: false, // Descriptions usually not sorted
            enableHiding: true,
        },
        {
            accessorKey: "descriptionAr",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("descriptionAr")} />
            ),
            cell: ({ row }) => row.getValue("descriptionAr") || '---',
            enableSorting: false,
            enableHiding: true,
        },
        {
            // Access nested property for display
            accessorKey: "parentCategory.nameEn", // Use accessorKey for sorting/filtering if needed later
            id: "parentCategory", // Unique ID for the column
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("parentCategory")} />
            ),
            // Display parent name or placeholder
            cell: ({ row }) => {
                const parent = row.original.parentCategory;
                // Display both names if parent exists
                return parent ? `${parent.nameEn} / ${parent.nameAr}` : '---';
            },
            enableSorting: false, // Sorting might be complex on nested optional fields
            enableHiding: true,
        },
        {
            accessorKey: "expenseAccount",
            id: "expenseAccount",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("expenseAccount")} />
            ),
            cell: ({ row }) => {
                const account = row.original.expenseAccount;
                return account ? `${account.accountNumber} - ${account.nameEn} / ${account.nameAr}` : '---';
            },
            enableSorting: false, // Sorting on nested object might require specific setup
            enableHiding: true,
        },
        {
            id: "actions",
            cell: ({ row }) => <DataTableRowActions row={row} onCategoryUpdated={() => { /* TODO: Invalidate query? */ }} />, // Pass callback if needed
            enableSorting: false,
            enableHiding: false,
        },
    ];
};
