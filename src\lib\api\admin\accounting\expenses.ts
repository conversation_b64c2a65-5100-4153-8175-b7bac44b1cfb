// Removed apiClient import
import { useAuthStore } from "@/stores/auth"; // Assuming auth store path
import {
    ExpenseDto,
    CreateExpenseRequest,
    UpdateExpenseRequest,
    GetExpensesParams,
    PageExpenseDto
} from "@/lib/dto/admin/accounting/expenses.dto";
// Removed buildQueryParams import
import { API_BASE_URL } from "@/lib/constants";


if (!API_BASE_URL) {
    console.error("Error: NEXT_PUBLIC_API_BASE_URL environment variable is not set.");
    // Removed the throw new Error line. The console error remains for development visibility.
}

const EXPENSES_ENDPOINT = `${API_BASE_URL}/accounting/expenses`;


/**
 * Find Expenses (Paginated)
 * GET /api/v1/accounting/expenses
 */
export const findExpenses = async (params: GetExpensesParams): Promise<PageExpenseDto> => {
    const { accessToken } = useAuthStore.getState(); // Get accessToken from Zustand store
    // Removed the explicit !accessToken check here. The fetch will proceed,
    // and the API should return 401 if the accessToken is missing/invalid.

    const query = new URLSearchParams();

    // Build query parameters manually
    if (params.page !== undefined) query.set('page', String(params.page));
    if (params.size !== undefined) query.set('size', String(params.size));
    if (params.sort) params.sort.forEach(s => query.append('sort', s));
    if (params.startDate) query.set('startDate', params.startDate);
    if (params.endDate) query.set('endDate', params.endDate);
    if (params.categoryId) query.set('categoryId', params.categoryId);
    // Add other params as needed

    const queryString = query.toString();
    const url = `${EXPENSES_ENDPOINT}${queryString ? `?${queryString}` : ''}`;

    const headers: HeadersInit = {
        'Accept': 'application/json',
    };
    if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch(url, {
        method: 'GET',
        headers: headers,
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(`Failed to fetch expenses: ${errorData.message || response.statusText}`);
    }

    return await response.json() as PageExpenseDto;
};

/**
 * Get Expense by ID
 * GET /api/v1/accounting/expenses/{id}
 */
export const getExpenseById = async (id: string): Promise<ExpenseDto> => {
    const { accessToken } = useAuthStore.getState();
    const url = `${EXPENSES_ENDPOINT}/${id}`;

    const response = await fetch(url, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Accept': 'application/json',
        },
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(`Failed to fetch expense ${id}: ${errorData.message || response.statusText}`);
    }

    return await response.json() as ExpenseDto;
};

/**
 * Create a new Expense
 * POST /api/v1/accounting/expenses
 */
export const createExpense = async (data: CreateExpenseRequest): Promise<ExpenseDto> => {
    const { accessToken } = useAuthStore.getState();

    const response = await fetch(EXPENSES_ENDPOINT, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(`Failed to create expense: ${errorData.message || response.statusText}`);
    }
     // Check for 201 Created status specifically if needed
    if (response.status === 201) {
        return await response.json() as ExpenseDto;
    } else {
        // Handle unexpected success statuses if necessary
        console.warn(`Unexpected status code ${response.status} on expense creation.`);
        return await response.json() as ExpenseDto; // Or throw an error
    }
};

/**
 * Update an Expense
 * PUT /api/v1/accounting/expenses/{id}
 */
export const updateExpense = async (id: string, data: UpdateExpenseRequest): Promise<ExpenseDto> => {
    const { accessToken } = useAuthStore.getState();
    const url = `${EXPENSES_ENDPOINT}/${id}`;

    const response = await fetch(url, {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(`Failed to update expense ${id}: ${errorData.message || response.statusText}`);
    }

    return await response.json() as ExpenseDto;
};

/**
 * Delete an Expense
 * DELETE /api/v1/accounting/expenses/{id}
 */
export const deleteExpense = async (id: string): Promise<void> => {
    const { accessToken } = useAuthStore.getState();
    const url = `${EXPENSES_ENDPOINT}/${id}`;

    const response = await fetch(url, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${accessToken}`,
        },
    });

    // DELETE typically returns 204 No Content on success
    if (!response.ok && response.status !== 204) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(`Failed to delete expense ${id}: ${errorData.message || response.statusText}`);
    }

    // No body expected for 204 response
};

// Add other potential API functions if needed (e.g., get by reference, etc.)
