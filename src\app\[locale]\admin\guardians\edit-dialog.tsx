"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { updateGuardianProfile } from "@/lib/api/admin/guardians";
import { UpdateGuardianProfileInput, updateGuardianProfileSchema } from "@/lib/schemas/admin/guardian";
import { getErrorMessage } from "@/lib/utils";
import { GuardianDto } from "@/lib/dto/admin/guardian.dto";

interface EditGuardianDialogProps {
    isOpen: boolean;
    setIsOpen: (open: boolean) => void;
    guardian: GuardianDto;
    onSuccess?: () => void;
}

export function EditGuardianDialog({ isOpen, setIsOpen, guardian, onSuccess }: EditGuardianDialogProps) {
    const t = useTranslations("AdminGuardiansPage.EditDialog");
    const queryClient = useQueryClient();

    const form = useForm<UpdateGuardianProfileInput>({
        resolver: zodResolver(updateGuardianProfileSchema),
        defaultValues: {
            occupation: guardian.occupation,
            address: guardian.address,
            nationalId: guardian.nationalId,
            idType: guardian.idType,
        },
    });

    // Reset form when guardian data changes (e.g., opening dialog for a different guardian)
    React.useEffect(() => {
        if (isOpen) {
            form.reset({
                occupation: guardian.occupation,
                address: guardian.address,
                nationalId: guardian.nationalId,
                idType: guardian.idType,
            });
        }
    }, [guardian, isOpen, form]);

    const mutation = useMutation({
        mutationFn: (data: UpdateGuardianProfileInput) => updateGuardianProfile(guardian.id, data),
        onSuccess: (data) => {
            toast.success(t("successToast", { name: `${data.userAccount.firstName} ${data.userAccount.lastName}` }));
            queryClient.invalidateQueries({ queryKey: ["guardians"] });
            setIsOpen(false); // Close dialog
            onSuccess?.();
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: getErrorMessage(error) }));
        },
    });

    const onSubmit = (data: UpdateGuardianProfileInput) => {
        mutation.mutate(data);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>
                        {t("description", { name: `${guardian.userAccount.firstName} ${guardian.userAccount.lastName}` })}
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4 py-4">
                        <FormField
                            control={form.control}
                            name="occupation"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("occupationLabel")}</FormLabel>
                                    <FormControl>
                                        <Input placeholder={t("occupationPlaceholder")} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="address"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("addressLabel")}</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder={t("addressPlaceholder")}
                                            className="resize-none"
                                            {...field}
                                            value={field.value || ""}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="nationalId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("nationalIdLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("nationalIdPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="idType"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("idTypeLabel")}</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("idTypePlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem value="NATIONAL_ID">{t("idTypeNationalId")}</SelectItem>
                                                <SelectItem value="PASSPORT">{t("idTypePassport")}</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <DialogFooter>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("submitting") : t("submit")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
