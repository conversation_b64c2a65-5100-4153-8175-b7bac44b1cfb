"use client";

import { ColumnDef } from "@tanstack/react-table";
import { AiReportDto, ReportStatus } from "@/lib/dto/admin/accounting/ai-reports.dto";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { UseTranslations } from "next-intl";

export const getColumns = (
  t: UseTranslations<"AiReportsPage.table">,
  tShared: UseTranslations<"Shared">
): ColumnDef<AiReportDto>[] => {
  return [
    {
      accessorKey: "reportType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("reportType")} />
      ),
      cell: ({ row }) => {
        const reportType = row.getValue("reportType") as string;
        return <div>{t(reportType.toLowerCase())}</div>;
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "startDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("startDate")} />
      ),
      cell: ({ row }) => {
        const startDate = row.getValue("startDate") as string;
        return <div>{startDate}</div>;
      },
    },
    {
      accessorKey: "endDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("endDate")} />
      ),
      cell: ({ row }) => {
        const endDate = row.getValue("endDate") as string;
        return <div>{endDate}</div>;
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("status")} />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as ReportStatus;
        
        let badgeVariant: "default" | "outline" | "secondary" | "destructive" = "default";
        
        switch (status) {
          case "COMPLETED":
            badgeVariant = "default";
            break;
          case "PENDING":
            badgeVariant = "secondary";
            break;
          case "FAILED":
            badgeVariant = "destructive";
            break;
        }
        
        return (
          <Badge variant={badgeVariant}>
            {t(status.toLowerCase())}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "createdDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("createdDate")} />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdDate") as string);
        return <div>{format(date, "yyyy-MM-dd HH:mm")}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const report = row.original;
        const isViewable = report.status === "COMPLETED";
        
        return (
          <div className="flex justify-end">
            <Button
              variant="ghost"
              size="icon"
              asChild
              disabled={!isViewable}
            >
              <Link href={`/admin/accounting/reports/ai-reports/${report.id}`}>
                <Eye className="h-4 w-4" />
                <span className="sr-only">{t("view")}</span>
              </Link>
            </Button>
          </div>
        );
      },
    },
  ];
};
