"use client";

import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Pen, Trash } from "lucide-react";
import { ExpenseCategoryDto } from "@/lib/dto/admin/accounting/expense-categories.dto";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteExpenseCategory } from "@/lib/api/admin/accounting/expense-categories";
import { toast } from "sonner";
import React, { useState } from "react"; // Import React
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, Alert<PERSON><PERSON>ogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { AddEditExpenseCategorySheet } from "./add-edit-expense-category-sheet"; // Use the new sheet component

interface DataTableRowActionsProps<TData> {
    row: Row<TData>;
    onCategoryUpdated?: () => void; // Optional callback after update
}

export function DataTableRowActions<TData>({
    row,
    onCategoryUpdated,
}: DataTableRowActionsProps<TData>) {
    const category = row.original as ExpenseCategoryDto;
    const t = useTranslations("AdminExpenseCategoriesPage.table");
    const tConfirm = useTranslations("Shared.confirmationDialog");
    const queryClient = useQueryClient();
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isSheetOpen, setIsSheetOpen] = useState(false); // State for the sheet

    const deleteMutation = useMutation({
        mutationFn: deleteExpenseCategory,
        onSuccess: () => {
            toast.success(t("deleteSuccessToast", { name: category.nameEn }));
            queryClient.invalidateQueries({ queryKey: ["expense-categories"] }); // Invalidate cache
            queryClient.invalidateQueries({ queryKey: ["expense-categories-list"] }); // Also invalidate list if used elsewhere
        },
        onError: (error) => {
            toast.error(t("deleteErrorToast", { error: error.message }));
        },
        onSettled: () => {
            setIsDeleteDialogOpen(false);
        }
    });

    const handleDelete = () => {
        deleteMutation.mutate(category.id);
    };

    const handleEditSuccess = () => {
        setIsSheetOpen(false); // Close sheet on success
        if (onCategoryUpdated) {
            onCategoryUpdated(); // Call optional callback
        }
        // Invalidation is handled within the sheet's mutation now
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                    >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{t("actionsOpenMenu")}</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuItem onClick={() => setIsSheetOpen(true)}> {/* Open sheet */}
                        <Pen className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("actionsEdit")}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        variant="destructive"
                        onClick={() => setIsDeleteDialogOpen(true)}
                        disabled={deleteMutation.isPending}
                    >
                        <Trash className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("actionsDelete")}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Add/Edit Sheet */}
            <AddEditExpenseCategorySheet
                categoryId={category.id} // Pass ID for editing
                isOpen={isSheetOpen}
                onOpenChange={setIsSheetOpen}
                onSuccess={handleEditSuccess} // Pass success handler
            />

            {/* Confirmation Dialog for Delete */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{tConfirm("title")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {tConfirm("deleteMessage", { item: `${t("expenseCategoryItem")} '${category.nameEn}'` })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={deleteMutation.isPending}>{tConfirm("cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            disabled={deleteMutation.isPending}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {deleteMutation.isPending ? tConfirm("deleting") : tConfirm("delete")}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
