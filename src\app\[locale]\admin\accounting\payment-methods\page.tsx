import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PaymentMethodsDataTable } from "./data-table"; // Import the new data table

export default async function PaymentMethodsPage() {
    const t = await getTranslations("AdminPaymentMethodsPage");

    return (
        <Card>
            <CardHeader>
                <CardTitle>{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent>
                <PaymentMethodsDataTable /> {/* Use the new data table */}
            </CardContent>
        </Card>
    );
}
