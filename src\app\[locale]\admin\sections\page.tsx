'use client';

import { ColumnDef, getCoreRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table'; // Imported for typing
import { DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE } from '@/lib/constants';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useDeleteSection, useGetSections } from '@/lib/api/admin/school-management/sections-api';
import { useEffect, useMemo, useState } from 'react';

import { AddEditSectionSheet } from './add-edit-section-sheet';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Page } from '@/lib/dto/common.dto'; // Corrected to Page
import { PageHeader } from '@/components/ui/page-header';
import { PlusCircle } from 'lucide-react'; // Imported PlusCircle
import { SectionDto } from '@/lib/dto/admin/school-management/sections.dto';
import { getColumns } from './columns';
import { useDebounce } from '@/hooks/use-debounce';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { useGetAcademicYears } from '@/lib/api/admin/school-management/academic-years-api';
import { useGetBranches } from '@/lib/api/admin/school-management/branches-api';
import { getAllGradeLevelsList } from '@/lib/api/admin/grade-levels';
import { useQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';

export default function SectionsPage() {
  const t = useTranslations('AdminSectionsPage'); // Updated to use new main key
  const tShared = useTranslations('Shared'); // Using the new Shared translations
  const deleteSectionMutation = useDeleteSection();
  const { confirm: showDeleteConfirmation, ConfirmDialog } = useDeleteConfirmation();

  // Fetch data for filter dropdowns
  const { data: academicYearsData, isLoading: isLoadingAcademicYears } = useGetAcademicYears({ size: 100 });
  const { data: branchesData, isLoading: isLoadingBranches } = useGetBranches({ size: 100 });
  const { data: gradeLevelsData, isLoading: isLoadingGradeLevels } = useQuery({
    queryKey: ['grade-levels-all'],
    queryFn: getAllGradeLevelsList,
  });

  const academicYears = useMemo(() => {
    if (!academicYearsData) return [];
    if (Array.isArray(academicYearsData)) return academicYearsData;
    if ('content' in academicYearsData) return academicYearsData.content;
    return [];
  }, [academicYearsData]);

  const branches = useMemo(() => {
    if (!branchesData) return [];
    if (Array.isArray(branchesData)) return branchesData;
    if ('content' in branchesData) return branchesData.content;
    return [];
  }, [branchesData]);

  const gradeLevels = useMemo(() => {
    return gradeLevelsData || [];
  }, [gradeLevelsData]);

  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [editingSection, setEditingSection] = useState<SectionDto | undefined>(undefined);

  // Filters
  const [academicYearId, setAcademicYearId] = useState<string | undefined>(undefined);
  const [branchId, setBranchId] = useState<string | undefined>(undefined);
  const [gradeLevelId, setGradeLevelId] = useState<string | undefined>(undefined);

  // Pagination state
  const [pageIndex, setPageIndex] = useState(DEFAULT_PAGE_INDEX);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

  // Initialize filter values when data is loaded
  useEffect(() => {
    if (academicYears.length > 0 && !academicYearId) {
      setAcademicYearId(academicYears[0].id);
    }
  }, [academicYears, academicYearId]);

  useEffect(() => {
    if (branches.length > 0 && !branchId) {
      setBranchId(branches[0].id);
    }
  }, [branches, branchId]);

  useEffect(() => {
    if (gradeLevels.length > 0 && !gradeLevelId) {
      setGradeLevelId(gradeLevels[0].id);
    }
  }, [gradeLevels, gradeLevelId]);

  const debouncedFilters = useDebounce(
    {
      academicYearId,
      branchId,
      gradeLevelId,
      page: pageIndex,
      size: pageSize,
    },
    500
  );

  const { data: sectionsData, isLoading, error } = useGetSections(
    {
      ...debouncedFilters,
      // Ensure undefined is not passed if a filter is not set, or handle in useGetSections
      academicYearId: debouncedFilters.academicYearId || undefined,
      branchId: debouncedFilters.branchId || undefined,
      gradeLevelId: debouncedFilters.gradeLevelId || undefined,
    },
    !!(debouncedFilters.academicYearId && debouncedFilters.branchId && debouncedFilters.gradeLevelId) // Enable only if all required filters are set
  );

  const sections = useMemo(() => {
    if (sectionsData && Array.isArray(sectionsData)) {
      return sectionsData; // For the endpoint without /all
    }
    if (sectionsData && 'content' in sectionsData) {
      return (sectionsData as Page<SectionDto>).content; // For the /all endpoint
    }
    return [];
  }, [sectionsData]);

  const pageCount = useMemo(() => {
    if (sectionsData && 'totalPages' in sectionsData) {
      return (sectionsData as Page<SectionDto>).totalPages;
    }
    return 0;
  }, [sectionsData]);


  const handleEdit = (section: SectionDto) => {
    setEditingSection(section);
    setIsSheetOpen(true);
  };

  const handleAddNew = () => {
    setEditingSection(undefined);
    setIsSheetOpen(true);
  };

  const tTable = useTranslations('AdminSectionsPage.table'); // Moved useTranslations out
  const columns: ColumnDef<SectionDto>[] = useMemo(() => getColumns({
    onEdit: handleEdit,
    t: tTable, // Pass table specific translations
    tShared,
    deleteSectionMutation: deleteSectionMutation.mutateAsync,
    showDeleteConfirmation,
  }), [tShared, tTable, handleEdit, deleteSectionMutation, showDeleteConfirmation]);

  const table = useReactTable({
    data: sections,
    columns,
    pageCount: pageCount,
    state: {
      pagination: {
        pageIndex,
        pageSize,
      },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const newState = updater({ pageIndex, pageSize });
        setPageIndex(newState.pageIndex);
        setPageSize(newState.pageSize);
      } else {
        setPageIndex(updater.pageIndex);
        setPageSize(updater.pageSize);
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    getSortedRowModel: getSortedRowModel(),
  });

  // TODO: Replace with actual Select components and data fetching for filters
  const renderFilters = () => (
    <div className="flex gap-4 mb-4 p-4 border rounded-lg bg-card">
        <div className="w-full md:w-1/3">
          <label htmlFor="academicYear" className="block text-sm font-medium text-gray-700">{t('filterByAcademicYear')}</label>
          <Select value={academicYearId || 'all'} onValueChange={value => setAcademicYearId(value === 'all' ? undefined : value)}>
            <SelectTrigger className="mt-1 w-full">
              <SelectValue placeholder={t('allAcademicYears')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('allAcademicYears')}</SelectItem>
              {isLoadingAcademicYears && <SelectItem value="loading" disabled>{tShared('Messages.loading')}</SelectItem>}
              {!isLoadingAcademicYears && academicYears.map(ay => <SelectItem key={ay.id} value={ay.id}>{ay.name}</SelectItem>)}
            </SelectContent>
          </Select>
        </div>

        <div className="w-full md:w-1/3">
          <label htmlFor="branch" className="block text-sm font-medium text-gray-700">{t('filterByBranch')}</label>
          <Select value={branchId || 'all'} onValueChange={value => setBranchId(value === 'all' ? undefined : value)}>
            <SelectTrigger className="mt-1 w-full">
              <SelectValue placeholder={t('allBranches')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('allBranches')}</SelectItem>
              {isLoadingBranches && <SelectItem value="loading" disabled>{tShared('Messages.loading')}</SelectItem>}
              {!isLoadingBranches && branches.map(b => <SelectItem key={b.id} value={b.id}>{b.nameEn}</SelectItem>)}
            </SelectContent>
          </Select>
        </div>

        <div className="w-full md:w-1/3">
          <label htmlFor="gradeLevel" className="block text-sm font-medium text-gray-700">{t('filterByGradeLevel')}</label>
          <Select value={gradeLevelId || 'all'} onValueChange={value => setGradeLevelId(value === 'all' ? undefined : value)}>
            <SelectTrigger className="mt-1 w-full">
              <SelectValue placeholder={t('allGradeLevels')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('allGradeLevels')}</SelectItem>
              {isLoadingGradeLevels && <SelectItem value="loading" disabled>{tShared('Messages.loading')}</SelectItem>}
              {!isLoadingGradeLevels && gradeLevels.map(gl => <SelectItem key={gl.id} value={gl.id}>{gl.nameEn}</SelectItem>)}
            </SelectContent>
          </Select>
        </div>
    </div>
  );

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <PageHeader title={t('title')} />
        <Button onClick={handleAddNew}>
          <PlusCircle className="mr-2 h-4 w-4" /> {t('addNewButton')}
        </Button>
      </div>

      {renderFilters()}

      {isLoading && <p>{tShared('Messages.loading')}</p>}
      {error && <p className="text-red-500">{tShared('Messages.error', { errorDetails: error.message })}</p>}

      {!isLoading && !error && (
        <DataTable
          table={table}
          columns={columns as ColumnDef<unknown>[]}
        />
      )}

      <AddEditSectionSheet
        isOpen={isSheetOpen}
        onClose={() => setIsSheetOpen(false)}
        section={editingSection}
        // Pass necessary IDs for creation context if not editing
        // These might come from the selected filters
        defaultAcademicYearId={academicYearId}
        defaultBranchId={branchId}
        defaultGradeLevelId={gradeLevelId}
      />
      <ConfirmDialog />
    </div>
  );
}
