"use client";

import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Pen, Eye, UserRound } from "lucide-react";
import { GuardianDto } from "@/lib/dto/admin/guardian.dto";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { EditGuardianDialog } from "./edit-dialog";
import { useRouter } from "next/navigation";

interface DataTableRowActionsProps {
    row: Row<GuardianDto>;
}

export function DataTableRowActions({ row }: DataTableRowActionsProps) {
    const t = useTranslations("AdminGuardiansPage.rowActions");
    const guardian = row.original;
    const queryClient = useQueryClient();
    const router = useRouter();
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

    // State to control client-side only rendering
    const [isClient, setIsClient] = useState(false);

    // Set isClient to true after component mounts
    useEffect(() => {
        setIsClient(true);
    }, []);

    // Function to handle successful edit
    const handleSuccess = () => {
        setIsEditDialogOpen(false);
        // Invalidate the query to refetch data
        queryClient.invalidateQueries({ queryKey: ['guardians'] });
        toast.success(t("editSuccess", { name: `${guardian.userAccount.firstName} ${guardian.userAccount.lastName}` }));
    };

    // Function to view guardian details
    const viewGuardianDetails = () => {
        // This would navigate to a details page if you have one
        toast.info(t("viewingDetails", { name: `${guardian.userAccount.firstName} ${guardian.userAccount.lastName}` }));
        // Example: router.push(`/admin/guardians/${guardian.id}`);
    };

    // Function to view associated students
    const viewAssociatedStudents = () => {
        if (guardian.students.length > 0) {
            // This would navigate to a filtered students page
            toast.info(t("viewingStudents", { name: `${guardian.userAccount.firstName} ${guardian.userAccount.lastName}` }));
            // Example: router.push(`/admin/students?guardianId=${guardian.id}`);
        } else {
            toast.warning(t("noStudentsToView"));
        }
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">{t("openMenu")}</span>
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                        <Pen className="mr-2 h-4 w-4" />
                        {t("edit")}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={viewGuardianDetails}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t("viewDetails")}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={viewAssociatedStudents}>
                        <UserRound className="mr-2 h-4 w-4" />
                        {t("viewStudents")}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Edit Dialog - only render on client side */}
            {isClient && (
                <EditGuardianDialog
                    isOpen={isEditDialogOpen}
                    setIsOpen={setIsEditDialogOpen}
                    guardian={guardian}
                    onSuccess={handleSuccess}
                />
            )}
        </>
    );
}
