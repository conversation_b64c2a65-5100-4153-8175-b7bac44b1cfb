"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { useTranslations } from "next-intl";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ReportType } from "@/lib/dto/admin/accounting/ai-reports.dto";
import { X } from "lucide-react";

interface DataTableToolbarProps {
  dateRange: DateRange | undefined;
  onDateRangeChange: (date: DateRange | undefined) => void;
  reportType: ReportType | undefined;
  onReportTypeChange: (type: ReportType | undefined) => void;
  onGenerateReport: () => void;
  isGenerating: boolean;
  onResetFilters: () => void;
}

export function DataTableToolbar({
  dateRange,
  onDateRangeChange,
  reportType,
  onReportTypeChange,
  onGenerateReport,
  isGenerating,
  onResetFilters,
}: DataTableToolbarProps) {
  const t = useTranslations("AiReportsPage");
  const tSharedDate = useTranslations("Shared.dateRangePicker");
  
  const isFiltered = !!dateRange || !!reportType;

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <DateRangePicker
          date={dateRange}
          onDateChange={onDateRangeChange}
          placeholder={t("selectDateRange")}
          className="w-full sm:w-auto"
        />
        
        <Select
          value={reportType}
          onValueChange={(value) => onReportTypeChange(value as ReportType)}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder={t("selectReportType")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="GENERAL_LEDGER">{t("general_ledger")}</SelectItem>
            <SelectItem value="EXPENSES">{t("expenses")}</SelectItem>
            <SelectItem value="INCOME_STATEMENT">{t("income_statement")}</SelectItem>
            <SelectItem value="TRIAL_BALANCE">{t("trial_balance")}</SelectItem>
            <SelectItem value="BALANCE_SHEET">{t("balance_sheet")}</SelectItem>
          </SelectContent>
        </Select>
        
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={onResetFilters}
            className="h-8 px-2 lg:px-3"
          >
            <X className="h-4 w-4" />
            <span className="ml-2">{t("resetFilters")}</span>
          </Button>
        )}
      </div>
      
      <Button
        onClick={onGenerateReport}
        disabled={!dateRange?.from || !dateRange?.to || !reportType || isGenerating}
      >
        {isGenerating ? t("generating") : t("generateReport")}
      </Button>
    </div>
  );
}
