"use client";

import * as React from "react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Save, X } from "lucide-react";
import { assignPermissionsToRole, getAllRoles, getRoleById } from "@/lib/api/admin/roles";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { AddRoleDialog } from "@/components/admin/roles/add-role-dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { getAllPermissions } from "@/lib/api/admin/permissions";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

export default function RolesPage() {
  const t = useTranslations("AdminRolesPage");
  const queryClient = useQueryClient();

  const [selectedRoleId, setSelectedRoleId] = React.useState<string | null>(null);
  // State to hold the permissions currently checked in the UI for the selected role
  const [selectedPermissions, setSelectedPermissions] = React.useState<Set<string>>(new Set());
  // State to track if permissions have been changed from the fetched state
  const [permissionsChanged, setPermissionsChanged] = React.useState<boolean>(false);

  // Fetch all roles for the left list
  const { data: roles, isLoading: isLoadingRoles, isError: isErrorRoles, error: errorRoles } = useQuery({
    queryKey: ["adminRoles"],
    queryFn: getAllRoles,
  });

  // Fetch all available permissions for the right list
  const { data: allPermissions, isLoading: isLoadingPermissions } = useQuery({
    queryKey: ["adminPermissions"],
    queryFn: getAllPermissions,
  });

  // Fetch details of the selected role (including its permissions)
  const { data: selectedRoleDetails, isLoading: isLoadingSelectedRole, isFetching: isFetchingSelectedRole } = useQuery({
    queryKey: ["adminRoleDetails", selectedRoleId],
    queryFn: () => getRoleById(selectedRoleId!), // Non-null assertion as it's enabled only when selectedRoleId is set
    enabled: !!selectedRoleId, // Only run query when a role is selected
    onSuccess: (data) => {
      // When role details load, update the checked permissions state
      const currentPermissionNames = new Set(data.permissions.map(p => p.name));
      setSelectedPermissions(currentPermissionNames);
      setPermissionsChanged(false); // Reset changed state on new role load
    },
  });

  // Mutation to assign permissions
  const assignPermissionsMutation = useMutation({
    mutationFn: assignPermissionsToRole,
    onSuccess: (updatedRole) => {
      toast.success(t("savePermissionsSuccessToast", { roleName: updatedRole.name }));
      // Update the specific role's data in the cache
      queryClient.setQueryData(["adminRoleDetails", selectedRoleId], updatedRole);
      // Optionally refetch all roles if needed, though maybe not necessary just for permissions
      // queryClient.invalidateQueries({ queryKey: ["adminRoles"] });
      setPermissionsChanged(false); // Reset changed state after successful save
    },
    onError: (error) => {
      toast.error(t("savePermissionsErrorToast", { error: error.message }));
    },
  });

  // Handle checkbox change
  const handlePermissionChange = (permissionName: string, checked: boolean | "indeterminate") => {
    setSelectedPermissions(prev => {
      const newSet = new Set(prev);
      if (checked === true) {
        newSet.add(permissionName);
      } else {
        newSet.delete(permissionName);
      }
      // Check if the new set differs from the *currently selected role's* original permissions
      // Ensure selectedRoleDetails is available before comparing
      const originalPermissionNames = selectedRoleDetails
        ? new Set(selectedRoleDetails.permissions.map(p => p.name))
        : new Set<string>(); // Default to empty set if details aren't loaded yet

      setPermissionsChanged(!setsAreEqual(newSet, originalPermissionNames));
      return newSet;
    });
  };

  // Helper to compare sets
  const setsAreEqual = (setA: Set<string>, setB: Set<string>) => {
    if (setA.size !== setB.size) return false;
    for (const item of setA) {
      if (!setB.has(item)) return false;
    }
    return true;
  };


  // Handle saving permission changes
  const handleSaveChanges = () => {
    if (!selectedRoleId) return;
    assignPermissionsMutation.mutate({
      roleId: selectedRoleId,
      permissionNames: Array.from(selectedPermissions),
    });
  };

  // Handle canceling permission changes
  const handleCancelChanges = () => {
     if (!selectedRoleDetails) return;
     const currentPermissionNames = new Set(selectedRoleDetails.permissions.map(p => p.name));
     setSelectedPermissions(currentPermissionNames);
     setPermissionsChanged(false);
  };

  // Loading/Error states for Roles List
  const renderRolesList = () => {
    if (isLoadingRoles) {
      return Array.from({ length: 5 }).map((_, i) => <Skeleton key={i} className="h-10 w-full mb-2" />);
    }
    if (isErrorRoles) {
      return <p className="text-red-600">{t("errorLoadingRoles", { error: errorRoles?.message })}</p>;
    }
    if (!roles || roles.length === 0) {
      return <p className="text-muted-foreground">{t("noRolesFound")}</p>;
    }
    return roles.map((role) => (
      <Button
        key={role.id}
        variant={selectedRoleId === role.id ? "secondary" : "ghost"}
        className={cn(
            "w-full justify-start text-left h-auto py-2 px-3 mb-1",
            selectedRoleId === role.id && "font-semibold"
        )}
        onClick={() => {
            // Reset change tracking *before* selecting the new role
            setPermissionsChanged(false);
            // Optionally clear visual selection immediately, though onSuccess will handle the final state
            // setSelectedPermissions(new Set());
            setSelectedRoleId(role.id);
        }}
        disabled={isFetchingSelectedRole && selectedRoleId === role.id} // Disable while fetching this role's details
      >
        {role.name}
        {isFetchingSelectedRole && selectedRoleId === role.id && <Loader2 className="ml-auto h-4 w-4 animate-spin" />}
      </Button>
    ));
  };

   // Loading/Content states for Permissions List
  const renderPermissionsList = () => {
    if (!selectedRoleId) {
        return <p className="text-muted-foreground text-center mt-10">{t("selectRolePrompt")}</p>;
    }
    if (isLoadingPermissions || (isLoadingSelectedRole && !selectedRoleDetails)) { // Show skeleton if loading all permissions OR loading selected role initially
      return Array.from({ length: 10 }).map((_, i) => (
         <div key={i} className="flex items-center space-x-2 mb-3">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-48" />
         </div>
      ));
    }
    if (!allPermissions) {
        return <p className="text-red-600">{t("errorLoadingPermissions")}</p>;
    }

    // Determine the source of truth for checked state based on whether changes have been made
    // Use selectedPermissions if changed, otherwise derive directly from fetched details
    const sourcePermissions = permissionsChanged
        ? selectedPermissions
        : new Set(selectedRoleDetails?.permissions.map(p => p.name) ?? []);


    return allPermissions.map((permission) => (
      <div key={permission.id} className="flex items-center space-x-3 mb-2 rounded-md p-2 hover:bg-muted/50">
        <Checkbox
          id={`perm-${permission.id}`}
          // Use the determined source set for the checked state
          checked={sourcePermissions.has(permission.name)}
          onCheckedChange={(checked) => handlePermissionChange(permission.name, checked)}
          disabled={assignPermissionsMutation.isPending} // Disable while saving
        />
        <Label htmlFor={`perm-${permission.id}`} className="flex-1 cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {permission.name}
          {permission.description && <p className="text-xs text-muted-foreground mt-0.5">{permission.description}</p>}
        </Label>
      </div>
    ));
  };


  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-4 md:gap-6 h-[calc(100vh-theme(space.14)-theme(space.14))]"> {/* Adjust height based on navbar/footer */}

      {/* Left Section: Roles List */}
      <Card className="col-span-1 flex flex-col">
        <CardHeader className="border-b p-4">
          <div className="flex items-center justify-between gap-2">
             <CardTitle className="text-lg">{t("rolesTitle")}</CardTitle>
             <AddRoleDialog />
          </div>
        </CardHeader>
        <CardContent className="flex-1 p-2 overflow-hidden">
           <ScrollArea className="h-full p-2">
             {renderRolesList()}
           </ScrollArea>
        </CardContent>
      </Card>

      {/* Right Section: Permissions */}
      <Card className="md:col-span-2 lg:col-span-3 flex flex-col">
         <CardHeader className="border-b p-4">
            <div className="flex items-center justify-between gap-4">
                <div>
                    <CardTitle className="text-lg">{t("permissionsTitle")}</CardTitle>
                    <CardDescription>{selectedRoleDetails ? t("permissionsDescription", { roleName: selectedRoleDetails.name }) : t("permissionsDescriptionDefault")}</CardDescription>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancelChanges}
                        disabled={!permissionsChanged || assignPermissionsMutation.isPending}
                    >
                        <X className="mr-1 h-4 w-4" />
                        {t("cancelButton")}
                    </Button>
                    <Button
                        size="sm"
                        onClick={handleSaveChanges}
                        disabled={!permissionsChanged || assignPermissionsMutation.isPending || !selectedRoleId}
                    >
                        {assignPermissionsMutation.isPending ? (
                            <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                        ) : (
                            <Save className="mr-1 h-4 w-4" />
                        )}
                        {t("saveButton")}
                    </Button>
                </div>
            </div>
         </CardHeader>
         <CardContent className="flex-1 p-2 overflow-hidden">
            <ScrollArea className="h-full p-4">
                {renderPermissionsList()}
            </ScrollArea>
         </CardContent>
      </Card>
    </div>
  );
}
