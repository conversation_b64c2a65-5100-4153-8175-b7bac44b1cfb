"use client";

import { Table } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options";
import { X } from "lucide-react";
import { AddPaymentMethodDialog } from "./add-dialog"; // Import the Add Dialog
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface DataTableToolbarProps<TData> {
    table: Table<TData>;
    globalFilter: string;
    setGlobalFilter: (value: string) => void;
    showActiveOnly: boolean;
    setShowActiveOnly: (value: boolean) => void;
}

export function DataTableToolbar<TData>({
    table,
    globalFilter,
    setGlobalFilter,
    showActiveOnly,
    setShowActiveOnly,
}: DataTableToolbarProps<TData>) {
    const t = useTranslations("AdminPaymentMethodsPage"); // Use correct translation scope
    const isFiltered = globalFilter !== '' || table.getState().columnFilters.length > 0;

    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
                <Input
                    placeholder={t("searchPlaceholder")} // Use correct placeholder
                    value={globalFilter ?? ""}
                    onChange={(event) => setGlobalFilter(event.target.value)}
                    className="h-8 w-[150px] lg:w-[250px]"
                />
                {isFiltered && (
                    <Button
                        variant="ghost"
                        onClick={() => {
                            setGlobalFilter('');
                            table.resetColumnFilters();
                        }}
                        className="h-8 px-2 lg:px-3"
                    >
                        {t("resetFilters")} {/* Add translation for reset */}
                        <X className="ml-2 h-4 w-4" />
                    </Button>
                )}
            </div>
            <div className="flex items-center space-x-2">
                 <div className="flex items-center space-x-2">
                    <Switch
                        id="active-only-switch"
                        checked={showActiveOnly}
                        onCheckedChange={setShowActiveOnly}
                    />
                    <Label htmlFor="active-only-switch">{t("showActiveOnly")}</Label>
                </div>
                <DataTableViewOptions table={table} />
                <AddPaymentMethodDialog /> {/* Use correct Add Dialog */}
            </div>
        </div>
    );
}
