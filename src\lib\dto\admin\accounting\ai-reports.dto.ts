export type ReportType = 
  | "GENERAL_LEDGER" 
  | "EXPENSES" 
  | "INCOME_STATEMENT" 
  | "TRIAL_BALANCE" 
  | "BALANCE_SHEET";

export type ReportStatus = "PENDING" | "COMPLETED" | "FAILED";

export interface AiReportDto {
  id: string;
  reportType: ReportType;
  startDate: string;
  endDate: string;
  filters: Record<string, any>;
  content: string;
  status: ReportStatus;
  fromCache: boolean;
  createdDate: string;
  lastModifiedDate: string;
}

export interface CreateAiReportRequest {
  reportType: ReportType;
  startDate: string;
  endDate: string;
  filters?: Record<string, any>;
}

export interface GetAiReportsParams {
  reportType?: ReportType;
  startDate?: string;
  endDate?: string;
  page?: number;
  size?: number;
  sort?: string[];
}

export interface PageAiReportDto {
  content: AiReportDto[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    sort: {
      empty: boolean;
      unsorted: boolean;
      sorted: boolean;
    };
    offset: number;
    unpaged: boolean;
    paged: boolean;
  };
  last: boolean;
  totalPages: number;
  totalElements: number;
  size: number;
  number: number;
  sort: {
    empty: boolean;
    unsorted: boolean;
    sorted: boolean;
  };
  numberOfElements: number;
  first: boolean;
  empty: boolean;
}
