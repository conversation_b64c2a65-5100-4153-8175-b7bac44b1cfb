import {Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle} from "@/components/ui/card";

import { JournalEntriesTable } from "./journal-entries-table";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export const metadata: Metadata = {
    // TODO: Add translations
    title: "Journal Entries",
};

export default async function JournalEntriesPage() {
    const t = await getTranslations("AdminJournalEntriesPage");

    // return (
    //     <div className="container mx-auto py-10">
    //         <PageHeader>
    //             <PageHeaderHeading>{t("title")}</PageHeaderHeading>
    //         </PageHeader>
    //         <JournalEntriesTable />
    //     </div>
    // );
    return (
        <Card>
            <CardHeader>
                <CardTitle>{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent>
                <JournalEntriesTable />
            </CardContent>
        </Card>
    );
}
