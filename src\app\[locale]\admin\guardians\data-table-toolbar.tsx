"use client";

import { useEffect, useState } from "react";

import { AddGuardianDialog } from "./add-dialog";
import { AddGuardianWithUserDialog } from "./add-guardian-with-user-dialog";
import { Button } from "@/components/ui/button";
import { Cross2Icon } from "@radix-ui/react-icons";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options";
import { Input } from "@/components/ui/input";
import { Table } from "@tanstack/react-table";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

interface DataTableToolbarProps<TData> {
    table: Table<TData>;
}

export function DataTableToolbar<TData>({
    table,
}: DataTableToolbarProps<TData>) {
    const t = useTranslations("AdminGuardiansPage.toolbar");
    const tShared = useTranslations("Shared.dataTable");
    const queryClient = useQueryClient();

    // State to control client-side only rendering
    const [isClient, setIsClient] = useState(false);

    // Set isClient to true after component mounts
    useEffect(() => {
        setIsClient(true);
    }, []);

    const isFiltered = table.getState().columnFilters.length > 0;
    // Function to handle successful add/edit
    const handleSuccess = () => {
        // Invalidate the query to refetch data
        queryClient.invalidateQueries({ queryKey: ['guardians'] });
    };
    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
                <Input
                    placeholder={t("searchPlaceholder")}
                    value={(table.getColumn("email")?.getFilterValue() as string) ?? ""}
                    onChange={(event) =>
                        table.getColumn("email")?.setFilterValue(event.target.value)
                    }
                    className="h-8 w-[150px] lg:w-[250px]"
                />
                {isFiltered && (
                    <Button
                        variant="ghost"
                        onClick={() => table.resetColumnFilters()}
                        className="h-8 px-2 lg:px-3"
                    >
                        {tShared("resetFilters")}
                        <Cross2Icon className="ml-2 h-4 w-4" />
                    </Button>
                )}
            </div>
            <div className="flex items-center space-x-2">
                <DataTableViewOptions table={table} />
                {isClient && (
                    <>
                        <AddGuardianDialog onSuccess={handleSuccess} />
                        <AddGuardianWithUserDialog onSuccess={handleSuccess} />
                    </>
                )}
            </div>
        </div>
    );
}
