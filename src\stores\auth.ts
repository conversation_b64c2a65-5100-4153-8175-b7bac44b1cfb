import { create } from 'zustand';
import { persist, type PersistStorage } from 'zustand/middleware';
import Cookies from 'js-cookie';

// Based on AuthResponse in api-docs.json
interface AuthState {
  accessToken: string | null;
  userId: string | null;
  email: string | null;
  firstName: string | null;
  lastName: string | null;
  roles: string[];
  permissions: string[];
  isHydrated: boolean; // Add hydration status flag
  isAuthenticated: boolean; // Explicit flag for authentication status
  setAuth: (auth: AuthResponse | null) => void;
  logout: () => void;
  checkAuth: () => boolean; // Helper function to check auth status
}

// Based on AuthResponse schema in api-docs.json
export interface AuthResponse {
  accessToken: string;
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  permissions: string[];
}

const initialState = {
    accessToken: null,
    userId: null,
    email: null,
    firstName: null,
    lastName: null,
    roles: [],
    permissions: [],
    isHydrated: false, // Initialize hydration status
    isAuthenticated: false, // Initialize authentication status
};

// <PERSON>ie name constant - must match middleware.ts
export const AUTH_COOKIE_NAME = 'auth-storage';

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({ // Need 'get' for onRehydrateStorage if accessing state
      ...initialState,
      isHydrated: false, // Ensure initial state within persist also has it
      isAuthenticated: false, // Ensure initial state has authentication status

      setAuth: (auth) => {
        if (auth) {
          // First, ensure the cookie is removed to prevent stale data
          Cookies.remove(AUTH_COOKIE_NAME);

          // Set the new auth state
          set({
            accessToken: auth.accessToken,
            userId: auth.userId,
            email: auth.email,
            firstName: auth.firstName,
            lastName: auth.lastName,
            roles: auth.roles || [],
            permissions: auth.permissions || [],
            isHydrated: true, // Ensure isHydrated is set to true when setting auth
            isAuthenticated: true, // Set authenticated flag
          });

          // Log the auth state for debugging
          console.log('[AuthStore] Auth set successfully:', {
            userId: auth.userId,
            hasToken: !!auth.accessToken,
            roles: auth.roles,
            permissions: auth.permissions,
          });

          // Force cookie to be set immediately
          const state = get();
          const stateForStorage = {
            state: {
              ...state,
              isHydrated: true,
              isAuthenticated: true,
            }
          };

          // Set cookie with path to ensure it's available for all routes
          Cookies.set(AUTH_COOKIE_NAME, JSON.stringify(stateForStorage), {
            expires: 7, // 7 days
            path: '/',
            // secure: process.env.NODE_ENV === 'production',
            // sameSite: 'lax',
          });

          console.log('[AuthStore] Cookie set manually after auth');
        } else {
          // Clear auth state
          set(initialState);
          // Remove cookie
          Cookies.remove(AUTH_COOKIE_NAME, { path: '/' });
          console.log('[AuthStore] Auth cleared');
        }
      },

      logout: () => {
        // Clear all auth data and reset to initial state
        set({
          ...initialState,
          isHydrated: true, // Keep hydrated state true
        });

        // Force cookie removal to ensure clean logout
        Cookies.remove(AUTH_COOKIE_NAME, { path: '/' });
        console.log('[AuthStore] Logged out, cookie removed');

        // Force page reload to clear any cached state
        window.location.href = '/';
      },

      // Helper function to check if user is authenticated
      checkAuth: () => {
        const state = get();
        const isAuth = state.isHydrated && state.isAuthenticated && !!state.accessToken;
        console.log('[AuthStore] checkAuth:', {
          isHydrated: state.isHydrated,
          isAuthenticated: state.isAuthenticated,
          hasToken: !!state.accessToken,
          result: isAuth
        });
        return isAuth;
      },
    }),
    {
      name: AUTH_COOKIE_NAME, // unique name for the cookie
      storage: {
        getItem: (name) => {
          const str = Cookies.get(name);
          console.log(`[AuthStore] Getting cookie '${name}':`, !!str);
          if (!str) {
            return null;
          }
          try {
            return JSON.parse(str);
          } catch (e) {
            console.error(`[AuthStore] Error parsing cookie '${name}':`, e);
            return null;
          }
        },
        setItem: (name, value) => {
          console.log(`[AuthStore] Setting cookie '${name}'`);
          // Set cookie with path to ensure it's available for all routes
          Cookies.set(name, JSON.stringify(value), {
            expires: 7, // 7 days
            path: '/',
            // secure: process.env.NODE_ENV === 'production',
            // sameSite: 'lax',
          });
        },
        removeItem: (name) => {
          console.log(`[AuthStore] Removing cookie '${name}'`);
          Cookies.remove(name, { path: '/' });
        },
      } as PersistStorage<AuthState>,
      onRehydrateStorage: () => {
        console.log('[AuthStore] Hydration process: persist middleware is preparing the rehydration listener.');
        return (rehydratedStateFromStorage, error) => {
          if (error) {
            console.error('[AuthStore] Hydration listener: Error reported during rehydration from storage.', error);
          } else if (rehydratedStateFromStorage) {
            console.log('[AuthStore] Hydration listener: Successfully read state from storage. This state will be merged.', {
              hasToken: !!rehydratedStateFromStorage.accessToken,
              userId: rehydratedStateFromStorage.userId,
            });
          } else {
            console.log('[AuthStore] Hydration listener: No state found in storage, or storage is empty.');
          }
          // Note: Actual state updates for isHydrated and isAuthenticated are now handled by onFinishHydration
        };
      },
    }
  )
);

// Attach a listener to set hydration and authentication status once persist is done
useAuthStore.persist.onFinishHydration((state) => {
  // `state` here is the fully rehydrated state after persist has applied it.
  useAuthStore.setState({
    isHydrated: true,
    isAuthenticated: !!state.accessToken, // Derive from rehydrated accessToken
  });
  console.log('[AuthStore] onFinishHydration: Store hydration complete. isHydrated and isAuthenticated updated.', {
    isHydrated: true, // Current state after setState
    isAuthenticated: !!state.accessToken, // Based on the rehydrated state
    userId: state.userId, // From rehydrated state
  });
});
