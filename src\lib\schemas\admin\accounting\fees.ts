import { z } from "zod";
import { UseTranslations } from "next-intl";

// Helper function for date validation messages
const dateRequiredError = (t: UseTranslations<string>, fieldName: string) => ({
    required_error: t(`${fieldName}Required`),
    invalid_type_error: t(`${fieldName}InvalidDate`),
});

// Base schema for common fields
const baseFeeSchema = (t: UseTranslations<string>) => ({
    nameEn: z.string()
        .min(1, { message: t("validation.nameEnRequired") })
        .max(255, { message: t("validation.nameEnTooLong") }),
    nameAr: z.string()
        .min(1, { message: t("validation.nameArRequired") })
        .max(255, { message: t("validation.nameArTooLong") }),
    descriptionEn: z.string()
        .max(1000, { message: t("validation.descriptionEnTooLong") })
        .optional().nullable(),
    descriptionAr: z.string()
        .max(1000, { message: t("validation.descriptionArTooLong") })
        .optional().nullable(),
    amount: z.coerce // Use coerce for number inputs from forms
        .number({ required_error: t("validation.amountRequired"), invalid_type_error: t("validation.amountInvalid") })
        .min(0, { message: t("validation.amountMin") }),
    academicYear: z.string()
        .min(1, { message: t("validation.academicYearRequired") })
        .max(50, { message: t("validation.academicYearTooLong") })
        // Optional: Add regex validation if a specific format like YYYY-YYYY is required
        .regex(/^\d{4}-\d{4}$/, { message: t("validation.academicYearFormat") }),
    dueDate: z.date(dateRequiredError(t, "validation.dueDate")),
    feeCategoryId: z.string().uuid({ message: t("validation.feeCategoryRequired") }),
    applicableGradeId: z.string().uuid().optional().nullable(),
    applicableStageId: z.string().uuid().optional().nullable(),
    applicableBranchId: z.string().uuid().optional().nullable(),
});

// Schema for creating a fee
export const createFeeSchema = (t: UseTranslations<string>) => z.object({
    ...baseFeeSchema(t),
});

// Schema for updating a fee (most fields optional, but IDs are required if present)
export const updateFeeSchema = (t: UseTranslations<string>) => z.object({
    nameEn: z.string()
        .min(1, { message: t("validation.nameEnRequired") })
        .max(255, { message: t("validation.nameEnTooLong") })
        .optional(),
    nameAr: z.string()
        .min(1, { message: t("validation.nameArRequired") })
        .max(255, { message: t("validation.nameArTooLong") })
        .optional(),
    descriptionEn: z.string()
        .max(1000, { message: t("validation.descriptionEnTooLong") })
        .optional().nullable(),
    descriptionAr: z.string()
        .max(1000, { message: t("validation.descriptionArTooLong") })
        .optional().nullable(),
    amount: z.coerce
        .number({ invalid_type_error: t("validation.amountInvalid") })
        .min(0, { message: t("validation.amountMin") })
        .optional(),
    academicYear: z.string()
        .min(1, { message: t("validation.academicYearRequired") })
        .max(50, { message: t("validation.academicYearTooLong") })
        .regex(/^\d{4}-\d{4}$/, { message: t("validation.academicYearFormat") })
        .optional(),
    dueDate: z.date(dateRequiredError(t, "validation.dueDate")).optional(),
    feeCategoryId: z.string().uuid({ message: t("validation.feeCategoryRequired") }).optional(),
    applicableGradeId: z.string().uuid().optional().nullable(),
    applicableStageId: z.string().uuid().optional().nullable(),
    applicableBranchId: z.string().uuid().optional().nullable(),
    active: z.boolean().optional(),
});


// Infer TypeScript types from schemas
export type CreateFeeInput = z.infer<ReturnType<typeof createFeeSchema>>;
export type UpdateFeeInput = z.infer<ReturnType<typeof updateFeeSchema>>;
