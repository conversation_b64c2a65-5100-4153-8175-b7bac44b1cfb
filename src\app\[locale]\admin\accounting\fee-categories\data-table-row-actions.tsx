"use client";

import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Pen, Trash } from "lucide-react";
import { FeeCategoryDto } from "@/lib/dto/admin/accounting/fee-categories.dto"; // Adjust DTO import
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteFeeCategory } from "@/lib/api/admin/accounting/fee-categories"; // Adjust API import
import { toast } from "sonner";
import { useState } from "react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, Al<PERSON>D<PERSON>ogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { EditFeeCategoryDialog } from "./edit-dialog"; // Import Edit Dialog

interface DataTableRowActionsProps<TData> {
    row: Row<TData>;
}

export function DataTableRowActions<TData>({
    row,
}: DataTableRowActionsProps<TData>) {
    const category = row.original as FeeCategoryDto; // Use specific type
    const t = useTranslations("AdminFeeCategoriesPage.table.actions"); // Adjust translation namespace
    const tConfirm = useTranslations("Shared.confirmationDialog");
    const queryClient = useQueryClient();
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

    const deleteMutation = useMutation({ // Renamed for clarity
        mutationFn: deleteFeeCategory, // Use correct API function
        onSuccess: () => {
            toast.success(t("deleteSuccessToast", { name: category.nameEn })); // Adjust success message
            queryClient.invalidateQueries({ queryKey: ["fee-categories"] }); // Adjust query key
        },
        onError: (error) => {
            toast.error(t("deleteErrorToast", { error: error.message })); // Adjust error message
        },
        onSettled: () => {
            setIsDeleteDialogOpen(false);
        }
    });

    const handleDelete = () => {
        deleteMutation.mutate(category.id);
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                    >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{t("openMenu")}</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                        <Pen className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("edit")}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        variant="destructive"
                        onClick={() => setIsDeleteDialogOpen(true)}
                        disabled={deleteMutation.isPending} // Disable while deleting
                    >
                        <Trash className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("delete")}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Edit Dialog */}
            <EditFeeCategoryDialog
                category={category}
                isOpen={isEditDialogOpen}
                onOpenChange={setIsEditDialogOpen}
            />

            {/* Confirmation Dialog for Delete */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{tConfirm("title")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {/* Adjust item name */}
                            {tConfirm("deleteMessage", { item: `${t("feeCategoryItem")} '${category.nameEn}'` })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={deleteMutation.isPending}>{tConfirm("cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            disabled={deleteMutation.isPending}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {deleteMutation.isPending ? tConfirm("deleting") : tConfirm("delete")}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
