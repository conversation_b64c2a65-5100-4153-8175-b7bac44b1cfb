"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetT<PERSON>le,
    SheetFooter,
    SheetClose,
} from "@/components/ui/sheet";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { AccountCombobox } from "@/components/ui/combobox-coa"; // Use the COA combobox
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"; // For category dropdown
import { createExpense, getExpenseById, updateExpense } from "@/lib/api/admin/accounting/expenses";
import { CreateExpenseInput, UpdateExpenseInput, createExpenseSchema, updateExpenseSchema } from "@/lib/schemas/admin/accounting/expenses";
import { getErrorMessage, formatCurrency } from "@/lib/utils"; // Import formatCurrency
import { getAllExpenseCategoriesList } from "@/lib/api/admin/accounting/expense-categories";
import { getAllActiveTaxesForSelect } from "@/lib/api/admin/accounting/taxes"; // Import tax fetch function
import { SimpleExpenseCategoryDto } from "@/lib/dto/admin/accounting/expense-categories.dto";
import { TaxDto } from "@/lib/dto/admin/accounting/taxes.dto"; // Import TaxDto
import { Skeleton } from "@/components/ui/skeleton";

interface AddEditExpenseSheetProps {
    expenseId?: string; // ID for editing, undefined for adding
    isOpen: boolean;
    onOpenChange: (isOpen: boolean) => void;
    onSuccess?: () => void; // Optional callback on successful add/edit
    children?: React.ReactNode; // To allow triggering the sheet with a button
}

// Helper function to fetch expense categories
async function fetchExpenseCategories(): Promise<SimpleExpenseCategoryDto[]> {
    try {
        return await getAllExpenseCategoriesList();
    } catch (error) {
        console.error("Failed to fetch expense categories:", error);
        toast.error("Failed to load expense categories for selection.");
        return []; // Return empty array on error
    }
}

// Helper function to fetch active taxes
async function fetchActiveTaxes(): Promise<TaxDto[]> {
    try {
        // Assuming a function exists to get *all* active taxes efficiently
        // If not, you might need to fetch paginated and combine, or adjust the API
        return await getAllActiveTaxesForSelect();
    } catch (error) {
        console.error("Failed to fetch taxes:", error);
        toast.error("Failed to load taxes for selection.");
        return [];
    }
}

export function AddEditExpenseSheet({
    expenseId,
    isOpen,
    onOpenChange,
    onSuccess,
    children,
}: AddEditExpenseSheetProps) {
    const t = useTranslations("AdminExpensesPage");
    const tValidation = useTranslations("AdminExpensesPage.validation");
    const queryClient = useQueryClient();
    const isEditMode = !!expenseId;

    const formSchema = isEditMode ? updateExpenseSchema(tValidation) : createExpenseSchema(tValidation);
    type FormValues = z.infer<typeof formSchema>;

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: isEditMode ? undefined : { // Set defaults only for add mode
            expenseDate: new Date(),
            amount: 0,
            descriptionEn: "",
            descriptionAr: "",
            vendor: null,
            referenceNumber: null,
            categoryId: "",
            paymentAccountId: "",
            taxId: null, // Default taxId to null
        },
    });

    // Fetch existing expense data if in edit mode
    const { data: existingExpense, isLoading: isLoadingExpense, isError: isErrorExpense } = useQuery({
        queryKey: ["expense", expenseId],
        queryFn: () => getExpenseById(expenseId!),
        enabled: isEditMode && isOpen, // Only fetch when editing and sheet is open
        staleTime: Infinity, // Don't refetch automatically unless invalidated
    });

    // Fetch expense categories for dropdown
    const { data: categories, isLoading: isLoadingCategories } = useQuery({
        queryKey: ["expenseCategoriesList"],
        queryFn: fetchExpenseCategories,
        enabled: isOpen, // Fetch when sheet opens
        staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    });

    // Fetch active taxes for dropdown
    const { data: taxes, isLoading: isLoadingTaxes } = useQuery({
        queryKey: ["activeTaxesList"],
        queryFn: fetchActiveTaxes,
        enabled: isOpen, // Fetch when sheet opens
        staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    });

    // Reset form when existing data is loaded in edit mode
    useEffect(() => {
        if (isEditMode && existingExpense) {
            form.reset({
                expenseDate: new Date(existingExpense.expenseDate), // Convert string to Date
                amount: existingExpense.amount,
                descriptionEn: existingExpense.descriptionEn,
                descriptionAr: existingExpense.descriptionAr,
                vendor: existingExpense.vendor,
                referenceNumber: existingExpense.referenceNumber,
                categoryId: existingExpense.category.id,
                paymentAccountId: existingExpense.paymentAccount.id,
                taxId: existingExpense.tax?.id ?? null, // Set taxId from existing expense, default to null
            });
        } else if (!isEditMode) {
             // Reset to defaults when switching to add mode or closing/reopening add mode
             form.reset({
                expenseDate: new Date(),
                amount: 0,
                descriptionEn: "",
                descriptionAr: "",
                vendor: null,
                referenceNumber: null,
                categoryId: "",
                paymentAccountId: "",
                taxId: null, // Reset taxId
            });
        }
    }, [isEditMode, existingExpense, form, isOpen]); // Depend on isOpen to reset on reopen

    const mutation = useMutation({
        mutationFn: async (data: FormValues) => {
            // Format date to YYYY-MM-DD string before sending
            const formattedData = {
                ...data,
                expenseDate: data.expenseDate ? data.expenseDate.toISOString().split('T')[0] : undefined,
            };

            if (isEditMode) {
                // Only send changed fields for update
                const changedData: UpdateExpenseInput = {};
                Object.keys(data).forEach((key) => {
                    const formKey = key as keyof FormValues;
                    // Need careful comparison, especially for date and potentially numbers
                    if (formKey === 'expenseDate') {
                        if (formattedData.expenseDate !== existingExpense?.expenseDate) {
                            changedData[formKey] = formattedData.expenseDate;
                        }
                    } else if (formKey === 'amount') {
                         // Compare amountBeforeTax (assuming 'amount' in form maps to this now)
                         if (Number(data[formKey]) !== existingExpense?.amountBeforeTax) {
                             changedData[formKey] = Number(data[formKey]);
                         }
                    } else if (formKey === 'taxId') {
                         // Handle null/undefined comparison for optional taxId
                         if (data.taxId !== (existingExpense?.tax?.id ?? null)) {
                             changedData.taxId = data.taxId || null; // Ensure null is sent if empty
                         }
                    } else if (data[formKey] !== existingExpense?.[formKey as keyof ExpenseDto]) {
                        // @ts-ignore - Trusting the structure for now, might need refinement
                        // Ensure nested objects aren't compared directly if only ID matters
                        if (formKey === 'categoryId' && data.categoryId !== existingExpense?.category?.id) {
                             changedData.categoryId = data.categoryId;
                        } else if (formKey === 'paymentAccountId' && data.paymentAccountId !== existingExpense?.paymentAccount?.id) {
                             changedData.paymentAccountId = data.paymentAccountId;
                        } else if (!['categoryId', 'paymentAccountId', 'taxId', 'expenseDate', 'amount'].includes(formKey)) {
                             // Handle other direct comparisons
                             changedData[formKey as keyof UpdateExpenseInput] = data[formKey];
                        }
                    }
                });

                 if (Object.keys(changedData).length === 0) {
                    toast.info(tValidation("noChanges"));
                    return null; // Indicate no actual mutation needed
                }

                console.log("Updating Expense Data:", changedData);
                return updateExpense(expenseId!, changedData);
            } else {
                console.log("Creating Expense Data:", formattedData);
                // Ensure all required fields are present for create
                return createExpense(formattedData as CreateExpenseInput);
            }
        },
        onSuccess: (result) => {
            if (result === null) return; // Handle case where no changes were made

            toast.success(isEditMode ? t("form.updateSuccess") : t("form.addSuccess"));
            queryClient.invalidateQueries({ queryKey: ["expenses"] });
            if (isEditMode) {
                queryClient.invalidateQueries({ queryKey: ["expense", expenseId] });
            }
            onSuccess?.();
            onOpenChange(false); // Close sheet
        },
        onError: (error) => {
            toast.error(t(isEditMode ? "form.updateError" : "form.addError", { error: getErrorMessage(error) }));
        },
    });

    const onSubmit = (data: FormValues) => {
        // Ensure taxId is set to null if empty string before submitting
        const submitData = {
            ...data,
            taxId: data.taxId === "" ? null : data.taxId,
        };
        mutation.mutate(submitData);
    };

    const isLoading = mutation.isPending || (isEditMode && isLoadingExpense) || isLoadingCategories || isLoadingTaxes;

    // Watch relevant form fields for calculation
    const watchedAmount = form.watch("amount");
    const watchedTaxId = form.watch("taxId");

    // Calculate tax and total amounts
    const selectedTax = taxes?.find(tax => tax.id === watchedTaxId);
    const taxRate = selectedTax ? selectedTax.percent / 100 : 0;
    const amountBeforeTax = Number(watchedAmount) || 0;
    const calculatedTaxAmount = amountBeforeTax * taxRate;
    const calculatedTotalAmount = amountBeforeTax + calculatedTaxAmount;


    return (
        <Sheet open={isOpen} onOpenChange={onOpenChange}>
            {children && <div onClick={() => onOpenChange(true)}>{children}</div>} {/* Optional trigger */}
            <SheetContent className="sm:max-w-lg overflow-y-auto flex flex-col"> {/* Use flex-col */}
                <SheetHeader>
                    <SheetTitle>{isEditMode ? t("form.editTitle") : t("form.addTitle")}</SheetTitle>
                    <SheetDescription>{isEditMode ? t("form.editDescription") : t("form.addDescription")}</SheetDescription>
                </SheetHeader>

                {isEditMode && isLoadingExpense && (
                     <div className="space-y-4 py-6 px-4">
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-20 w-full" />
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-8 w-full" />
                    </div>
                )}
                 {isEditMode && isErrorExpense && (
                    <div className="py-6 text-center text-destructive">
                        {t("form.errorLoadingExpense")}
                    </div>
                )}

                {/* Render form only when not loading/error in edit mode, or always in add mode */}
                {(!isEditMode || (isEditMode && !isLoadingExpense && !isErrorExpense)) && (
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-6 px-4">
                            <FormField
                                control={form.control}
                                name="expenseDate"
                                render={({ field }) => (
                                    <FormItem className="flex flex-col">
                                        <FormLabel>{t("form.expenseDateLabel")}</FormLabel>
                                        <FormControl>
                                            <DatePicker
                                                date={field.value}
                                                setDate={field.onChange}
                                                placeholder={t("form.expenseDatePlaceholder")}
                                                className="w-full"
                                                disabled={isLoading}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="amount"
                                render={({ field }) => (
                                    <FormItem>
                                        {/* Assuming 'amount' field now represents 'amountBeforeTax' */}
                                        <FormLabel>{t("form.amountBeforeTaxLabel")}</FormLabel>
                                        <FormControl>
                                            <Input type="number" step="0.01" placeholder={t("form.amountPlaceholder")} {...field} disabled={isLoading} />
                                        </FormControl>
                                        <FormDescription>{t("form.amountBeforeTaxDescription")}</FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="categoryId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("form.categoryLabel")}</FormLabel>
                                        <Select onValueChange={field.onChange} value={field.value} disabled={isLoadingCategories || isLoading}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("form.categoryPlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {categories?.map((category) => (
                                                    <SelectItem key={category.id} value={category.id}>
                                                        {category.nameEn} / {category.nameAr}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                           <FormField
                               control={form.control}
                               name="taxId"
                               render={({ field }) => (
                                   <FormItem>
                                       <FormLabel>{t("form.taxLabel")}</FormLabel>
                                       <Select
                                           onValueChange={(value) => field.onChange(value === "none" ? null : value)} // Handle 'none' option
                                           value={field.value ?? "none"} // Default to 'none' if null/undefined
                                           disabled={isLoadingTaxes || isLoading}
                                       >
                                           <FormControl>
                                               <SelectTrigger>
                                                   <SelectValue placeholder={t("form.taxPlaceholder")} />
                                               </SelectTrigger>
                                           </FormControl>
                                           <SelectContent>
                                               <SelectItem value="none">{t("form.taxNoneOption")}</SelectItem>
                                               {taxes?.map((tax) => (
                                                   <SelectItem key={tax.id} value={tax.id}>
                                                       {tax.nameEn} / {tax.nameAr} ({tax.percent}%)
                                                   </SelectItem>
                                               ))}
                                           </SelectContent>
                                       </Select>
                                       <FormMessage />
                                   </FormItem>
                               )}
                           />

                            <FormField
                                control={form.control}
                                name="paymentAccountId"
                                render={({ field }) => (
                                    <FormItem className="flex flex-col">
                                        <FormLabel>{t("form.paymentAccountLabel")}</FormLabel>
                                        <AccountCombobox
                                            value={field.value}
                                            onChange={field.onChange}
                                            filterCategory="ASSET" // Typically pay from Asset accounts (Cash, Bank)
                                            placeholder={t("form.paymentAccountPlaceholder")}
                                            searchPlaceholder={t("form.searchAccountPlaceholder")}
                                            noResultsText={t("form.noAccountFound")}
                                            disabled={isLoading}
                                        />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="descriptionEn"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("form.descriptionEnLabel")}</FormLabel>
                                        <FormControl>
                                            <Textarea placeholder={t("form.descriptionEnPlaceholder")} {...field} disabled={isLoading} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="descriptionAr"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("form.descriptionArLabel")}</FormLabel>
                                        <FormControl>
                                            <Textarea placeholder={t("form.descriptionArPlaceholder")} {...field} dir="rtl" disabled={isLoading} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="vendor"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("form.vendorLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("form.vendorPlaceholder")} {...field} value={field.value ?? ''} disabled={isLoading} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="referenceNumber"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("form.referenceLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("form.referencePlaceholder")} {...field} value={field.value ?? ''} disabled={isLoading} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            {/* Calculated Amounts Display */}
                            <div className="mt-auto pt-4 border-t"> {/* Push to bottom */}
                                <h4 className="text-sm font-medium mb-2">{t("form.calculatedAmountsTitle")}</h4>
                                <div className="space-y-1 text-sm">
                                    <div className="flex justify-between">
                                        <span>{t("form.netAmountLabel")}:</span>
                                        <span className="font-medium">{formatCurrency(amountBeforeTax)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>{t("form.taxAmountLabel")} ({selectedTax ? `${selectedTax.percent}%` : '0%'}):</span>
                                        <span className="font-medium">{formatCurrency(calculatedTaxAmount)}</span>
                                    </div>
                                    <div className="flex justify-between font-semibold text-base">
                                        <span>{t("form.totalAmountLabel")}:</span>
                                        <span>{formatCurrency(calculatedTotalAmount)}</span>
                                    </div>
                                </div>
                            </div>


                            <SheetFooter className="mt-4"> {/* Add margin top */}
                                <SheetClose asChild>
                                    <Button type="button" variant="outline">
                                        {t("form.cancelButton")}
                                    </Button>
                                </SheetClose>
                                <Button type="submit" disabled={isLoading || (isEditMode && !form.formState.isDirty)}>
                                    {isLoading ? t("form.savingButton") : t("form.saveButton")}
                                </Button>
                            </SheetFooter>
                        </form>
                    </Form>
                 )}
            </SheetContent>
        </Sheet>
    );
}
