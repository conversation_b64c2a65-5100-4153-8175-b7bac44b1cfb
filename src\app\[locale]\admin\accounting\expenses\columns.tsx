"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ExpenseDto } from "@/lib/dto/admin/accounting/expenses.dto";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions";
import { Checkbox } from "@/components/ui/checkbox";
import { useTranslations } from "next-intl";
import { formatCurrency } from "@/lib/utils"; // Assuming a currency formatting util

// Define props if needed, e.g., for passing translation function or callbacks
interface ColumnsProps {
    t: ReturnType<typeof useTranslations<'AdminExpensesPage.table'>>;
    tShared: ReturnType<typeof useTranslations<'Shared'>>;
    // Add callbacks if needed, e.g., onExpenseUpdated, onExpenseDeleted
}

export const getColumns = ({ t, tShared }: ColumnsProps): ColumnDef<ExpenseDto>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
                className="translate-y-[2px]"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
                className="translate-y-[2px]"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "expenseDate",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("expenseDate")} />
        ),
        cell: ({ row }) => {
            const date = row.getValue("expenseDate") as string;
            // Format date (adapt locale as needed)
            const formattedDate = date ? new Date(date).toLocaleDateString() : '-';
            return <div className="w-[100px]">{formattedDate}</div>;
        },
        enableSorting: true,
        enableHiding: true,
    },
    {
        accessorKey: "descriptionEn", // Or description based on locale preference
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("description")} />
        ),
        cell: ({ row }) => <div className="w-[200px] truncate">{row.original.descriptionEn}</div>, // Display based on locale
        enableSorting: false,
        enableHiding: true,
    },
    {
        accessorKey: "amountBeforeTax",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("amountBeforeTax")} />
        ),
        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("amountBeforeTax"));
            return <div className="w-[100px] text-right font-medium">{formatCurrency(amount)}</div>;
        },
        enableSorting: true,
        enableHiding: true,
    },
    {
        accessorKey: "taxAmount",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("taxAmount")} />
        ),
        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("taxAmount"));
            return <div className="w-[100px] text-right font-medium">{formatCurrency(amount)}</div>;
        },
        enableSorting: true, // Usually calculated, might not sort well
        enableHiding: true,
    },
    {
        accessorKey: "totalAmount",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("totalAmount")} />
        ),
        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("totalAmount"));
            return <div className="w-[100px] text-right font-medium">{formatCurrency(amount)}</div>;
        },
        enableSorting: true,
        enableHiding: true,
    },
    {
        accessorKey: "category",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("category")} />
        ),
        cell: ({ row }) => {
            const category = row.original.category;
            // Display name based on locale preference if available
            return <div className="w-[150px] truncate">{category?.nameEn || '-'}</div>; // Adjust based on locale
        },
        enableSorting: false, // Sorting by nested object might require specific setup
        enableHiding: true,
        // Add filter function if filtering by category name client-side (or handle server-side)
        filterFn: (row, id, value) => {
            const categoryName = row.original.category?.nameEn?.toLowerCase() || ''; // Adjust based on locale
            return categoryName.includes(String(value).toLowerCase());
        },
    },
     {
        accessorKey: "tax",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("tax")} />
        ),
        cell: ({ row }) => {
            const tax = row.original.tax;
            // Display name based on locale preference if available
            return <div className="w-[150px] truncate">{tax ? `${tax.nameEn} (${tax.percent}%)` : '-'}</div>; // Adjust based on locale
        },
        enableSorting: false,
        enableHiding: true,
        filterFn: (row, id, value) => {
            const taxName = row.original.tax?.nameEn?.toLowerCase() || ''; // Adjust based on locale
            return taxName.includes(String(value).toLowerCase());
        },
    },
    {
        accessorKey: "paymentAccount",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("paymentAccount")} />
        ),
        cell: ({ row }) => {
            const account = row.original.paymentAccount;
            // Display name based on locale preference if available
            return <div className="w-[150px] truncate">{account?.nameEn || '-'}</div>; // Adjust based on locale
        },
        enableSorting: false,
        enableHiding: true,
    },
    {
        accessorKey: "vendor",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("vendor")} />
        ),
        cell: ({ row }) => <div className="w-[150px] truncate">{row.getValue("vendor") || '-'}</div>,
        enableSorting: true,
        enableHiding: true,
    },
    {
        accessorKey: "referenceNumber",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title={t("referenceNumber")} />
        ),
        cell: ({ row }) => <div className="w-[100px] truncate">{row.getValue("referenceNumber") || '-'}</div>,
        enableSorting: true,
        enableHiding: true,
    },
    {
        id: "actions",
        cell: ({ row }) => <DataTableRowActions row={row} />, // Pass necessary props like refetch
        enableSorting: false,
        enableHiding: false,
    },
];
