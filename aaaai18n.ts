import {getRequestConfig} from 'next-intl/server';
import { notFound } from 'next/navigation';

export const locales = ['en', 'ar'];
export default getRequestConfig(async ({requestLocale}) => {
  // Typically corresponds to the `[locale]` segment
  const locale = await requestLocale;

if(!locale ||!locales.includes(locale as any)) notFound();

  return {
    messages: (await import(`./messages/${locale}.json`)).default
  };
});