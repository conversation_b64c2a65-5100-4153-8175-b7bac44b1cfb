"use client";

import { Cross2Icon } from "@radix-ui/react-icons";
import { Table } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options";
import { StudentDto } from "@/lib/dto/admin/student.dto";

interface DataTableToolbarProps {
  table: Table<StudentDto>;
  globalFilter: string;
  setGlobalFilter: (value: string) => void;
}

export function DataTableToolbar({
  table,
  globalFilter,
  setGlobalFilter,
}: DataTableToolbarProps) {
  const t = useTranslations("StudentsPage.toolbar");
  const isFiltered = table.getState().columnFilters.length > 0 || globalFilter !== '';

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Input
          placeholder={t("search")}
          value={globalFilter}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => {
              table.resetColumnFilters();
              setGlobalFilter('');
            }}
            className="h-8 px-2 lg:px-3"
          >
            {t("resetFilters")}
            <Cross2Icon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  );
}
