"use client";

import React from 'react';
import { useMutation } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    // No AlertDialogTrigger needed here, controlled externally
} from '@/components/ui/alert-dialog';
import { deleteEducationalStage } from '@/lib/api/admin/educational-stages';
import { EducationalStageDto } from '@/lib/dto/admin/educational-stage.dto';
import { handleApiError } from '@/lib/api-error-handler'; // Corrected import path

interface DeleteStageDialogProps {
    stage: EducationalStageDto;
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    onStageDeleted: () => void; // Callback to refresh data
}

export function DeleteStageDialog({ stage, isOpen, onOpenChange, onStageDeleted }: DeleteStageDialogProps) {
    const t = useTranslations('AdminEducationalStagesPage.table'); // For success/error messages
    const tShared = useTranslations('Shared.confirmationDialog'); // For dialog text

    const mutation = useMutation({
        mutationFn: () => deleteEducationalStage(stage.id),
        onSuccess: () => {
            toast.success(t('deleteSuccessToast', { name: stage.nameEn }));
            onOpenChange(false); // Close dialog
            onStageDeleted(); // Trigger data refresh
        },
        onError: (error) => {
            handleApiError(error, (message) => t('deleteErrorToast', { error: message }));
        },
    });

    const handleDelete = () => {
        mutation.mutate();
    };

    return (
        <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{tShared('title')}</AlertDialogTitle>
                    <AlertDialogDescription>
                        {tShared('deleteMessage', { item: t('educationalStageItem') })}
                        <br />
                        <span className="font-semibold">{stage.nameEn} ({stage.nameAr})</span>
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel disabled={mutation.isPending}>{tShared('cancel')}</AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleDelete}
                        disabled={mutation.isPending}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                        {mutation.isPending ? tShared('deleting') : tShared('delete')}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
