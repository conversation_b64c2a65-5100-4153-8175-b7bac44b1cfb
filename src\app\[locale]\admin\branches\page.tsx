"use client";

import { useQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { getAllBranchesList } from '@/lib/api/admin/branches';
import { DataTable } from './data-table';
import { BranchDto } from '@/lib/dto/admin/branch.dto';
import { getErrorMessage } from '@/lib/utils'; // Corrected path
import { Skeleton } from '@/components/ui/skeleton'; // For loading state

export default function BranchesPage() {
    const t = useTranslations('AdminBranchesPage');

    const { data: branches, isLoading, error, refetch } = useQuery<BranchDto[], Error>({
        queryKey: ['branches'],
        queryFn: getAllBranchesList,
    });

    if (error) {
        toast.error(t('errorLoading', { error: getErrorMessage(error) }));
        // Optionally return a more user-friendly error component
    }

    // Callbacks for child components to trigger refetch
    const handleBranchAction = () => {
        refetch();
    };

    return (
        <div className="container mx-auto py-10">
            <h1 className="text-3xl font-bold mb-4">{t('title')}</h1>
            <p className="text-muted-foreground mb-6">{t('description')}</p>

            {isLoading ? (
                <div className="space-y-4">
                    <div className="flex justify-between">
                        <Skeleton className="h-8 w-[250px]" />
                        <Skeleton className="h-8 w-[150px]" />
                    </div>
                    <Skeleton className="h-[400px] w-full rounded-md border" />
                    <div className="flex justify-end">
                         <Skeleton className="h-8 w-[200px]" />
                    </div>
                </div>
            ) : (
                <DataTable
                    data={branches ?? []}
                    isLoading={isLoading}
                    onBranchAdded={handleBranchAction}
                    onBranchUpdated={handleBranchAction}
                    onBranchDeleted={handleBranchAction}
                />
            )}
        </div>
    );
}
