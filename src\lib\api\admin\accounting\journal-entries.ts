import { API_BASE_URL } from "@/lib/constants";
import { fetchWithAuth } from "@/lib/fetch-with-auth";
import {
    CreateJournalEntryRequest,
    GetJournalEntriesParams,
    JournalEntryDto,
    PageJournalEntryDto,
    UpdateJournalEntryRequest
} from "@/lib/dto/admin/accounting/journal-entries.dto";
import { buildQueryString } from "@/lib/utils";

const JOURNAL_ENTRIES_API_PATH = `${API_BASE_URL}/accounting/journal-entries`;

// Add optional token parameter
export async function findJournalEntries(params: GetJournalEntriesParams, token?: string): Promise<PageJournalEntryDto> {
    const queryString = buildQueryString(params);
    const url = `${JOURNAL_ENTRIES_API_PATH}?${queryString}`;
    // Pass token to fetchWithAuth
    const response = await fetchWithAuth(url, {}, token);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to fetch journal entries: ${response.statusText}`);
    }
    return await response.json() as PageJournalEntryDto;
}

// Add optional token parameter
export async function getJournalEntryById(id: string, token?: string): Promise<JournalEntryDto> {
    const url = `${JOURNAL_ENTRIES_API_PATH}/${id}`;
    // Pass token to fetchWithAuth
    const response = await fetchWithAuth(url, {}, token);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to fetch journal entry ${id}: ${response.statusText}`);
    }
    return await response.json() as JournalEntryDto;
}

// Add optional token parameter
export async function createJournalEntry(request: CreateJournalEntryRequest, token?: string): Promise<JournalEntryDto> {
    const url = JOURNAL_ENTRIES_API_PATH;
    // Pass token to fetchWithAuth
    const response = await fetchWithAuth(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
    }, token);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to create journal entry: ${response.statusText}`);
    }
    return await response.json() as JournalEntryDto;
}

// Add optional token parameter
export async function updateJournalEntry(id: string, request: UpdateJournalEntryRequest, token?: string): Promise<JournalEntryDto> {
    const url = `${JOURNAL_ENTRIES_API_PATH}/${id}`;
    // Pass token to fetchWithAuth
    const response = await fetchWithAuth(url, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
    }, token);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.message || `Failed to update journal entry ${id}: ${response.statusText}`);
    }
    return await response.json() as JournalEntryDto;
}

// Add optional token parameter
export async function deleteJournalEntry(id: string, token?: string): Promise<void> {
    const url = `${JOURNAL_ENTRIES_API_PATH}/${id}`;
    // Pass token to fetchWithAuth
    const response = await fetchWithAuth(url, {
        method: 'DELETE',
    }, token);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        // Handle specific conflict error for posted entries if needed
        if (response.status === 409 && errorData?.message?.includes('posted')) {
             throw new Error('Cannot delete a posted journal entry.');
        }
        throw new Error(errorData?.message || `Failed to delete journal entry ${id}: ${response.statusText}`);
    }
    // No content expected on successful delete (204)
}

// Add optional token parameter
export async function postJournalEntry(id: string, token?: string): Promise<JournalEntryDto> {
    const url = `${JOURNAL_ENTRIES_API_PATH}/${id}/post`;
    // Pass token to fetchWithAuth
    const response = await fetchWithAuth(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }, // API might not need body, adjust if necessary
        // body: JSON.stringify({}), // Send empty body if required by API
    }, token);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        // Handle specific conflict errors if needed (e.g., already posted, unbalanced)
        throw new Error(errorData?.message || `Failed to post journal entry ${id}: ${response.statusText}`);
    }
    return await response.json() as JournalEntryDto;
}
