"use client";

import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Pen, Trash } from "lucide-react";
import { ChartOfAccountDto } from "@/lib/dto/admin/accounting/chart-of-accounts.dto";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteChartOfAccount } from "@/lib/api/admin/accounting/chart-of-accounts";
import { toast } from "sonner";
import { useState, useEffect } from "react"; // Import useEffect
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, Alert<PERSON><PERSON>ogFooter, Al<PERSON>DialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { EditAccountDialog } from "./edit-dialog"; // Import the Edit Dialog

interface DataTableRowActionsProps<TData> {
    row: Row<TData>;
}

export function DataTableRowActions<TData>({
    row,
}: DataTableRowActionsProps<TData>) {
    const account = row.original as ChartOfAccountDto;
    const t = useTranslations("AdminChartOfAccountsPage.table");
    const tConfirm = useTranslations("Shared.confirmationDialog");
    const queryClient = useQueryClient();
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false); // State for edit dialog

    const deleteMutation = useMutation({ // Rename mutation for clarity
        mutationFn: deleteChartOfAccount,
        onSuccess: () => {
            toast.success(t("deleteSuccessToast", { accountNumber: account.accountNumber }));
            queryClient.invalidateQueries({ queryKey: ["chart-of-accounts"] }); // Invalidate cache
        },
        onError: (error) => {
            toast.error(t("deleteErrorToast", { error: error.message }));
        },
        onSettled: () => {
            setIsDeleteDialogOpen(false); // Close dialog after mutation settles
        }
    });

    const handleDelete = () => {
        deleteMutation.mutate(account.id);
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                    >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{t("actionsOpenMenu")}</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuItem
                        onClick={() => {
                            // Open the Edit Dialog
                            setIsEditDialogOpen(true);
                        }}
                    >
                        <Pen className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("actionsEdit")}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        variant="destructive"
                        onClick={() => setIsDeleteDialogOpen(true)} // Open confirmation dialog
                        disabled={deleteMutation.isPending} // Disable if delete is pending
                    >
                        <Trash className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                        {t("actionsDelete")}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>

            {/* Edit Dialog */}
            {isEditDialogOpen && ( // Conditionally render the dialog
                <EditAccountDialog
                    accountId={account.id}
                    isOpen={isEditDialogOpen}
                    onOpenChange={setIsEditDialogOpen}
                />
            )}

            {/* Confirmation Dialog for Delete */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>{tConfirm("title")}</AlertDialogTitle>
                        <AlertDialogDescription>
                            {tConfirm("deleteMessage", { item: `${t("accountItem")} ${account.accountNumber} (${account.nameEn})` })}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={deleteMutation.isPending}>{tConfirm("cancel")}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            disabled={deleteMutation.isPending}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {deleteMutation.isPending ? tConfirm("deleting") : t("actionsDelete")} {/* Use correct translation key */}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
