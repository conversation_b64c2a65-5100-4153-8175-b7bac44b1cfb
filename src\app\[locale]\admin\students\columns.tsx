"use client";

import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { StudentDto } from "@/lib/dto/admin/student.dto";
import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";
import Link from "next/link";

export const columns: ColumnDef<StudentDto>[] = [
  {
    accessorFn: (row) => row.userAccount.firstName,
    id: "firstName",
    header: ({ column }) => {
      const t = useTranslations("StudentsPage.columns");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("firstName")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className="font-medium">{row.original.userAccount.firstName}</div>
    ),
  },
  {
    accessorFn: (row) => row.userAccount.lastName,
    id: "lastName",
    header: ({ column }) => {
      const t = useTranslations("StudentsPage.columns");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("lastName")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => (
      <div>{row.original.userAccount.lastName}</div>
    ),
  },
  {
    accessorFn: (row) => row.userAccount.email,
    id: "email",
    header: ({ column }) => {
      const t = useTranslations("StudentsPage.columns");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("email")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => <div>{row.original.userAccount.email}</div>,
  },
  {
    accessorKey: "admissionNumber",
    header: ({ column }) => {
      const t = useTranslations("StudentsPage.columns");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("admissionNumber")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "nationalId",
    header: ({ column }) => {
      const t = useTranslations("StudentsPage.columns");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("nationalId")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "idType",
    header: ({ column }) => {
      const t = useTranslations("StudentsPage.columns");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("idType")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const t = useTranslations("StudentsPage.idTypes");
      const idType = row.original.idType;

      // Handle null or undefined idType
      if (!idType) {
        return <Badge variant="outline">{t("unknown")}</Badge>;
      }

      return (
        <Badge variant="outline">
          {t(idType.toLowerCase())}
        </Badge>
      );
    },
  },
  {
    accessorFn: (row) => row.guardians?.length || 0,
    id: "guardiansCount",
    header: ({ column }) => {
      const t = useTranslations("StudentsPage.columns");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("guardians")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const guardians = row.original.guardians || [];
      return <div>{guardians.length}</div>;
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const t = useTranslations("StudentsPage.actions");
      const student = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">{t("openMenu")}</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t("actions")}</DropdownMenuLabel>
            <DropdownMenuItem asChild>
              <Link href={`/admin/students/${student.id}`}>
                {t("viewDetails")}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/admin/students/${student.id}/edit`}>
                {t("edit")}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/admin/students/${student.id}/guardians`}>
                {t("manageGuardians")}
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
