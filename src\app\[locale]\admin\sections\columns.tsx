'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { SectionDto } from '@/lib/dto/admin/school-management/sections.dto';
import { UseMutateAsyncFunction } from '@tanstack/react-query';
import { ConfirmOptions } from '@/hooks/use-delete-confirmation';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal, Pencil, Trash2 } from 'lucide-react';
import { useTranslations } from 'next-intl';

interface GetColumnsProps {
  onEdit: (section: SectionDto) => void;
  t: ReturnType<typeof useTranslations<'Admin.Sections'>>;
  tShared: ReturnType<typeof useTranslations<'Shared'>>;
  deleteSectionMutation: UseMutateAsyncFunction<void, Error, string, unknown>;
  showDeleteConfirmation: (options: ConfirmOptions) => Promise<boolean>;
}

export const getColumns = ({ onEdit, t, tShared, deleteSectionMutation, showDeleteConfirmation }: GetColumnsProps): ColumnDef<SectionDto>[] => {
  // const { toast } = useToast();

  const handleDelete = async (sectionId: string, sectionName: string) => {
    const confirmed = await showDeleteConfirmation({
      title: tShared('Messages.Confirmations.deleteTitle', { entityName: sectionName }),
      message: tShared('Messages.Confirmations.deleteMessage', { entityName: sectionName, entityType: t('entityName') }),
    });

    if (confirmed) {
      try {
        await deleteSectionMutation(sectionId); // Use mutateAsync directly
        toast.success(tShared('Messages.Success.deletedTitle'), {
          description: tShared('Messages.Success.deletedMessage', { entityName: sectionName, entityType: t('entityName') }),
        });
      } catch (error: unknown) {
        let errorMessage = tShared('Messages.Error.generic');
        if (error instanceof Error) {
          errorMessage = error.message;
        }
        toast.error(tShared('Messages.Error.deleteTitle'), {
          description: errorMessage,
        });
      }
    }
  };

  return [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('name')}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => <div>{row.getValue('name')}</div>,
    },
    {
      accessorKey: 'capacity',
      header: t('capacity'),
      cell: ({ row }) => <div>{row.getValue('capacity') ?? '-'}</div>,
    },
    {
      accessorKey: 'gradeLevelNameEn',
      header: t('gradeLevel'),
      cell: ({ row }) => {
        // Assuming you might want to display both English and Arabic names if available
        // Or use a locale-specific name based on current language
        const nameEn = row.original.gradeLevelNameEn;
        const nameAr = row.original.gradeLevelNameAr;
        return <div>{nameEn}{nameAr ? ` (${nameAr})` : ''}</div>;
      },
    },
    {
      accessorKey: 'branchNameEn',
      header: t('branch'),
      cell: ({ row }) => {
        const nameEn = row.original.branchNameEn;
        const nameAr = row.original.branchNameAr;
        return <div>{nameEn}{nameAr ? ` (${nameAr})` : ''}</div>;
      },
    },
    {
      accessorKey: 'academicYearName',
      header: t('academicYear'),
      cell: ({ row }) => <div>{row.original.academicYearName}</div>,
    },
    {
      accessorKey: 'createdAt',
      header: tShared('Table.Headers.createdAt'),
      cell: ({ row }) => new Date(row.getValue('createdAt')).toLocaleDateString(),
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const section = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">{tShared('Actions.openMenu')}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{tShared('Actions.title')}</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => onEdit(section)}>
                <Pencil className="mr-2 h-4 w-4" />
                {tShared('Actions.edit')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleDelete(section.id, section.name)} className="text-red-600 hover:text-red-700">
                <Trash2 className="mr-2 h-4 w-4" />
                {tShared('Actions.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
};