"use client";

import { ColumnDef } from "@tanstack/react-table";
import { useTranslations } from 'next-intl';
import { EducationalStageDto } from "@/lib/dto/admin/educational-stage.dto";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header"; // Trying path with /data-table/ subfolder again
import { DataTableRowActions } from "./data-table-row-actions"; // Row actions specific to stages

// Define props for the columns function to accept callbacks
interface ColumnsProps {
    t: ReturnType<typeof useTranslations<'AdminEducationalStagesPage.table'>>; // Add t function prop
    onStageUpdated: () => void;
    onStageDeleted: () => void;
}

export const columns = ({ t, onStageUpdated, onStageDeleted }: ColumnsProps): ColumnDef<EducationalStageDto>[] => {
    // const t = useTranslations('AdminEducationalStagesPage.table'); // Remove useTranslations call from here

    return [
        {
            accessorKey: "nameEn",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t('nameEn')} />
            ),
            cell: ({ row }) => <div>{row.getValue("nameEn")}</div>,
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "nameAr",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t('nameAr')} />
            ),
            cell: ({ row }) => <div>{row.getValue("nameAr")}</div>,
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "sortOrder",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t('sortOrder')} />
            ),
            cell: ({ row }) => <div className="text-center">{row.getValue("sortOrder")}</div>, // Center align sort order
            enableSorting: true,
            enableHiding: true,
        },
        {
            id: "actions",
            cell: ({ row }) => (
                <DataTableRowActions
                    row={row}
                    onStageUpdated={onStageUpdated}
                    onStageDeleted={onStageDeleted}
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
    ];
};
