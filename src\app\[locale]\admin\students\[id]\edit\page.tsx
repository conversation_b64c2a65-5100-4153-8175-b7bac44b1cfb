"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useTranslations } from "next-intl";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useState, useEffect } from "react";

import { getStudentById, updateStudentProfile } from "@/lib/api/admin/students";
import { getAllEducationalStagesList } from "@/lib/api/admin/educational-stages";
import { getGradeLevelsByStage } from "@/lib/api/admin/grade-levels";
import { IdType } from "@/lib/dto/admin/student.dto";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal, ArrowLeft, Save } from "lucide-react";

export default function EditStudentPage() {
  const t = useTranslations("EditStudentPage");
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const studentId = params.id as string;
  const [selectedStageId, setSelectedStageId] = useState<string | null>(null);

  // Create form schema
  const formSchema = z.object({
    admissionNumber: z.string().min(1, t("errors.admissionNumberRequired")),
    nationalId: z.string().min(1, t("errors.nationalIdRequired")),
    idType: z.enum(["NATIONAL_ID", "PASSPORT"] as const),
    gradeLevelId: z.string().uuid(t("errors.invalidGradeLevel")).optional(),
    branchId: z.string().uuid(t("errors.invalidBranch")).optional(),
  });

  type FormValues = z.infer<typeof formSchema>;

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      admissionNumber: "",
      nationalId: "",
      idType: "NATIONAL_ID" as IdType,
      gradeLevelId: undefined,
      branchId: undefined,
    },
  });

  // Fetch student data
  const { data: student, isLoading: isLoadingStudent, isError: isErrorStudent, error: studentError } = useQuery({
    queryKey: ["student", studentId],
    queryFn: () => getStudentById(studentId),
  });

  // Fetch educational stages
  const { data: stagesData } = useQuery({
    queryKey: ["educational-stages"],
    queryFn: getAllEducationalStagesList,
  });

  // Fetch grade levels based on selected stage
  const { data: gradeLevelsData } = useQuery({
    queryKey: ["grade-levels", selectedStageId],
    queryFn: () => getGradeLevelsByStage({ stageId: selectedStageId! }),
    enabled: !!selectedStageId,
  });

  // Update form values when student data is loaded
  useEffect(() => {
    if (student) {
      form.reset({
        admissionNumber: student.admissionNumber,
        nationalId: student.nationalId,
        idType: student.idType,
        gradeLevelId: student.gradeLevel?.id,
        branchId: student.branch?.id,
      });

      // If the student has a grade level, find its stage and set it
      if (student.gradeLevel && stagesData) {
        const stage = stagesData.find(stage => 
          stage.gradeLevels?.some(grade => grade.id === student.gradeLevel?.id)
        );
        if (stage) {
          setSelectedStageId(stage.id);
        }
      }
    }
  }, [student, stagesData, form]);

  // Update student mutation
  const mutation = useMutation({
    mutationFn: (values: FormValues) => updateStudentProfile(studentId, values),
    onSuccess: () => {
      toast.success(t("successToast"));
      queryClient.invalidateQueries({ queryKey: ["student", studentId] });
      queryClient.invalidateQueries({ queryKey: ["students"] });
      router.push(`/admin/students/${studentId}`);
    },
    onError: (error) => {
      toast.error(t("errorToast", { error: error instanceof Error ? error.message : String(error) }));
    },
  });

  // Form submission handler
  const onSubmit = (values: FormValues) => {
    mutation.mutate(values);
  };

  if (isLoadingStudent) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/4 mb-2" />
            <Skeleton className="h-4 w-1/3" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-24" />
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (isErrorStudent) {
    return (
      <Alert variant="destructive">
        <Terminal className="h-4 w-4" />
        <AlertTitle>{t("errorTitle")}</AlertTitle>
        <AlertDescription>
          {studentError instanceof Error ? studentError.message : t("unknownError")}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">
            {t("title", { name: `${student.userAccount.firstName} ${student.userAccount.lastName}` })}
          </h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("formTitle")}</CardTitle>
          <CardDescription>{t("formDescription")}</CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="admissionNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("admissionNumber")}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nationalId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("nationalId")}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="idType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("idType")}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("selectIdType")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="NATIONAL_ID">{t("idTypes.national_id")}</SelectItem>
                          <SelectItem value="PASSPORT">{t("idTypes.passport")}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <FormLabel>{t("educationalStage")}</FormLabel>
                  <Select
                    onValueChange={(value) => setSelectedStageId(value)}
                    value={selectedStageId || undefined}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectStage")} />
                    </SelectTrigger>
                    <SelectContent>
                      {stagesData?.map((stage) => (
                        <SelectItem key={stage.id} value={stage.id}>
                          {stage.nameEn}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <FormField
                  control={form.control}
                  name="gradeLevelId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("gradeLevel")}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={!selectedStageId}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("selectGradeLevel")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {gradeLevelsData?.map((grade) => (
                            <SelectItem key={grade.id} value={grade.id}>
                              {grade.nameEn}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(`/admin/students/${studentId}`)}
              >
                {t("cancel")}
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                <Save className="mr-2 h-4 w-4" />
                {mutation.isPending ? t("saving") : t("save")}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  );
}
