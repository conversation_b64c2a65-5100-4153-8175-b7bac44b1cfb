"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { updateFeeCategory } from "@/lib/api/admin/accounting/fee-categories";
import { UpdateFeeCategoryInput, updateFeeCategorySchema } from "@/lib/schemas/admin/accounting/fee-categories";
import { FeeCategoryDto } from "@/lib/dto/admin/accounting/fee-categories.dto";

interface EditFeeCategoryDialogProps {
    category: FeeCategoryDto;
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
}

export function EditFeeCategoryDialog({ category, isOpen, onOpenChange }: EditFeeCategoryDialogProps) {
    const t = useTranslations("AdminFeeCategoriesPage.EditDialog");
    const tValidation = useTranslations("AdminFeeCategoriesPage.EditDialog.validation");
    const queryClient = useQueryClient();

    const formSchema = updateFeeCategorySchema(tValidation);
    const form = useForm<UpdateFeeCategoryInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            nameEn: category.nameEn || "",
            nameAr: category.nameAr || "",
            descriptionEn: category.descriptionEn || "",
            descriptionAr: category.descriptionAr || "",
        },
    });

     // Reset form when category changes (e.g., opening dialog for a different category)
    useEffect(() => {
        if (isOpen) {
            form.reset({
                nameEn: category.nameEn || "",
                nameAr: category.nameAr || "",
                descriptionEn: category.descriptionEn || "",
                descriptionAr: category.descriptionAr || "",
            });
        }
    }, [category, isOpen, form]);


    const mutation = useMutation({
        mutationFn: (data: UpdateFeeCategoryInput) => updateFeeCategory(category.id, data),
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.nameEn }));
            queryClient.invalidateQueries({ queryKey: ["fee-categories"] });
            onOpenChange(false); // Close dialog
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message }));
        },
    });

    const onSubmit = (values: UpdateFeeCategoryInput) => {
        // Filter out unchanged values if necessary, or send all
        const changedValues: UpdateFeeCategoryInput = {};
        if (values.nameEn !== category.nameEn) changedValues.nameEn = values.nameEn;
        if (values.nameAr !== category.nameAr) changedValues.nameAr = values.nameAr;
        if (values.descriptionEn !== category.descriptionEn) changedValues.descriptionEn = values.descriptionEn || null; // Send null to clear
        if (values.descriptionAr !== category.descriptionAr) changedValues.descriptionAr = values.descriptionAr || null; // Send null to clear

        if (Object.keys(changedValues).length > 0) {
             console.log("Updating Fee Category:", category.id, changedValues);
            mutation.mutate(changedValues);
        } else {
            toast.info("No changes detected.");
            onOpenChange(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description", { name: category.nameEn })}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="space-y-4 p-1">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <FormField
                                    control={form.control}
                                    name="nameEn"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("nameEnLabel")}</FormLabel>
                                            <FormControl>
                                                <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="nameAr"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("nameArLabel")}</FormLabel>
                                            <FormControl>
                                                <Input dir="rtl" placeholder={t("nameArPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <FormField
                                control={form.control}
                                name="descriptionEn"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                        <FormControl>
                                            <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} value={field.value ?? ''} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="descriptionAr"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                        <FormControl>
                                            <Textarea dir="rtl" placeholder={t("descriptionArPlaceholder")} {...field} value={field.value ?? ''} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={mutation.isPending}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
