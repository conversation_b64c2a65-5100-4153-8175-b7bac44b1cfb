"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "@/i18n/navigation"; // Use i18n navigation for proper locale handling
import { useLocale } from "next-intl"; // Import useLocale to get current locale
import { toast } from "sonner";
import { redirectToDashboard } from "@/lib/auth-helpers"; // Import auth helpers

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useTranslations } from "next-intl"; // Import useTranslations

import { Input } from "@/components/ui/input";
import { getLoginSchema, type LoginSchema } from "@/lib/schemas/auth"; // Import getLoginSchema
import { loginUser } from "@/lib/api/auth";
import { useAuthStore } from "@/stores/auth";

export function LoginForm() {
  const router = useRouter();
  const locale = useLocale(); // Get current locale
  const setAuth = useAuthStore((state) => state.setAuth);
  const t = useTranslations("LoginPage"); // Initialize translation hook

  const form = useForm<LoginSchema>({
    resolver: zodResolver(getLoginSchema(t)), // Use the function to get the schema
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const loginMutation = useMutation({
    mutationFn: loginUser,
    onSuccess: (data) => {
      console.log("[LoginForm] Login successful, setting auth data...");

      // Store auth data in Zustand
      setAuth(data);

      // Show success toast
      toast.success(t("loginSuccessToast"));

      // Add a longer delay to ensure the auth state is properly saved before redirecting
      setTimeout(() => {
        console.log("[LoginForm] Redirecting to dashboard...");

        // Use our helper to redirect to the dashboard
        redirectToDashboard(locale);
      }, 1000); // Increased timeout to ensure cookie is set
    },
    onError: (error) => {
      // Display error message from API or a generic one
      // TODO: Consider translating API error messages if possible, or use a generic translated fallback
      toast.error(error.message || t("loginErrorToast")); // Use translated error toast fallback
      // Optionally clear password field on error
      form.resetField("password");
    },
  });

  function onSubmit(values: LoginSchema) {
    loginMutation.mutate(values);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("emailLabel")}</FormLabel>
              <FormControl>
                <Input placeholder={t("emailPlaceholder")} {...field} />
              </FormControl>
              <FormMessage /> {/* Zod errors are now translated */}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("passwordLabel")}</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder={t("passwordPlaceholder")}
                  {...field}
                />
              </FormControl>
              <FormMessage /> {/* Zod errors are now translated */}
            </FormItem>
          )}
        />
        <Button
          type="submit"
          className="w-full"
          disabled={loginMutation.isPending}
        >
          {loginMutation.isPending ? t("loggingInButton") : t("loginButton")}
        </Button>
        {/* TODO: Add link to forgot password page */}
        {/* TODO: Add link to registration page if applicable */}
      </form>
    </Form>
  );
}
