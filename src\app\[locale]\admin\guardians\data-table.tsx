"use client";

import * as React from "react";

import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar";
import { Skeleton } from "@/components/ui/skeleton";
import { getColumns } from "./columns";
import { getGuardians } from "@/lib/api/admin/guardians";
import { useTranslations } from "next-intl";

export function GuardiansDataTable() {
    const t = useTranslations("AdminGuardiansPage.table");
    const tShared = useTranslations("Shared.dataTable");

    // Table state
    const [rowSelection, setRowSelection] = React.useState({});
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [pagination, setPagination] = React.useState({
        pageIndex: 0,
        pageSize: 10,
    });

    // Fetch data
    const { data, isLoading, error } = useQuery({
        queryKey: ["guardians", pagination.pageIndex, pagination.pageSize],
        queryFn: () => getGuardians({
            page: pagination.pageIndex,
            size: pagination.pageSize,
        }),
        placeholderData: (previousData) => previousData,
        retry: 1,
        retryDelay: 1000
    });

    // Show toast on error
    React.useEffect(() => {
        if (error) {
            console.error("Error fetching guardians:", error);
            toast.error(`Failed to load guardians: ${error instanceof Error ? error.message : "Unknown error"}`);
        }
    }, [error]);

    // Memoize columns to prevent re-creation on every render
    const columns = React.useMemo(
        () => getColumns({ t, tShared }),
        [t, tShared]
    );

    const table = useReactTable({
        data: data || [] as any[],
        columns,
        state: {
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
            pagination,
        },
        enableRowSelection: true,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        manualPagination: true,
    });

    return (
        <div className="space-y-4">
            <DataTableToolbar table={table} />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            // Loading skeleton
                            Array.from({ length: pagination.pageSize }).map((_, index) => (
                                <TableRow key={`loading-${index}`}>
                                    {Array.from({ length: columns.length }).map((_, cellIndex) => (
                                        <TableCell key={`loading-cell-${cellIndex}`}>
                                            <Skeleton className="h-6 w-full" />
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && "selected"}
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className="h-24 text-center"
                                >
                                    {error ? tShared("errorLoadingData", { error: (error as Error).message }) : t("noResults")}
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            <DataTablePagination table={table} />

            {/* Add Guardian Dialog - only render on client side */}
            {/* {isClient && <AddGuardianDialog onSuccess={handleSuccess} />} */}
        </div>
    );
}
