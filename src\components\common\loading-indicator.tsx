"use client";

import React, { useEffect, useState } from "react";

import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface LoadingIndicatorProps {
  isLoading: boolean;
  className?: string;
  /**
   * Type of loading indicator to show
   * - "bar": Shows a progress bar at the top of the page
   * - "spinner": Shows a spinner in the center of the page
   * - "both": Shows both the bar and spinner
   */
  type?: "bar" | "spinner" | "both";
}

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  isLoading,
  className,
  type = "bar", // Default to bar
}) => {
  // Add a small delay before showing the indicator to avoid flashing
  // for very quick navigations
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (isLoading) {
      // Show the indicator after a small delay
      timeout = setTimeout(() => {
        setShouldShow(true);
      }, 100);
    } else {
      setShouldShow(false);
    }

    return () => {
      clearTimeout(timeout);
    };
  }, [isLoading]);

  if (!shouldShow) return null;

  const showBar = type === "bar" || type === "both";
  const showSpinner = type === "spinner" || type === "both";

  return (
    <>
      {/* Progress bar at the top */}
      {showBar && (
        <div
          className={cn(
            "fixed top-0 left-0 right-0 z-50 h-1.5 bg-primary/10 overflow-hidden",
            className
          )}
        >
          <div
            className="h-full bg-primary transition-all duration-300 ease-in-out"
            style={{
              width: "30%",
              animation: "loading 1.2s ease-in-out infinite",
            }}
          />
        </div>
      )}

      {/* Centered spinner */}
      {showSpinner && (
        <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
          <div className="bg-background/80 rounded-full p-3 shadow-md">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        </div>
      )}

      {/* Global styles for animations */}
      <style jsx global>{`
        @keyframes loading {
          0% {
            transform: translateX(-100%);
          }
          50% {
            transform: translateX(100%);
          }
          100% {
            transform: translateX(300%);
          }
        }
      `}</style>
    </>
  );
};
