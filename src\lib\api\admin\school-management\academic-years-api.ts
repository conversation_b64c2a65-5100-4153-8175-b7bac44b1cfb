import { API_ENDPOINTS, DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE } from '@/lib/constants';
import { AcademicYearDto, PageAcademicYearDto, GetAcademicYearsParams } from '@/lib/dto/admin/academic-year.dto';
import { Page } from '@/lib/dto/common.dto';

import { fetchWithAuth } from '@/lib/fetch-with-auth';
import { useQuery } from '@tanstack/react-query';

// API Hooks

/**
 * Fetches a paginated list of academic years.
 */
export const useGetAcademicYears = (
  params: GetAcademicYearsParams = {}
) => {
  const {
    page = DEFAULT_PAGE_INDEX,
    size = DEFAULT_PAGE_SIZE,
    search,
    sort,
    activeOnly = false
  } = params;

  return useQuery<PageAcademicYearDto | AcademicYearDto[], Error>({
    queryKey: [API_ENDPOINTS.ADMIN_ACADEMIC_YEARS, { page, size, search, sort, activeOnly }],
    queryFn: async () => {
      const queryParams = new URLSearchParams();

      if (page !== undefined) queryParams.append('page', page.toString());
      if (size !== undefined) queryParams.append('size', size.toString());
      if (search) queryParams.append('search', search);
      if (sort && sort.length > 0) {
        sort.forEach(s => queryParams.append('sort', s));
      }

      // Use different endpoint for active-only academic years if needed
      const endpoint = activeOnly
        ? `${API_ENDPOINTS.ADMIN_ACADEMIC_YEARS}/active`
        : `${API_ENDPOINTS.ADMIN_ACADEMIC_YEARS}`;

      const url = queryParams.toString()
        ? `${endpoint}?${queryParams.toString()}`
        : endpoint;

      const response = await fetchWithAuth(url);

      if (!response.ok) {
        throw new Error('Failed to fetch academic years');
      }

      return response.json();
    },
  });
};

/**
 * Fetches a single academic year by its ID.
 */
export const useGetAcademicYearById = (id: string | undefined, options?: { enabled?: boolean }) => {
  return useQuery<AcademicYearDto, Error>({
    queryKey: [API_ENDPOINTS.ADMIN_ACADEMIC_YEARS, id],
    queryFn: async () => {
      if (!id) throw new Error('Academic Year ID is required');
      const response = await fetchWithAuth(`${API_ENDPOINTS.ADMIN_ACADEMIC_YEARS}/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch academic year');
      }

      return response.json();
    },
    enabled: options?.enabled !== undefined ? options.enabled && !!id : !!id,
  });
};
