"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import { PlusCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea'; // Use Textarea for address
import { createBranch } from '@/lib/api/admin/branches';
import { CreateBranchInput, createBranchSchema } from '@/lib/schemas/admin/branch';
import { getErrorMessage } from '@/lib/utils'; // Corrected path

interface AddBranchDialogProps {
    onSuccess?: () => void; // Optional callback
}

export function AddBranchDialog({ onSuccess }: AddBranchDialogProps) {
    const t = useTranslations('AdminBranchesPage.AddDialog');
    const queryClient = useQueryClient();
    const [isOpen, setIsOpen] = React.useState(false);

    const form = useForm<CreateBranchInput>({
        resolver: zodResolver(createBranchSchema(t)),
        defaultValues: {
            nameEn: '',
            nameAr: '',
            address: '',
        },
    });

    const mutation = useMutation({
        mutationFn: createBranch,
        onSuccess: (data) => {
            toast.success(t('successToast', { name: data.nameEn }));
            queryClient.invalidateQueries({ queryKey: ['branches'] });
            setIsOpen(false); // Close dialog
            form.reset(); // Reset form
            onSuccess?.();
        },
        onError: (error) => {
            toast.error(t('errorToast', { error: getErrorMessage(error) }));
        },
    });

    const onSubmit = (data: CreateBranchInput) => {
        mutation.mutate(data);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button size="sm" className="ml-auto h-8">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t('triggerButton')}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>{t('title')}</DialogTitle>
                    <DialogDescription>{t('description')}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4 py-4">
                        <FormField
                            control={form.control}
                            name="nameEn"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('nameEnLabel')}</FormLabel>
                                    <FormControl>
                                        <Input placeholder={t('nameEnPlaceholder')} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="nameAr"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('nameArLabel')}</FormLabel>
                                    <FormControl>
                                        <Input placeholder={t('nameArPlaceholder')} {...field} dir="rtl" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="address"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('addressLabel')}</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder={t('addressPlaceholder')}
                                            className="resize-none" // Optional: prevent resizing
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={mutation.isPending}>
                                {t('cancelButton')}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t('savingButton') : t('saveButton')}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
