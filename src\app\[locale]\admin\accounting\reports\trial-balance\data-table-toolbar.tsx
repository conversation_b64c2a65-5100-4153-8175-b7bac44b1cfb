"use client";

"use client";

import { Button } from "@/components/ui/button";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { format } from "date-fns";
import { useState } from "react";
import { useTranslations } from "next-intl";

interface TrialBalanceDataTableToolbarProps {
  onDateRangeChange: (range: DateRange | undefined) => void;
  onGenerateReport: (startDate: string, endDate: string) => void;
  isLoading: boolean;
}

export function TrialBalanceDataTableToolbar({
  onDateRangeChange,
  onGenerateReport,
  isLoading,
}: TrialBalanceDataTableToolbarProps) {
  const t = useTranslations("TrialBalance");
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);

  const handleDateChange = (range: DateRange | undefined) => {
    setDateRange(range);
    onDateRangeChange(range);
  };

  const handleGenerateClick = () => {
    if (dateRange?.from && dateRange?.to) {
      onGenerateReport(format(dateRange.from, "yyyy-MM-dd"), format(dateRange.to, "yyyy-MM-dd"));
    }
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <DateRangePicker date={dateRange} onDateChange={handleDateChange} />
        <Button
          onClick={handleGenerateClick}
          disabled={!dateRange?.from || !dateRange?.to || isLoading}
        >
          {t("generateReport")}
        </Button>
      </div>
    </div>
  );
}
