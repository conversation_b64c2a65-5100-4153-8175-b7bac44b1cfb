Maali School Next.js Project Guidelines 
For AI-Assisted Development  
📁 Project Overview  

Name : Maali School Management System
Purpose : Comprehensive school administration platform with:   

    Student/staff management  
    Accounting & payroll (SAR currency)  
    Bus route tracking  
    Learning management
    Architecture : Next.js 14 App Router with domain-driven structure
     

🛠️ Technical Stack  (Strict Compliance Required)  
UI
	
Shadcn UI + Tailwind CSS (Brand colors: #B14E00, #D6AA48, #1C5954, #C2C1B1)
State
	
Zustand (auth persistence), TanStack Query (server state)
Forms
	
React Hook Form + Zod validation
i18n
	
next-intl (maintain ar.json/en.json)
Data Grids
	
TanStack Table
APIs
	
Spring Boot integration via fetch()
 
 
📝 Implementation Rules  

    Component Structure    
        Mirror existing domain folder organization  
        Server Components: Static content + SEO pages  
        Client Components: Interactive elements requiring hooks/APIs
         

    Development Workflow    
        ✅ Always reference api-docs.json for endpoint specs  
        ✅ Duplicate Chart of Accounts page structure for new financial features  
        ✅ Auto-generate translation keys in both language files
         

    Code Quality Enforcement    
        TypeScript strict mode compliance  
        Security: Token validation in API calls, role-based access checks  
        Performance: TanStack Query prefetching + pagination  
        Responsive design: Mobile-first with Tailwind breakpoints
         

    Forbidden Practices    
        ❌ Custom CSS (use Tailwind/Shadcn variants)  
        ❌ State management outside Zustand/TanStack Query  
        ❌ Unvalidated API responses
         
     

🚨 Critical Requirements  

    Authentication : All protected routes must validate Zustand auth state  
    Error Handling : Global error boundaries + form validation messages  
    Consistency : Match existing component patterns (refactor cautiously)
     

🤖 AI Assistance Parameters  

    Output Expectations    
        Production-ready code implementing all specifications  
        Automatic translation key generation with placeholder values  
        File structure suggestions matching domain architecture
         

    Context Utilization    
        Analyze api-docs.json for endpoint requirements  
        Reference context.txt for business logic patterns  
        Maintain currency formatting (SAR) in financial components
         

    Forbidden Responses    
        Generic explanations of Next.js basics  
        Suggestions outside the specified tech stack  
        Unrequested code explanations
         
     