import { z } from "zod";
import { useTranslations } from "next-intl";

// Base schema for common fields
const baseExpenseSchema = (t: ReturnType<typeof useTranslations<"AdminExpensesPage.validation">>) => z.object({
    expenseDate: z.date({
        required_error: t("expenseDateRequired"),
        invalid_type_error: t("expenseDateInvalid"),
    }),
    amount: z.coerce.number({ // Use coerce for input type="number"
        required_error: t("amountRequired"),
        invalid_type_error: t("amountInvalid"),
    }).positive({ message: t("amountPositive") }),
    descriptionEn: z.string()
        .min(1, { message: t("descriptionEnRequired") })
        .max(500, { message: t("descriptionEnTooLong") }),
    descriptionAr: z.string()
        .min(1, { message: t("descriptionArRequired") })
        .max(500, { message: t("descriptionArTooLong") }),
    vendor: z.string().max(255, { message: t("vendorTooLong") }).optional().nullable(),
    referenceNumber: z.string().max(100, { message: t("referenceTooLong") }).optional().nullable(),
    categoryId: z.string().uuid({ message: t("categoryRequired") }),
    paymentAccountId: z.string().uuid({ message: t("paymentAccountRequired") }),
    taxId: z.string().uuid({ message: t("taxInvalid") }).optional().nullable(), // Optional tax ID
});

// Schema for creating an expense
export const createExpenseSchema = (t: ReturnType<typeof useTranslations<"AdminExpensesPage.validation">>) => baseExpenseSchema(t);

// Schema for updating an expense (all fields optional except ID which is handled separately)
export const updateExpenseSchema = (t: ReturnType<typeof useTranslations<"AdminExpensesPage.validation">>) => baseExpenseSchema(t).partial().refine(data => Object.keys(data).length > 0, {
    message: t("noChanges"), // Add a general message if needed, or handle in component
});


export type CreateExpenseInput = z.infer<ReturnType<typeof createExpenseSchema>>;
export type UpdateExpenseInput = z.infer<ReturnType<typeof updateExpenseSchema>>;
