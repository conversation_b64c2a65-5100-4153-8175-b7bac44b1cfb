import { z } from "zod";
import { UseTranslations } from "next-intl";

// Enum definition matching the API and DTO
export const PaymentMethodTypeEnum = z.enum([
    "CASH",
    "BANK_TRANSFER",
    "CHEQUE",
    "CREDIT_CARD",
    "DEBIT_CARD",
    "ONLINE_PAYMENT_GATEWAY",
    "MOBILE_MONEY",
]);

// Schema for creating a payment method
export const createPaymentMethodSchema = (t: UseTranslations<"AdminPaymentMethodsPage.AddDialog.validation">) => z.object({
    nameEn: z.string().min(1, t("nameEnRequired")).max(100, t("nameEnTooLong")),
    nameAr: z.string().min(1, t("nameArRequired")).max(100, t("nameArTooLong")),
    type: PaymentMethodTypeEnum.refine(val => val !== undefined, { message: t("typeRequired") }),
    descriptionEn: z.string().max(500, t("descriptionEnTooLong")).optional(),
    descriptionAr: z.string().max(500, t("descriptionArTooLong")).optional(),
});

export type CreatePaymentMethodInput = z.infer<ReturnType<typeof createPaymentMethodSchema>>;

// Schema for updating a payment method
export const updatePaymentMethodSchema = (t: UseTranslations<"AdminPaymentMethodsPage.EditDialog.validation">) => z.object({
    nameEn: z.string().min(1, t("nameEnRequired")).max(100, t("nameEnTooLong")),
    nameAr: z.string().min(1, t("nameArRequired")).max(100, t("nameArTooLong")),
    type: PaymentMethodTypeEnum.refine(val => val !== undefined, { message: t("typeRequired") }),
    descriptionEn: z.string().max(500, t("descriptionEnTooLong")).optional(),
    descriptionAr: z.string().max(500, t("descriptionArTooLong")).optional(),
    active: z.boolean(), // Include active status for update
});

export type UpdatePaymentMethodInput = z.infer<ReturnType<typeof updatePaymentMethodSchema>>;
