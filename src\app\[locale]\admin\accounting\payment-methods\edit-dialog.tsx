"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch"; // Import Switch
import { updatePaymentMethod } from "@/lib/api/admin/accounting/payment-methods"; // Import correct API
import { UpdatePaymentMethodInput, PaymentMethodTypeEnum, updatePaymentMethodSchema } from "@/lib/schemas/admin/accounting/payment-methods"; // Import correct schema/types
import { PaymentMethodDto } from "@/lib/dto/admin/accounting/payment-methods.dto"; // Import correct DTO
import { ScrollArea } from "@/components/ui/scroll-area";

interface EditPaymentMethodDialogProps {
    isOpen: boolean;
    setIsOpen: (open: boolean) => void;
    paymentMethod: PaymentMethodDto;
}

export function EditPaymentMethodDialog({ isOpen, setIsOpen, paymentMethod }: EditPaymentMethodDialogProps) {
    const t = useTranslations("AdminPaymentMethodsPage.EditDialog");
    const tValidation = useTranslations("AdminPaymentMethodsPage.EditDialog.validation");
    const tTypes = useTranslations("AdminPaymentMethodsPage.types");
    const queryClient = useQueryClient();

    const formSchema = updatePaymentMethodSchema(tValidation);
    const form = useForm<UpdatePaymentMethodInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            nameEn: paymentMethod.nameEn || "",
            nameAr: paymentMethod.nameAr || "",
            type: paymentMethod.type,
            descriptionEn: paymentMethod.descriptionEn || "",
            descriptionAr: paymentMethod.descriptionAr || "",
            active: paymentMethod.active, // Initialize active state
        },
    });

    // Reset form when paymentMethod changes (e.g., opening dialog for a different item)
    useEffect(() => {
        if (paymentMethod) {
            form.reset({
                nameEn: paymentMethod.nameEn || "",
                nameAr: paymentMethod.nameAr || "",
                type: paymentMethod.type,
                descriptionEn: paymentMethod.descriptionEn || "",
                descriptionAr: paymentMethod.descriptionAr || "",
                active: paymentMethod.active,
            });
        }
    }, [paymentMethod, form]);

    const mutation = useMutation({
        mutationFn: (data: UpdatePaymentMethodInput) => updatePaymentMethod(paymentMethod.id, data),
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.nameEn }));
            queryClient.invalidateQueries({ queryKey: ["payment-methods"] });
            setIsOpen(false);
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message }));
        },
    });

    const onSubmit = (values: UpdatePaymentMethodInput) => {
        console.log("Updating:", values);
        mutation.mutate(values);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description", { name: paymentMethod.nameEn })}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <ScrollArea className="h-[50vh] pr-6">
                            <div className="space-y-4 p-1">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <FormField
                                        control={form.control}
                                        name="nameEn"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("nameEnLabel")}</FormLabel>
                                                <FormControl>
                                                    <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="nameAr"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("nameArLabel")}</FormLabel>
                                                <FormControl>
                                                    <Input dir="rtl" placeholder={t("nameArPlaceholder")} {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>
                                <FormField
                                    control={form.control}
                                    name="type"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("typeLabel")}</FormLabel>
                                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder={t("typePlaceholder")} />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {PaymentMethodTypeEnum.options.map((type) => (
                                                        <SelectItem key={type} value={type}>
                                                            {tTypes(type as any)}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="descriptionEn"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                            <FormControl>
                                                <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="descriptionAr"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                            <FormControl>
                                                <Textarea dir="rtl" placeholder={t("descriptionArPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="active"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <FormLabel className="text-base">
                                                    {t("activeLabel")}
                                                </FormLabel>
                                                <FormDescription>
                                                    {t("activeDescription")}
                                                </FormDescription>
                                            </div>
                                            <FormControl>
                                                <Switch
                                                    checked={field.value}
                                                    onCheckedChange={field.onChange}
                                                />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </ScrollArea>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={mutation.isPending}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
