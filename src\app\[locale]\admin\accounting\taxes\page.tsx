import { getTranslations, setRequestLocale } from "next-intl/server"; // Use stable setRequestLocale
import { TaxesDataTable } from "./data-table";
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card";
import {ChartOfAccountsDataTable} from "@/app/[locale]/admin/accounting/chart-of-accounts/data-table";

interface AdminTaxesPageProps {
    params: {
        locale: string;
    };
}

export default async function AdminTaxesPage({ params }: AdminTaxesPageProps) {
    // Await params before accessing locale
    const { locale } = await params;
    setRequestLocale(locale); // Use stable setRequestLocale

    // Translations will be fetched in the Client Component
    // const t = await getTranslations("AdminTaxesPage");
    // const tShared = await getTranslations("Shared");

    // Fetch page-level translations needed directly in the Server Component
    const tPage = await getTranslations("AdminTaxesPage");

    return (
        <Card>
            <CardHeader>
                <CardTitle>{tPage("title")}</CardTitle>
                <CardDescription>{tPage("description")}</CardDescription>
            </CardHeader>
            <CardContent>
                <TaxesDataTable />
            </CardContent>
        </Card>
    );
}
