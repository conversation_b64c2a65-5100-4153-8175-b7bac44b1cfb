import { z } from 'zod';
import { useTranslations } from 'next-intl';

// Helper function to create schemas with translations
export const createEducationalStageSchema = (t: ReturnType<typeof useTranslations<'AdminEducationalStagesPage.AddDialog.validation'>>) => z.object({
    nameEn: z.string()
        .min(1, { message: t('nameEnRequired') })
        .max(100, { message: t('nameEnTooLong') }),
    nameAr: z.string()
        .min(1, { message: t('nameArRequired') })
        .max(100, { message: t('nameArTooLong') }),
    sortOrder: z.coerce // Use coerce for number inputs from forms
        .number({ invalid_type_error: t('sortOrderRequired') })
        .int({ message: t('sortOrderInteger') })
        .min(1, { message: t('sortOrderMin') }), // Assuming sort order should be positive
});

// Schema for updating (all fields optional)
export const updateEducationalStageSchema = (t: ReturnType<typeof useTranslations<'AdminEducationalStagesPage.EditDialog.validation'>>) => z.object({
    nameEn: z.string()
        .min(1, { message: t('nameEnRequired') })
        .max(100, { message: t('nameEnTooLong') })
        .optional(),
    nameAr: z.string()
        .min(1, { message: t('nameArRequired') })
        .max(100, { message: t('nameArTooLong') })
        .optional(),
    sortOrder: z.coerce
        .number({ invalid_type_error: t('sortOrderRequired') })
        .int({ message: t('sortOrderInteger') })
        .min(1, { message: t('sortOrderMin') })
        .optional(),
}).refine(data => Object.keys(data).some(key => data[key as keyof typeof data] !== undefined), { // Ensure at least one field is provided for update
    message: t('noChanges'), // Use a specific translation for this
    path: [], // General error, not specific to a field
});


export type CreateEducationalStageInput = z.infer<ReturnType<typeof createEducationalStageSchema>>;
export type UpdateEducationalStageInput = z.infer<ReturnType<typeof updateEducationalStageSchema>>;
