import { z } from "zod";
import { UseTranslations } from "next-intl";

// Base schema for common fields
const baseFeeCategorySchema = (t: UseTranslations<string>) => ({
    nameEn: z.string()
        .min(1, { message: t("nameEnRequired") })
        .max(100, { message: t("nameEnTooLong") }),
    nameAr: z.string()
        .min(1, { message: t("nameArRequired") })
        .max(100, { message: t("nameArTooLong") }),
    descriptionEn: z.string()
        .max(500, { message: t("descriptionEnTooLong") })
        .optional(),
    descriptionAr: z.string()
        .max(500, { message: t("descriptionArTooLong") })
        .optional(),
});

// Schema for creating a fee category
export const createFeeCategorySchema = (t: UseTranslations<string>) => z.object({
    ...baseFeeCategorySchema(t),
});

// Schema for updating a fee category (all fields optional)
export const updateFeeCategorySchema = (t: UseTranslations<string>) => z.object({
    nameEn: z.string()
        .min(1, { message: t("nameEnRequired") })
        .max(100, { message: t("nameEnTooLong") })
        .optional(),
    nameAr: z.string()
        .min(1, { message: t("nameArRequired") })
        .max(100, { message: t("nameArTooLong") })
        .optional(),
    descriptionEn: z.string()
        .max(500, { message: t("descriptionEnTooLong") })
        .optional()
        .nullable(), // Allow null to clear the field if needed
    descriptionAr: z.string()
        .max(500, { message: t("descriptionArTooLong") })
        .optional()
        .nullable(), // Allow null to clear the field if needed
});


// Infer TypeScript types from schemas
export type CreateFeeCategoryInput = z.infer<ReturnType<typeof createFeeCategorySchema>>;
export type UpdateFeeCategoryInput = z.infer<ReturnType<typeof updateFeeCategorySchema>>;
