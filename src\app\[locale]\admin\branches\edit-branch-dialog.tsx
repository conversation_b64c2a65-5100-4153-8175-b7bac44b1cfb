"use client";

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { updateBranch } from '@/lib/api/admin/branches';
import { BranchDto } from '@/lib/dto/admin/branch.dto';
import { UpdateBranchInput, updateBranchSchema } from '@/lib/schemas/admin/branch';
import { getErrorMessage } from '@/lib/utils'; // Corrected path

interface EditBranchDialogProps {
    branch: BranchDto;
    children: React.ReactNode; // To wrap the trigger element (e.g., button or menu item)
    onSuccess?: () => void; // Optional callback
}

export function EditBranchDialog({ branch, children, onSuccess }: EditBranchDialogProps) {
    const t = useTranslations('AdminBranchesPage.EditDialog');
    const queryClient = useQueryClient();
    const [isOpen, setIsOpen] = React.useState(false);

    const form = useForm<UpdateBranchInput>({
        resolver: zodResolver(updateBranchSchema(t)),
        defaultValues: {
            nameEn: branch.nameEn ?? '',
            nameAr: branch.nameAr ?? '',
            address: branch.address ?? '',
        },
    });

     // Reset form when branch data changes (e.g., opening dialog for a different branch)
    useEffect(() => {
        if (isOpen) {
            form.reset({
                nameEn: branch.nameEn ?? '',
                nameAr: branch.nameAr ?? '',
                address: branch.address ?? '',
            });
        }
    }, [branch, isOpen, form]);


    const mutation = useMutation({
        mutationFn: (data: UpdateBranchInput) => updateBranch(branch.id, data),
        onSuccess: (data) => {
            toast.success(t('successToast', { name: data.nameEn }));
            queryClient.invalidateQueries({ queryKey: ['branches'] });
            queryClient.invalidateQueries({ queryKey: ['branch', branch.id] }); // Also invalidate single branch query if used
            setIsOpen(false); // Close dialog
            onSuccess?.();
        },
        onError: (error) => {
            toast.error(t('errorToast', { error: getErrorMessage(error) }));
        },
    });

    const onSubmit = (data: UpdateBranchInput) => {
        // Only submit changed values
        const changedData: UpdateBranchInput = {};
        if (data.nameEn !== branch.nameEn) changedData.nameEn = data.nameEn;
        if (data.nameAr !== branch.nameAr) changedData.nameAr = data.nameAr;
        if (data.address !== (branch.address ?? '')) changedData.address = data.address; // Handle potential null/undefined

        if (Object.keys(changedData).length === 0) {
             toast.info(t('noChanges'));
             setIsOpen(false);
             return;
        }

        mutation.mutate(changedData);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>{children}</DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>{t('title')}</DialogTitle>
                    <DialogDescription>{t('description', { name: branch.nameEn })}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4 py-4">
                        <FormField
                            control={form.control}
                            name="nameEn"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('nameEnLabel')}</FormLabel>
                                    <FormControl>
                                        <Input placeholder={t('nameEnPlaceholder')} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="nameAr"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('nameArLabel')}</FormLabel>
                                    <FormControl>
                                        <Input placeholder={t('nameArPlaceholder')} {...field} dir="rtl" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="address"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('addressLabel')}</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder={t('addressPlaceholder')}
                                            className="resize-none"
                                            {...field}
                                            value={field.value ?? ''} // Ensure value is not null/undefined for textarea
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={mutation.isPending}>
                                {t('cancelButton')}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending || !form.formState.isDirty}>
                                {mutation.isPending ? t('savingButton') : t('saveButton')}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
