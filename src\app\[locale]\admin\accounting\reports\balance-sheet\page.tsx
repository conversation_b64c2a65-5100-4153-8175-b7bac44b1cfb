"use client";

import { <PERSON><PERSON>eader, PageHeaderDescription, PageHeaderHeading } from "@/components/ui/page-header";

import { BalanceSheetDisplay } from "./balance-sheet-display"; // This component will be created next
import { BalanceSheetDto } from "@/lib/dto/admin/accounting/balance-sheet.dto";
import { DataTableToolbar } from "./data-table-toolbar";
import { ErrorResponse } from "@/lib/dto/error-response.dto";
import { Skeleton } from "@/components/ui/skeleton";
import { getBalanceSheet } from "@/lib/api/admin/accounting/balance-sheet";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";

export default function BalanceSheetPage() {
  const t = useTranslations("BalanceSheetPage"); // Assuming translations will be under this key
  const searchParams = useSearchParams();
  const fromDate = searchParams.get("fromDate") || undefined;
  const toDate = searchParams.get("toDate") || undefined;

  const {
    data: balanceSheetData,
    isLoading,
    error,
  } = useQuery<BalanceSheetDto, ErrorResponse>({
    queryKey: ["balanceSheet", fromDate, toDate],
    queryFn: async (): Promise<BalanceSheetDto> => { // Explicitly type the return
      const result = await getBalanceSheet(fromDate, toDate);
      // getBalanceSheet now throws ErrorResponse directly on failure
      return result;
    },
  });

  return (
    <>
      <PageHeader
      >
         <PageHeaderHeading>{t("title")}</PageHeaderHeading>
         <PageHeaderDescription>{t("description")}</PageHeaderDescription>
        {/* <h3>{t("title")}</h3>
        <p className="text-sm text-muted-foreground">{t("description")}</p> */}
      </PageHeader>
      <div className="container mx-auto py-10">
        <DataTableToolbar /> {/* Toolbar for date filtering */}
        {isLoading && (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        )}
        {error && (
          <div className="text-red-500">
            {t("errorLoading")}: {error.message}
          </div>
        )}
        {balanceSheetData && (
          <BalanceSheetDisplay data={balanceSheetData} /> // Component to display data
        )}
      </div>
    </>
  );
}
