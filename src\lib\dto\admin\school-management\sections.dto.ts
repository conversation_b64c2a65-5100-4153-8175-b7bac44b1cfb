import { z } from 'zod';

// Base Section Schema (for common fields, can be extended)
export const SectionBaseSchema = z.object({
  name: z.string().min(1, { message: "validation.section.name.notBlank" }).max(50, { message: "validation.section.name.size" }),
  capacity: z.coerce.number().int().nonnegative({ message: "validation.section.capacity.positive" }).optional().nullable(),
});

// Schema for creating a new section
export const CreateSectionSchema = SectionBaseSchema.extend({
  gradeLevelId: z.string().uuid({ message: "validation.section.gradeLevel.notNull" }),
  branchId: z.string().uuid({ message: "validation.section.branch.notNull" }),
  academicYearId: z.string().uuid({ message: "validation.section.academicYear.notNull" }),
});

// Schema for updating an existing section (all fields optional)
export const UpdateSectionSchema = z.object({
  name: z.string().min(1, { message: "validation.section.name.notBlank" }).max(50, { message: "validation.section.name.size" }).optional(),
  capacity: z.coerce.number().int().nonnegative({ message: "validation.section.capacity.positive" }).optional().nullable(),
  // Parent IDs (gradeLevelId, branchId, academicYearId) are typically not updated here
  // If they need to be updatable, add them as optional fields.
});


// TypeScript interface for the Section DTO (response from API)
export interface SectionDto {
  id: string; // UUID
  name: string;
  gradeLevelId: string; // UUID
  branchId: string; // UUID
  academicYearId: string; // UUID
  capacity?: number | null;
  gradeLevelNameEn?: string | null;
  gradeLevelNameAr?: string | null;
  branchNameEn?: string | null;
  branchNameAr?: string | null;
  academicYearName?: string | null;
  createdAt: string; // LocalDateTime will be string in JSON
  updatedAt?: string | null; // LocalDateTime will be string in JSON
}

// For type inference
export type CreateSectionPayload = z.infer<typeof CreateSectionSchema>;
export type UpdateSectionPayload = z.infer<typeof UpdateSectionSchema>;