import { API_BASE_URL } from "@/lib/constants";
import { IncomeStatementDto } from "@/lib/dto/admin/accounting/income-statement.dto";
import { ErrorResponse } from "@/lib/dto/error-response.dto";
import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { useQuery } from "@tanstack/react-query";

const INCOME_STATEMENT_API_PATH = `${API_BASE_URL}/accounting/income-statement`;

/**
 * Fetches the income statement for a given date range
 */
export async function getIncomeStatement(
  startDate?: string,
  endDate?: string
): Promise<IncomeStatementDto> {
  const params = new URLSearchParams();
  if (startDate) {
    params.append("startDate", startDate);
  }
  if (endDate) {
    params.append("endDate", endDate);
  }

  const url = `${INCOME_STATEMENT_API_PATH}${params.toString() ? `?${params.toString()}` : ""}`;

  const response = await fetchWithAuth(url, {
    method: "GET",
  });

  const data = await response.json();

  if (!response.ok) {
    // Throw the error response directly
    throw data as ErrorResponse;
  }

  return data as IncomeStatementDto;
}

/**
 * React Query hook for fetching income statement
 */
export function useIncomeStatement(startDate?: string, endDate?: string) {
  return useQuery<IncomeStatementDto, ErrorResponse>({
    queryKey: ["incomeStatement", startDate, endDate],
    queryFn: async () => {
      return await getIncomeStatement(startDate, endDate);
    },
    enabled: !!startDate && !!endDate, // Only fetch when both dates are provided
  });
}
