"use client";

import { Table } from "@tanstack/react-table";
import { AddStageDialog } from "./add-dialog"; // Import the Add dialog
import { useQueryClient } from "@tanstack/react-query"; // To refetch data

interface DataTableToolbarProps<TData> {
    table: Table<TData>;
    // No search or filter props needed for this version
}

export function DataTableToolbar<TData>({
    table,
}: DataTableToolbarProps<TData>) {
    const queryClient = useQueryClient();

    const handleStageAdded = () => {
        queryClient.invalidateQueries({ queryKey: ['educationalStages'] });
    };

    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
                {/* No search input */}
            </div>
            <div className="flex items-center space-x-2">
                 {/* Add Button integrated via Dialog Trigger */}
                <AddStageDialog onStageAdded={handleStageAdded} />
                {/* No View Options Dropdown needed if columns are fixed */}
            </div>
        </div>
    );
}
