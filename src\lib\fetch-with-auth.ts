import { useAuthStore } from "@/stores/auth"; // Import the Zustand store

/**
 * A wrapper around the native fetch function that automatically adds the
 * Authorization header with the Bearer token from the auth store.
 *
 * @param url The URL to fetch.
 * @param options Optional fetch options (method, headers, body, etc.).
 * @param token Optional explicit token to use for authentication (useful for Server Components).
 * @returns A Promise resolving to the Response object.
 */
export async function fetchWithAuth(
  url: string | URL | Request,
  options: RequestInit = {},
  token?: string | null // Add optional token parameter
): Promise<Response> {
  // Determine the token to use: explicit parameter > Zustand store
  const accessToken = token ?? useAuthStore.getState().accessToken;

  // Prepare headers
  const headers = new Headers(options.headers);

  // Add Authorization header if token exists
  if (accessToken) {
    headers.set("Authorization", `Bearer ${accessToken}`);
  }

  // Ensure Content-Type is set for relevant methods if not already present
  if (options.body && !headers.has("Content-Type")) {
     if (typeof options.body === 'string' || options.body instanceof URLSearchParams) {
       // Assume JSON for string bodies unless specified otherwise
       headers.set("Content-Type", "application/json");
     }
     // FormData sets its own Content-Type with boundary
     // Blob/BufferSource might need specific Content-Types set by the caller
  }

  // Log the request for debugging
  const urlString = typeof url === 'string' ? url : url.toString();
  console.log(`[fetchWithAuth] Requesting: ${urlString}`);

  try {
    // Perform the fetch request
    const response = await fetch(url, {
      ...options,
      headers,
    });

    // Log response status for debugging
    console.log(`[fetchWithAuth] Response status: ${response.status} for ${urlString}`);

    // Note: We don't throw errors for !response.ok here.
    // The calling function (e.g., getUsers) is responsible for checking
    // response.ok and handling API-specific errors or structures.
    // This makes the helper more generally reusable.

    return response;
  } catch (error) {
    // Log the error for debugging
    console.error(`[fetchWithAuth] Network error for ${urlString}:`, error);
    throw error;
  }
}

// Example usage (similar to what's in getUsers):
/*
async function exampleUsage() {
  try {
    const response = await fetchWithAuth('/some/protected/endpoint');
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Failed to parse error' }));
      throw new Error(errorData.message || `Request failed: ${response.statusText}`);
    }
    const data = await response.json();
    console.log(data);
  } catch (error) {
    console.error("API call failed:", error);
    // Handle error appropriately (e.g., show toast notification)
  }
}
*/
