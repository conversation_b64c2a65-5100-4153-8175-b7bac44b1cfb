"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea"; // Use Textarea for descriptions
import { PlusCircle } from "lucide-react";
import { createFeeCategory } from "@/lib/api/admin/accounting/fee-categories"; // Adjust API import
import { CreateFeeCategoryInput, createFeeCategorySchema } from "@/lib/schemas/admin/accounting/fee-categories"; // Adjust schema import
import { ScrollArea } from "@/components/ui/scroll-area";

export function AddFeeCategoryDialog() {
    const [isOpen, setIsOpen] = useState(false);
    const [hasMounted, setHasMounted] = useState(false);

    useEffect(() => {
        setHasMounted(true);
    }, []);

    const t = useTranslations("AdminFeeCategoriesPage.AddDialog"); // Adjust translation namespace
    const tValidation = useTranslations("AdminFeeCategoriesPage.AddDialog.validation"); // Adjust validation namespace
    const queryClient = useQueryClient();

    const formSchema = createFeeCategorySchema(tValidation);
    const form = useForm<CreateFeeCategoryInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            nameEn: "",
            nameAr: "",
            descriptionEn: "",
            descriptionAr: "",
        },
    });

    const mutation = useMutation({
        mutationFn: createFeeCategory, // Use correct API function
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.nameEn })); // Adjust success message
            queryClient.invalidateQueries({ queryKey: ["fee-categories"] }); // Adjust query key
            setIsOpen(false);
            form.reset();
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message })); // Adjust error message
        },
    });

    const onSubmit = (values: CreateFeeCategoryInput) => {
        console.log("Submitting Fee Category:", values);
        mutation.mutate(values);
    };

    if (!hasMounted) {
        return (
            <Button onClick={() => setIsOpen(true)}>
                <PlusCircle className="mr-2 h-4 w-4" />
                {t("triggerButton")}
            </Button>
        );
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t("triggerButton")}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]"> {/* Adjust width if needed */}
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description")}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        {/* No ScrollArea needed for fewer fields */}
                        <div className="space-y-4 p-1">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <FormField
                                    control={form.control}
                                    name="nameEn"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("nameEnLabel")}</FormLabel>
                                            <FormControl>
                                                <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="nameAr"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("nameArLabel")}</FormLabel>
                                            <FormControl>
                                                <Input dir="rtl" placeholder={t("nameArPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <FormField
                                control={form.control}
                                name="descriptionEn"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                        <FormControl>
                                            <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="descriptionAr"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                        <FormControl>
                                            <Textarea dir="rtl" placeholder={t("descriptionArPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={mutation.isPending}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
