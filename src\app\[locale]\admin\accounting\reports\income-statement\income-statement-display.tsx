import { AccountBalanceDto } from "@/lib/dto/admin/accounting/account-balance.dto";
import { IncomeStatementDto } from "@/lib/dto/admin/accounting/income-statement.dto";
import { useTranslations } from "next-intl";

interface IncomeStatementDisplayProps {
  data: IncomeStatementDto;
}

export function IncomeStatementDisplay({ data }: IncomeStatementDisplayProps) {
  const t = useTranslations("IncomeStatementPage");

  const renderAccountSection = (
    accounts: AccountBalanceDto[],
    sectionTitle: string,
    totalAmount: number
  ) => (
    <div className="mb-6">
      <h3 className="text-lg font-semibold mb-2">{sectionTitle}</h3>
      <div className="ml-4">
        {accounts.map((account) => (
          <div key={account.accountId} className="flex justify-between border-b py-1">
            <span>{account.accountName} ({account.accountNumber})</span>
            <span>{account.balance.toFixed(2)}</span>
          </div>
        ))}
        <div className="flex justify-between font-semibold mt-2 pt-2 border-t">
          <span>{t("total")} {sectionTitle}</span>
          <span>{totalAmount.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      <div className="text-right text-sm">
        {t("reportPeriod")}: {data.startDate} - {data.endDate}
      </div>
      
      {renderAccountSection(data.incomeAccounts, t("revenue"), data.totalRevenue)}
      {renderAccountSection(data.expenseAccounts, t("expenses"), data.totalExpenses)}
      
      <div className="flex justify-between font-bold mt-8 pt-4 border-t-2">
        <span>{t("netIncome")}</span>
        <span className={data.netIncome >= 0 ? "text-green-600" : "text-red-600"}>
          {data.netIncome.toFixed(2)}
        </span>
      </div>
    </div>
  );
}
