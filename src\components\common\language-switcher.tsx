"use client";

import { useLocale, useTranslations } from "next-intl";
import { usePathname, useRouter } from "@/i18n/navigation"; // Use the i18n navigation
import { Button } from "@/components/ui/button"; // Keep Button for trigger styling if needed
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // Import Select components
import { Globe } from "lucide-react"; // Using lucide icon for visual cue

export function LanguageSwitcher() {
  const t = useTranslations("LanguageSwitcher");
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const switchLocale = (nextLocale: string) => {
    // Use the router to switch the locale, preserving the current path
    router.replace(pathname, { locale: nextLocale });
  };

  return (
    <Select onValueChange={switchLocale} defaultValue={locale}>
      <SelectTrigger
        className="w-auto gap-2 px-3 py-1.5 text-sm h-8" // Adjust styling as needed
        aria-label={t("selectLanguageLabel")}
      >
        <Globe className="h-4 w-4 text-muted-foreground" />
        <SelectValue placeholder={t("selectLanguageLabel")}>
          {/* Placeholder for current language icon */}
          <span className="font-medium">
            {locale === "en" ? "EN" : "AR"}
          </span>
        </SelectValue>
      </SelectTrigger>
      <SelectContent align="end">
        <SelectItem value="en">
          <div className="flex items-center gap-2">
            {/* Placeholder for EN flag icon */}
            <span>EN</span>
            <span className="text-muted-foreground">English</span>
          </div>
        </SelectItem>
        <SelectItem value="ar">
          <div className="flex items-center gap-2">
            {/* Placeholder for AR flag icon */}
            <span>AR</span>
            <span className="text-muted-foreground">العربية</span>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  );
}
