"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PlusCircle } from "lucide-react";
import { createPaymentMethod } from "@/lib/api/admin/accounting/payment-methods"; // Import correct API
import { CreatePaymentMethodInput, PaymentMethodTypeEnum, createPaymentMethodSchema } from "@/lib/schemas/admin/accounting/payment-methods"; // Import correct schema/types
import { ScrollArea } from "@/components/ui/scroll-area";

export function AddPaymentMethodDialog() {
    const [isOpen, setIsOpen] = useState(false);
    const [hasMounted, setHasMounted] = useState(false);

    useEffect(() => {
        setHasMounted(true);
    }, []);

    const t = useTranslations("AdminPaymentMethodsPage.AddDialog");
    const tValidation = useTranslations("AdminPaymentMethodsPage.AddDialog.validation");
    const tTypes = useTranslations("AdminPaymentMethodsPage.types");
    const queryClient = useQueryClient();

    const formSchema = createPaymentMethodSchema(tValidation);
    const form = useForm<CreatePaymentMethodInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            nameEn: "",
            nameAr: "",
            type: undefined,
            descriptionEn: "",
            descriptionAr: "",
        },
    });

    const mutation = useMutation({
        mutationFn: createPaymentMethod,
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.nameEn }));
            queryClient.invalidateQueries({ queryKey: ["payment-methods"] });
            setIsOpen(false);
            form.reset();
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message }));
        },
    });

    const onSubmit = (values: CreatePaymentMethodInput) => {
        console.log("Submitting:", values);
        mutation.mutate(values);
    };

    if (!hasMounted) {
        return (
            <Button onClick={() => setIsOpen(true)}>
                <PlusCircle className="mr-2 h-4 w-4" />
                {t("triggerButton")}
            </Button>
        );
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t("triggerButton")}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description")}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <ScrollArea className="h-[50vh] pr-6">
                            <div className="space-y-4 p-1">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <FormField
                                        control={form.control}
                                        name="nameEn"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("nameEnLabel")}</FormLabel>
                                                <FormControl>
                                                    <Input placeholder={t("nameEnPlaceholder")} {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="nameAr"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>{t("nameArLabel")}</FormLabel>
                                                <FormControl>
                                                    <Input dir="rtl" placeholder={t("nameArPlaceholder")} {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>
                                <FormField
                                    control={form.control}
                                    name="type"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("typeLabel")}</FormLabel>
                                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder={t("typePlaceholder")} />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {PaymentMethodTypeEnum.options.map((type) => (
                                                        <SelectItem key={type} value={type}>
                                                            {tTypes(type as any)} {/* Use translation key */}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="descriptionEn"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("descriptionEnLabel")}</FormLabel>
                                            <FormControl>
                                                <Textarea placeholder={t("descriptionEnPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="descriptionAr"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>{t("descriptionArLabel")}</FormLabel>
                                            <FormControl>
                                                <Textarea dir="rtl" placeholder={t("descriptionArPlaceholder")} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </ScrollArea>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={mutation.isPending}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
