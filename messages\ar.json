{"LoginPage": {"title": "تسجيل الدخول", "description": "أدخل بريدك الإلكتروني أدناه لتسجيل الدخول إلى حسابك.", "emailLabel": "الب<PERSON>يد الإلكتروني", "emailPlaceholder": "<EMAIL>", "passwordLabel": "كلمة المرور", "passwordPlaceholder": "********", "loginButton": "تسجيل الدخول", "loggingInButton": "جارٍ تسجيل الدخول...", "loginSuccessToast": "تم تسجيل الدخول بنجاح!", "loginErrorToast": "فشل تسجيل الدخول. يرجى المحاولة مرة أخرى.", "invalidEmailError": "عنوان بريد إلكتروني غير صالح", "passwordRequiredError": "كلمة المرور مطلوبة"}, "LanguageSwitcher": {"switchToEnglish": "التبديل إلى اللغة الإنجليزية", "switchToArabic": "التبديل إلى اللغة العربية", "selectLanguageLabel": "اختر اللغة"}, "AdminLayout": {"Navbar": {"toggleMenu": "تبديل قائمة التنقل", "searchPlaceholder": "بحث...", "toggleSidebar": "تبديل الشريط الجانبي", "toggleUserMenu": "تبديل قائمة المستخدم", "myAccount": "حسابي", "settings": "الإعدادات", "support": "الدعم", "logout": "تسجيل الخروج", "logoutSuccess": "تم تسجيل الخروج بنجاح"}, "Sidebar": {"panelTitle": "لوحة الإدارة", "dashboard": "لوحة التحكم", "userManagementGroup": "إدارة المستخدمين", "users": "المستخدمون", "roles": "الأدوار", "studentsManagementGroup": "إدارة الطلاب", "students": "الطلاب", "guardians": "أولياء الأمور", "schoolManagementGroup": "إدارة المدرسة", "stages": "المراحل", "gradeLevels": "المراحل الدراسية", "sections": "الأقسام", "academicYears": "السنوات الدراسية", "holidays": "العطلات", "branches": "الفروع", "accountingGroup": "المحاسبة", "journalEntries": "قيود اليومية", "configurations": "الإعدادات", "chartOfAccounts": "دليل الحسابات", "paymentMethods": "طرق الدفع", "feeCategories": "فئات الرسوم", "fees": "الرسوم", "expenseCategories": "فئات المصروفات", "expenses": "المصروفات", "taxes": "الضرائب", "reportsGroup": "التقارير", "generalLedger": "دفتر الأستاذ العام", "trialBalance": "ميزان المراجعة", "balanceSheet": "قائمة المركز المالي", "incomeStatement": "قائمة الدخل", "aiReports": "تقارير الذكاء الاصطناعي", "settings": "الإعدادات"}, "Footer": {"copyright": "© {year} مدرسة معالي. جميع الحقوق محفوظة."}}, "AdminDashboard": {"title": "لوحة التحكم", "Cards": {"totalRevenueTitle": "إجمالي الإيرادات", "totalRevenueValue": "٤٥٬٢٣١٫٨٩ $", "totalRevenueDescription": "+٢٠٫١٪ عن الشهر الماضي", "subscriptionsTitle": "الاشتراكات", "subscriptionsValue": "+٢٣٥٠", "subscriptionsDescription": "+١٨٠٫١٪ عن الشهر الماضي", "salesTitle": "المبيعات", "salesValue": "+١٢٬٢٣٤", "salesDescription": "+١٩٪ عن الشهر الماضي", "activeNowTitle": "نشط الآن", "activeNowValue": "+٥٧٣", "activeNowDescription": "+٢٠١ منذ الساعة الماضية"}, "overviewTitle": "نظرة عامة", "chartPlaceholder": "عنصر نائب للرسم البياني", "recentActivityTitle": "النشاط الأخير", "recentActivityDescription": "آخر التحديثات والإجراءات.", "activity1Title": "تسجيل مستخدم جديد", "activity1Description": "<EMAIL>", "activity1Value": "+٠٫٠٠ $", "welcomeTitle": "مرحباً بعودتك أيها المدير!", "welcomeDescription": "إليك نظرة عامة على حالة المدرسة.", "contentPlaceholder": "محتوى لوحة التحكم يوضع هنا. يمكنك إضافة رسوم بيانية وإحصائيات وروابط سريعة."}, "AdminJournalEntriesPage": {"title": "قيود اليومية", "description": "إدارة وتتبع المعاملات المالية من خلال قيود اليومية المفصلة", "table": {"postAction": "ترحيل القيد", "journalEntryNumber": "رق<PERSON> القيد", "entryDate": "تاريخ القيد", "referenceNumber": "الرقم المرجعي", "description": "الوصف", "totalAmount": "المبلغ الإجمالي", "status": "الحالة", "posted": "مرحّل", "unposted": "غير مرحّل", "filterByReference": "تصفية بالرقم المرجعي...", "filterByDate": "تصفية حسب نطاق التاريخ...", "filterByStatus": "تصفية حسب الحالة...", "statusAll": "كل الحالات", "statusPosted": "مرحّل", "statusUnposted": "غير مرحّل", "addJournalEntry": "إضافة قيد يومية", "postSuccess": "تم ترحيل قيد اليومية {id} بنجاح.", "postError": "فشل ترحيل قيد اليومية.", "journalEntry": "قيد اليومية"}, "form": {"addTitle": "إضافة قيد يومية جديد", "editTitle": "تعديل قيد يومية", "entryDateLabel": "تاريخ القيد", "referenceLabel": "الرقم المرجعي (اختياري)", "descriptionLabel": "الوصف", "linesLabel": "بن<PERSON><PERSON> القيد", "accountLabel": "الحساب", "typeLabel": "النوع", "debitLabel": "مدين", "creditLabel": "دائن", "amountLabel": "المبلغ", "lineDescriptionLabel": "وصف البند (اختياري)", "addLine": "إضافة بند", "removeLine": "إزالة بند", "selectAccountPlaceholder": "اختر حساب...", "selectTypePlaceholder": "اختر النوع...", "save": "حفظ قيد اليومية", "saving": "جاري الحفظ...", "addSuccess": "تم إنشاء قيد اليومية بنجاح.", "updateSuccess": "تم تحديث قيد اليومية بنجاح.", "addError": "فشل إنشاء قيد اليومية.", "updateError": "فشل تحديث قيد اليومية.", "postSuccess": "تم ترحيل قيد اليومية بنجاح.", "postError": "فشل ترحيل قيد اليومية.", "deleteSuccess": "تم حذف قيد اليومية بنجاح.", "deleteError": "فشل حذف قيد اليومية.", "confirmPostTitle": "تأكيد ترحيل القيد", "confirmPostMessage": "هل أنت متأكد من رغبتك في ترحيل قيد اليومية هذا؟ لا يمكن التراجع عن هذا الإجراء وسيؤدي إلى إنهاء القيد.", "confirmDeleteTitle": "تأكيد الحذف", "confirmDeleteMessage": "هل أنت متأكد من رغبتك في حذف قيد اليومية هذا؟ لا يمكن التراجع عن هذا الإجراء.", "totalDebits": "إجمالي المدين", "totalCredits": "إجمالي الدائن", "balance": "الرصيد", "errorLoadingAccounts": "خطأ في تحميل الحسابات", "searchAccountPlaceholder": "ابحث عن حساب...", "noAccountFound": "لم يتم العثور على حساب."}, "validation": {"entryDateRequired": "تاريخ القيد مطلوب.", "descriptionRequired": "الوصف مطلوب.", "descriptionTooLong": "لا يمكن أن يتجاوز الوصف 500 حرف.", "referenceTooLong": "لا يمكن أن يتجاوز الرقم المرجعي 100 حرف.", "accountIdRequired": "الحساب مطلوب.", "typeRequired": "النوع (مدين/دائن) مطلوب.", "amountPositive": "يجب أن يكون المبلغ موجبًا.", "lineDescriptionTooLong": "لا يمكن أن يتجاوز وصف البند 500 حرف.", "atLeastTwoLines": "مطلو<PERSON> بندان على الأقل.", "debitsMustEqualCredits": "يجب أن يتساوى إجمالي المدين مع إجمالي الدائن.", "atLeastOneDebit": "مط<PERSON><PERSON><PERSON> بند مدين واحد على الأقل.", "atLeastOneCredit": "مطلوب بند دائن واحد على الأقل."}}, "AdminJournalEntryViewPage": {"title": "تفاصيل قيد اليومية", "description": "عرض تفاصيل قيد اليومية المحدد.", "backButton": "العودة إلى القائمة", "journalEntryNumberLabel": "رق<PERSON> القيد", "entryDateLabel": "تاريخ القيد", "referenceLabel": "الرقم المرجعي", "statusLabel": "الحالة", "descriptionLabel": "الوصف", "postedOn": "تم الترحيل في {date}", "linesTitle": "بن<PERSON><PERSON> القيد", "tableAccount": "الحساب", "tableLineDescription": "وصف البند", "tableDebit": "مدين", "tableCredit": "دائن", "totalDebits": "إجمالي المدين", "totalCredits": "إجمالي الدائن"}, "AdminChartOfAccountsPage": {"title": "دليل الحسابات", "description": "إدارة الحسابات المالية المستخدمة في المدرسة.", "searchPlaceholder": "ابحث بالرقم أو الاسم...", "addAccountButton": "إضافة حساب", "showActiveOnly": "إظهار النشط فقط", "errorLoading": "خطأ في تحميل الحسابات: {error}", "noResults": "لم يتم العثور على حسابات.", "table": {"accountNumber": "رقم الحساب", "nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "category": "الفئة", "status": "الحالة", "parentAccount": "الحساب الأصلي", "actions": "الإجراءات", "actionsOpenMenu": "فتح قائمة الإجراءات", "actionsEdit": "تعديل الحساب", "actionsDelete": "<PERSON><PERSON><PERSON> الح<PERSON>اب", "deleteSuccessToast": "تم حذف الحساب '{accountNumber}' بنجاح.", "deleteErrorToast": "فشل حذف الحساب: {error}", "accountItem": "الحساب"}, "AddDialog": {"triggerButton": "إضافة حساب", "title": "إنشاء حساب جديد", "description": "أدخل تفاصيل الحساب المالي الجديد.", "accountNumberLabel": "رقم الحساب", "accountNumberPlaceholder": "مثال: 1010", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Cash on Hand", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: نقدية بالصندوق", "categoryLabel": "الفئة", "categoryPlaceholder": "اختر فئة", "parentAccountLabel": "الحساب الأصلي (اختياري)", "parentAccountPlaceholder": "اختر حسابًا أصليًا", "parentAccountDescription": "اربط هذا الحساب بحساب آخر موجود.", "noParentValue": "--- لا يو<PERSON>د حساب أصلي ---", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "إنشاء حساب", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء الحساب '{accountNumber}' بنجاح.", "errorToast": "فشل إنشاء الحساب: {error}", "validation": {"accountNumberRequired": "رقم الحساب مطلوب.", "accountNumberTooLong": "لا يمكن أن يتجاوز رقم الحساب 50 حرفًا.", "nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 255 حرفًا.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 255 حرفًا.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 1000 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 1000 حرف.", "categoryRequired": "فئة الحساب مطلوبة.", "invalidParentAccount": "الحساب الأصلي المحدد غير صالح."}}, "categories": {"ASSET": "أصل", "LIABILITY": "التزام", "EQUITY": "حقوق ملكية", "REVENUE": "إيراد", "EXPENSE": "مصروف"}, "EditDialog": {"title": "تعديل الحساب", "description": "تحديث تفاصيل الحساب: {accountNumber}", "accountNumberLabel": "رقم الحساب", "accountNumberDescription": "لا يمكن تغيير رقم الحساب.", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Cash on Hand", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: نقدية بالصندوق", "categoryLabel": "الفئة", "categoryPlaceholder": "اختر فئة", "parentAccountLabel": "الحساب الأصلي (اختياري)", "parentAccountPlaceholder": "اختر حسابًا أصليًا", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "activeLabel": "نشط", "activeDescription": "السماح بالمعاملات لهذا الحساب.", "saveButton": "حفظ التغييرات", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "successToast": "تم تحديث الحساب '{accountNumber}' بنجاح.", "errorToast": "فشل تحديث الحساب: {error}", "errorLoadingAccount": "خطأ في تحميل تفاصيل الحساب: {error}", "noChanges": "لم يتم اكتشاف أي تغييرات.", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 255 حرفًا.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 255 حرفًا.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 1000 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 1000 حرف.", "categoryRequired": "فئة الحساب مطلوبة.", "invalidParentAccount": "الحساب الأصلي المحدد غير صالح."}}}, "AdminEducationalStagesPage": {"title": "المراحل التعليمية", "description": "إدارة المراحل التعليمية داخل المدرسة (مثل رياض الأطفال، الابتدائي).", "addStageButton": "إضافة مرحلة", "errorLoadingTitle": "خطأ في تحميل المراحل", "errorLoading": "تعذر تحميل المراحل التعليمية: {error}", "noResults": "لم يتم العثور على مراحل تعليمية.", "table": {"nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "sortOrder": "ترتيب الفرز", "actions": "الإجراءات", "actionsOpenMenu": "فتح قائمة الإجراءات", "actionsEdit": "تعديل المرحلة", "actionsDelete": "<PERSON><PERSON><PERSON> الم<PERSON>ح<PERSON>ة", "deleteSuccessToast": "تم حذف المرحلة التعليمية '{name}' بنجاح.", "deleteErrorToast": "فشل حذف المرحلة: {error}", "educationalStageItem": "المرحلة التعليمية"}, "AddDialog": {"triggerButton": "إضافة مرحلة", "title": "إنشاء مرحلة تعليمية جديدة", "description": "أدخل تفاصيل المرحلة التعليمية الجديدة.", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Primary School", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: المرحلة الابتدائية", "sortOrderLabel": "ترتيب الفرز", "sortOrderPlaceholder": "مثال: 1", "saveButton": "إنشاء مرحلة", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء المرحلة التعليمية '{name}' بنجاح.", "errorToast": "فشل إنشاء المرحلة: {error}", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "sortOrderRequired": "ترتيب الفرز مطلوب.", "sortOrderInteger": "يجب أن يكون ترتيب الفرز عددًا صحيحًا.", "sortOrderMin": "يجب أن يكون ترتيب الفرز 1 على الأقل."}}, "EditDialog": {"title": "تعديل المرحلة التعليمية", "description": "تحديث تفاصيل المرحلة: {name}", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Primary School", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: المرحلة الابتدائية", "sortOrderLabel": "ترتيب الفرز", "sortOrderPlaceholder": "مثال: 1", "saveButton": "حفظ التغييرات", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "successToast": "تم تحديث المرحلة التعليمية '{name}' بنجاح.", "errorToast": "فشل تحديث المرحلة: {error}", "noChanges": "لم يتم اكتشاف أي تغييرات.", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "sortOrderRequired": "ترتيب الفرز مطلوب.", "sortOrderInteger": "يجب أن يكون ترتيب الفرز عددًا صحيحًا.", "sortOrderMin": "يجب أن يكون ترتيب الفرز 1 على الأقل."}}}, "AdminFeeCategoriesPage": {"title": "فئات الرسوم", "description": "إدارة الفئات المستخدمة لتجميع رسوم المدرسة.", "searchPlaceholder": "ابحث بالاسم...", "addFeeCategoryButton": "إضافة فئة", "resetFilters": "إعادة تعيين", "errorLoading": "خطأ في تحميل فئات الرسوم: {error}", "noResults": "لم يتم العثور على فئات رسوم.", "table": {"nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "descriptionEn": "الوصف (إنجليزي)", "descriptionAr": "الوصف (عربي)", "actions": "الإجراءات", "actionsOpenMenu": "فتح قائمة الإجراءات", "actionsEdit": "تعديل الفئة", "actionsDelete": "حذ<PERSON> الفئة", "deleteSuccessToast": "تم حذف فئة الرسوم '{name}' بنجاح.", "deleteErrorToast": "فشل حذف فئة الرسوم: {error}", "feeCategoryItem": "فئة الرسوم"}, "AddDialog": {"triggerButton": "إضافة فئة", "title": "إنشاء فئة رسوم جديدة", "description": "أدخل تفاصيل فئة الرسوم الجديدة.", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: <PERSON><PERSON>", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: رسوم دراسية", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "إنشاء فئة", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء فئة الرسوم '{name}' بنجاح.", "errorToast": "فشل إنشاء فئة الرسوم: {error}", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 500 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 500 حرف."}}, "EditDialog": {"title": "تعديل فئة الرسوم", "description": "تحديث تفاصيل فئة الرسوم: {name}", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: <PERSON><PERSON>", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: رسوم دراسية", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "حفظ التغييرات", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "successToast": "تم تحديث فئة الرسوم '{name}' بنجاح.", "errorToast": "فشل تحديث فئة الرسوم: {error}", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 500 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 500 حرف."}}}, "AdminAcademicYearsPage": {"title": "السنوات الدراسية", "description": "إدارة السنوات الدراسية للمدرسة وفصولها الدراسية.", "searchPlaceholder": "ابحث بالاسم...", "addAcademicYearButton": "إضافة سنة", "showActiveOnly": "إظهار النشط فقط", "resetFilters": "إعادة تعيين", "errorLoading": "خطأ في تحميل السنوات الدراسية: {error}", "noResults": "لم يتم العثور على سنوات دراسية.", "table": {"name": "الاسم", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "status": "الحالة", "actions": {"openMenu": "فتح قائمة الإجراءات", "edit": "تعديل السنة", "delete": "<PERSON><PERSON><PERSON> السنة", "activate": "تعيين كنشط", "activating": "جارٍ التفعيل...", "activateSuccessToast": "تم تعيين السنة الدراسية '{name}' كنشطة.", "activateErrorToast": "فشل تعيين السنة الدراسية كنشطة: {error}", "activateConfirmTitle": "تعيين سنة دراسية نشطة؟", "activateConfirmMessage": "هل أنت متأكد أنك تريد تعيين '{name}' كسنة دراسية نشطة؟ قد يؤثر هذا على أجزاء أخرى من النظام.", "academicYearItem": "السنة الدراسية"}, "actionsOpenMenu": "فتح قائمة الإجراءات", "actionsEdit": "تعديل السنة", "actionsDelete": "<PERSON><PERSON><PERSON> السنة", "actionsActivate": "تعيين كنشط", "deleteSuccessToast": "تم حذف السنة الدراسية '{name}' بنجاح.", "deleteErrorToast": "فشل حذف السنة الدراسية: {error}", "activateSuccessToast": "تم تعيين السنة الدراسية '{name}' كنشطة.", "activateErrorToast": "فشل تعيين السنة الدراسية كنشطة: {error}", "activateConfirmTitle": "تعيين سنة دراسية نشطة؟", "activateConfirmMessage": "هل أنت متأكد أنك تريد تعيين '{name}' كسنة دراسية نشطة؟ قد يؤثر هذا على أجزاء أخرى من النظام.", "activating": "جارٍ التفعيل...", "academicYearItem": "السنة الدراسية"}, "AddDialog": {"triggerButton": "إضافة سنة", "title": "إنشاء سنة دراسية جديدة", "description": "أدخل تفاصيل السنة الدراسية الجديدة.", "nameLabel": "الاسم", "namePlaceholder": "مثال: 2024-2025", "startDateLabel": "تاريخ البدء", "startDatePlaceholder": "اختر تاريخ البدء", "endDateLabel": "تاريخ الانتهاء", "endDatePlaceholder": "اختر تاريخ الانتهاء", "saveButton": "إنشاء سنة", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء السنة الدراسية '{name}' بنجاح.", "errorToast": "فشل إنشاء السنة الدراسية: {error}", "validation": {"nameRequired": "الاسم مطلوب.", "nameTooLong": "لا يمكن أن يتجاوز الاسم 50 حرفًا.", "startDateRequired": "تاريخ البدء مطلوب.", "startDateInvalidFormat": "يجب أن يكون تاريخ البدء بتنسيق YYYY-MM-DD.", "startDateInvalidDate": "تاريخ البدء غير صالح.", "endDateRequired": "تاريخ الانتهاء مطلوب.", "endDateInvalidFormat": "يجب أن يكون تاريخ الانتهاء بتنسيق YYYY-MM-DD.", "endDateInvalidDate": "تاريخ الانتهاء غير صالح.", "endDateBeforeStartDate": "يجب أن يكون تاريخ الانتهاء بعد تاريخ البدء."}}, "EditDialog": {"title": "تعديل السنة الدراسية", "description": "تحديث تفاصيل السنة الدراسية: {name}", "nameLabel": "الاسم", "namePlaceholder": "مثال: 2024-2025", "startDateLabel": "تاريخ البدء", "startDatePlaceholder": "اختر تاريخ البدء", "endDateLabel": "تاريخ الانتهاء", "endDatePlaceholder": "اختر تاريخ الانتهاء", "saveButton": "حفظ التغييرات", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "successToast": "تم تحديث السنة الدراسية '{name}' بنجاح.", "errorToast": "فشل تحديث السنة الدراسية: {error}", "noChanges": "لم يتم اكتشاف أي تغييرات.", "validation": {"nameRequired": "الاسم مطلوب.", "nameTooLong": "لا يمكن أن يتجاوز الاسم 50 حرفًا.", "startDateRequired": "تاريخ البدء مطلوب.", "startDateInvalidFormat": "يجب أن يكون تاريخ البدء بتنسيق YYYY-MM-DD.", "startDateInvalidDate": "تاريخ البدء غير صالح.", "endDateRequired": "تاريخ الانتهاء مطلوب.", "endDateInvalidFormat": "يجب أن يكون تاريخ الانتهاء بتنسيق YYYY-MM-DD.", "endDateInvalidDate": "تاريخ الانتهاء غير صالح.", "endDateBeforeStartDate": "يجب أن يكون تاريخ الانتهاء بعد تاريخ البدء."}}}, "AdminBranchesPage": {"title": "الفروع", "description": "إدارة فروع المدرسة.", "addBranchButton": "إضافة فرع", "errorLoading": "خطأ في تحميل الفروع: {error}", "noResults": "لم يتم العثور على فروع.", "table": {"nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "address": "العنوان", "actions": "الإجراءات", "actionsOpenMenu": "فتح قائمة الإجراءات", "actionsEdit": "تعديل الفرع", "actionsDelete": "حذ<PERSON> الفرع", "deleteSuccessToast": "تم حذف الفرع '{name}' بنجاح.", "deleteErrorToast": "فشل حذف الفرع: {error}", "branchItem": "الفرع"}, "AddDialog": {"triggerButton": "إضافة فرع", "title": "إنشاء فرع جديد", "description": "أدخل تفاصيل فرع المدرسة الجديد.", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Main Campus", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: الحرم الرئيسي", "addressLabel": "العنوان (اختياري)", "addressPlaceholder": "أدخل عنوان الفرع", "saveButton": "إنشاء فرع", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء الفرع '{name}' بنجاح.", "errorToast": "فشل إنشاء الفرع: {error}", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 150 حرفًا.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 150 حرفًا.", "addressTooLong": "لا يمكن أن يتجاوز العنوان 255 حرفًا."}}, "EditDialog": {"title": "تعديل الفرع", "description": "تحديث تفاصيل الفرع: {name}", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Main Campus", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: الحرم الرئيسي", "addressLabel": "العنوان (اختياري)", "addressPlaceholder": "أدخل عنوان الفرع", "saveButton": "حفظ التغييرات", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "successToast": "تم تحديث الفرع '{name}' بنجاح.", "errorToast": "فشل تحديث الفرع: {error}", "noChanges": "لم يتم اكتشاف أي تغييرات.", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 150 حرفًا.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 150 حرفًا.", "addressTooLong": "لا يمكن أن يتجاوز العنوان 255 حرفًا."}}}, "AdminPaymentMethodsPage": {"title": "طرق الدفع", "description": "إدارة طرق الدفع المقبولة في المدرسة.", "searchPlaceholder": "ابحث بالاسم...", "addPaymentMethodButton": "إضافة طريقة دفع", "showActiveOnly": "إظهار النشط فقط", "resetFilters": "إعادة تعيين", "errorLoading": "خطأ في تحميل طرق الدفع: {error}", "noResults": "لم يتم العثور على طرق دفع.", "table": {"nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "type": "النوع", "status": "الحالة", "actions": "الإجراءات", "actionsOpenMenu": "فتح قائمة الإجراءات", "actionsEdit": "تعديل الطريقة", "actionsDelete": "<PERSON>ذ<PERSON> الطريقة", "actionsActivate": "تفعيل الطريقة", "actionsDeactivate": "إلغاء تفعيل الطريقة", "deleteSuccessToast": "تم حذف طريقة الدفع '{name}' بنجاح.", "deleteErrorToast": "فشل حذف طريقة الدفع: {error}", "activateSuccessToast": "تم تفعيل طريقة الدفع '{name}' بنجاح.", "activateErrorToast": "فشل تفعيل طريقة الدفع: {error}", "deactivateSuccessToast": "تم إلغاء تفعيل طريقة الدفع '{name}' بنجاح.", "deactivateErrorToast": "فشل إلغاء تفعيل طريقة الدفع: {error}", "paymentMethodItem": "طريقة الدفع"}, "AddDialog": {"triggerButton": "إضافة طريقة", "title": "إنشاء طريقة دفع جديدة", "description": "أدخل تفاصيل طريقة الدفع الجديدة.", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Cash", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: نقداً", "typeLabel": "النوع", "typePlaceholder": "اختر نوعًا", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "إنشاء طريقة", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء طريقة الدفع '{name}' بنجاح.", "errorToast": "فشل إنشاء طريقة الدفع: {error}", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "typeRequired": "نوع طريقة الدفع مطلوب.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 500 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 500 حرف."}}, "EditDialog": {"title": "تعديل طريقة الدفع", "description": "تحديث تفاصيل طريقة الدفع: {name}", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Cash", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: نقداً", "typeLabel": "النوع", "typePlaceholder": "اختر نوعًا", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "activeLabel": "نشط", "activeDescription": "السماح باستخدام طريقة الدفع هذه.", "saveButton": "حفظ التغييرات", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "successToast": "تم تحديث طريقة الدفع '{name}' بنجاح.", "errorToast": "فشل تحديث طريقة الدفع: {error}", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "typeRequired": "نوع طريقة الدفع مطلوب.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 500 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 500 حرف."}}, "types": {"CASH": "نقداً", "BANK_TRANSFER": "تحويل بنكي", "CHEQUE": "شيك", "CREDIT_CARD": "بطاقة ائتمان", "DEBIT_CARD": "بطاقة خصم", "ONLINE_PAYMENT_GATEWAY": "بوابة دفع إلكترونية", "MOBILE_MONEY": "أموال الهاتف المحمول"}}, "AdminFeesPage": {"title": "الرسوم", "description": "إدارة رسوم المدرسة وقابليتها للتطبيق.", "filterByAcademicYearPlaceholder": "تصفية حسب السنة الدراسية...", "filterByCategoryPlaceholder": "تصفية حسب الفئة...", "allCategories": "جميع الفئات", "allAcademicYears": "جميع السنوات الدراسية", "addFeeButton": "إضافة رسم", "resetFilters": "إعادة تعيين", "errorLoading": "خطأ في تحميل الرسوم: {error}", "noResults": "لم يتم العثور على رسوم.", "table": {"nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "amount": "المبلغ", "academicYear": "السنة الدراسية", "dueDate": "تاريخ الاستحقاق", "category": "الفئة", "status": "الحالة", "actions": "الإجراءات", "actionsOpenMenu": "فتح قائمة الإجراءات", "actionsEdit": "تعديل الرسم", "actionsDelete": "<PERSON><PERSON><PERSON> الرسم", "actionsActivate": "تفعيل الرسم", "actionsDeactivate": "إلغاء تفعيل الرسم", "deleteSuccessToast": "تم حذف الرسم '{name}' بنجاح.", "deleteErrorToast": "فشل حذف الرسم: {error}", "activateSuccessToast": "تم تفعيل الرسم '{name}' بنجاح.", "activateErrorToast": "فشل تفعيل الرسم: {error}", "deactivateSuccessToast": "تم إلغاء تفعيل الرسم '{name}' بنجاح.", "deactivateErrorToast": "فشل إلغاء تفعيل الرسم: {error}", "activateConfirmTitle": "تفعيل الرسم؟", "activateConfirmMessage": "هل أنت متأكد أنك تريد تفعيل الرسم '{name}'؟", "deactivateConfirmTitle": "إلغاء تفعيل الرسم؟", "deactivateConfirmMessage": "هل أنت متأكد أنك تريد إلغاء تفعيل الرسم '{name}'؟ قد يمنع هذا تعيينه.", "feeItem": "الرسم"}, "AddDialog": {"triggerButton": "إضافة رسم", "title": "إنشاء رسم جديد", "description": "أدخل تفاصيل رسم المدرسة الجديد.", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Term 1 Tuition", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: رسوم الفصل الأول", "amountLabel": "المبلغ", "amountPlaceholder": "مثال: 1500.00", "academicYearLabel": "السنة الدراسية", "academicYearPlaceholder": "مثال: 2024-2025", "academicYearDescription": "التنسيق: YYYY-YYYY", "dueDateLabel": "تاريخ الاستحقاق", "dueDatePlaceholder": "اختر تاريخ الاستحقاق", "categoryLabel": "فئة الرسوم", "categoryPlaceholder": "اختر فئة", "applicableGradeLabel": "الصف المطبق (اختياري)", "applicableGradePlaceholder": "اختر الصف", "allGradesValue": "جميع الصفوف", "applicableStageLabel": "المرحلة المطبقة (اختياري)", "applicableStagePlaceholder": "اختر المرحلة", "allStagesValue": "جميع المراحل", "applicableBranchLabel": "الفرع المطبق (اختياري)", "applicableBranchPlaceholder": "اختر الفرع", "allBranchesValue": "جميع الفروع", "applicabilityDescription": "اتركه فارغًا للتطبيق على الكل.", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "إنشاء رسم", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء الرسم '{name}' بنجاح.", "errorToast": "فشل إنشاء الرسم: {error}", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 255 حرفًا.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 255 حرفًا.", "amountRequired": "المبلغ مطلوب.", "amountInvalid": "يجب أن يكون المبلغ رقمًا صالحًا.", "amountMin": "يجب أن يكون المبلغ صفرًا أو موجبًا.", "academicYearRequired": "السنة الدراسية مطلوبة.", "academicYearInvalidUuid": "يرجى اختيار سنة دراسية صالحة.", "dueDateRequired": "تاريخ الاستحقاق مطلوب.", "dueDateInvalidDate": "تاريخ الاستحقاق غير صالح.", "feeCategoryRequired": "فئة الرسوم مطلوبة.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 1000 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 1000 حرف."}}, "EditDialog": {"title": "تعديل الرسم", "description": "تحديث تفاصيل الرسم: {name}", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Term 1 Tuition", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: رسوم الفصل الأول", "amountLabel": "المبلغ", "amountPlaceholder": "مثال: 1500.00", "academicYearLabel": "السنة الدراسية", "academicYearPlaceholder": "مثال: 2024-2025", "academicYearDescription": "التنسيق: YYYY-YYYY", "dueDateLabel": "تاريخ الاستحقاق", "dueDatePlaceholder": "اختر تاريخ الاستحقاق", "categoryLabel": "فئة الرسوم", "categoryPlaceholder": "اختر فئة", "applicableGradeLabel": "الصف المطبق (اختياري)", "applicableGradePlaceholder": "اختر الصف", "allGradesValue": "جميع الصفوف", "applicableStageLabel": "المرحلة المطبقة (اختياري)", "applicableStagePlaceholder": "اختر المرحلة", "allStagesValue": "جميع المراحل", "applicableBranchLabel": "الفرع المطبق (اختياري)", "applicableBranchPlaceholder": "اختر الفرع", "allBranchesValue": "جميع الفروع", "applicabilityDescription": "اتركه فارغًا للتطبيق على الكل.", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "activeLabel": "نشط", "activeDescription": "السماح بتعيين هذا الرسم أو استخدامه.", "saveButton": "حفظ التغييرات", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "successToast": "تم تحديث الرسم '{name}' بنجاح.", "errorToast": "فشل تحديث الرسم: {error}", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 255 حرفًا.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 255 حرفًا.", "amountRequired": "المبلغ مطلوب.", "amountInvalid": "يجب أن يكون المبلغ رقمًا صالحًا.", "amountMin": "يجب أن يكون المبلغ صفرًا أو موجبًا.", "academicYearRequired": "السنة الدراسية مطلوبة.", "academicYearInvalidUuid": "يرجى اختيار سنة دراسية صالحة.", "dueDateRequired": "تاريخ الاستحقاق مطلوب.", "dueDateInvalidDate": "تاريخ الاستحقاق غير صالح.", "feeCategoryRequired": "فئة الرسوم مطلوبة.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 1000 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 1000 حرف."}}}, "AdminExpensesPage": {"title": "المصروفات", "description": "تسجيل وإدارة مصروفات المدرسة.", "filterByDatePlaceholder": "تصفية حسب نطاق التاريخ...", "filterByCategoryPlaceholder": "تصفية حسب الفئة...", "allCategories": "جميع الفئات", "addExpenseButton": "إضافة مصروف", "resetFilters": "إعادة تعيين", "errorLoading": "خطأ في تحميل المصروفات: {error}", "noResults": "لم يتم العثور على مصروفات.", "table": {"expenseDate": "التاريخ", "amount": "المبلغ", "description": "الوصف", "category": "الفئة", "paymentAccount": "دُفع من", "vendor": "المورد", "referenceNumber": "الرقم المرجعي", "actions": "الإجراءات", "deleteSuccessToast": "تم حذف سجل المصروف بنجاح.", "deleteErrorToast": "فشل حذف المصروف: {error}", "expenseItem": "المصروف", "amountBeforeTax": "المبلغ (الصافي)", "taxAmount": "مبلغ الضريبة", "totalAmount": "المبلغ الإجمالي", "tax": "الضريبة"}, "form": {"addTitle": "تسجيل مصروف جديد", "editTitle": "تعديل المصروف", "addDescription": "أدخل تفاصيل المصروف الجديد.", "editDescription": "تحديث تفاصيل المصروف.", "expenseDateLabel": "تاريخ المصروف", "expenseDatePlaceholder": "اختر التاريخ", "amountLabel": "المبلغ", "amountPlaceholder": "مثال: 150.50", "categoryLabel": "فئة المصروف", "categoryPlaceholder": "اختر فئة", "paymentAccountLabel": "حساب الدفع", "paymentAccountPlaceholder": "اختر الحساب الذي تم الدفع منه...", "searchAccountPlaceholder": "ابحث عن حساب...", "noAccountFound": "لم يتم العثور على حساب.", "descriptionEnLabel": "الوصف (إنجليزي)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "vendorLabel": "المورد (اختياري)", "vendorPlaceholder": "مثال: متجر أدوات مكتبية", "referenceLabel": "الرقم المرجعي (اختياري)", "referencePlaceholder": "مثال: فاتورة رقم 123", "saveButton": "ح<PERSON><PERSON> المصروف", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "addSuccess": "تم تسجيل المصروف بنجاح.", "updateSuccess": "تم تحديث المصروف بنجاح.", "addError": "فشل تسجيل المصروف: {error}", "updateError": "فشل تحديث المصروف: {error}", "errorLoadingExpense": "خطأ في تحميل تفاصيل المصروف.", "amountBeforeTaxLabel": "المبلغ (قبل الضريبة)", "amountBeforeTaxDescription": "أد<PERSON>ل المبلغ بدون الضريبة.", "taxLabel": "الضريبة (اختياري)", "taxPlaceholder": "اختر معدل الضريبة...", "taxNoneOption": "بدون ضريبة", "calculatedAmountsTitle": "المبالغ المحسوبة", "netAmountLabel": "المبلغ الصافي", "taxAmountLabel": "مبلغ الضريبة", "totalAmountLabel": "المبلغ الإجمالي"}, "validation": {"expenseDateRequired": "تاريخ المصروف مطلوب.", "expenseDateInvalid": "تاريخ المصروف غير صالح.", "amountRequired": "المبلغ مطلوب.", "amountInvalid": "يجب أن يكون المبلغ رقمًا صالحًا.", "amountPositive": "يجب أن يكون المبلغ موجبًا.", "descriptionEnRequired": "الوصف الإنجليزي مطلوب.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 500 حرف.", "descriptionArRequired": "الوصف العربي مطلوب.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 500 حرف.", "vendorTooLong": "لا يمكن أن يتجاوز اسم المورد 255 حرفًا.", "referenceTooLong": "لا يمكن أن يتجاوز الرقم المرجعي 100 حرف.", "categoryRequired": "فئة المصروف مطلوبة.", "paymentAccountRequired": "حساب الدفع مطلوب.", "taxInvalid": "الضريبة المحددة غير صالحة.", "noChanges": "لم يتم اكتشاف أي تغييرات."}}, "AdminExpenseCategoriesPage": {"title": "فئات المصروفات", "description": "إدارة الفئات المستخدمة لتصنيف المصروفات.", "searchPlaceholder": "ابحث بالاسم...", "addExpenseCategoryButton": "إضافة فئة", "resetFilters": "إعادة تعيين", "errorLoading": "خطأ في تحميل فئات المصروفات: {error}", "noResults": "لم يتم العثور على فئات مصروفات.", "table": {"nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "descriptionEn": "الوصف (إنجليزي)", "descriptionAr": "الوصف (عربي)", "parentCategory": "الفئة الأصل", "actions": "الإجراءات", "actionsOpenMenu": "فتح قائمة الإجراءات", "actionsEdit": "تعديل الفئة", "actionsDelete": "حذ<PERSON> الفئة", "deleteSuccessToast": "تم حذف فئة المصروفات '{name}' بنجاح.", "deleteErrorToast": "فشل حذف فئة المصروفات: {error}", "expenseCategoryItem": "فئة المصروفات", "expenseAccount": "حساب المصروف"}, "AddDialog": {"triggerButton": "إضافة فئة", "title": "إنشاء فئة مصروفات جديدة", "description": "أدخل تفاصيل فئة المصروفات الجديدة.", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Office Supplies", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: مستلزمات مكتبية", "parentCategoryLabel": "الفئة الأصل (اختياري)", "parentCategoryPlaceholder": "اختر فئة أصل", "parentCategoryDescription": "اجمع هذه الفئة تحت فئة أخرى موجودة.", "noParentValue": "--- لا يو<PERSON>د أصل ---", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "expenseAccountLabel": "حساب المصروف", "expenseAccountPlaceholder": "اختر حساب مصروف...", "searchAccountPlaceholder": "ابحث عن حساب...", "noAccountFound": "لم يتم العثور على حساب.", "searchParentPlaceholder": "ابحث عن فئة أصل...", "noParentFound": "لم يتم العثور على فئة أصل.", "saveButton": "إنشاء فئة", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء فئة المصروفات '{name}' بنجاح.", "errorToast": "فشل إنشاء فئة المصروفات: {error}", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 500 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 500 حرف.", "invalidParentCategory": "الفئة الأصل المحددة غير صالحة.", "expenseAccountRequired": "حساب المصروف مطلوب.", "expenseAccountInvalid": "حساب المصرو<PERSON> المحدد غير صالح."}}, "EditDialog": {"title": "تعديل فئة المصروفات", "description": "تحديث تفاصيل الفئة: {name}", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Office Supplies", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: مستلزمات مكتبية", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "expenseAccountLabel": "حساب المصروف", "expenseAccountPlaceholder": "اختر حساب مصروف...", "searchAccountPlaceholder": "ابحث عن حساب...", "noAccountFound": "لم يتم العثور على حساب.", "parentCategoryLabel": "الفئة الأصل (اختياري)", "parentCategoryPlaceholder": "اختر فئة أصل", "searchParentPlaceholder": "ابحث عن فئة أصل...", "noParentFound": "لم يتم العثور على فئة أصل.", "saveButton": "حفظ التغييرات", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "successToast": "تم تحديث فئة المصروفات '{name}' بنجاح.", "errorToast": "فشل تحديث فئة المصروفات: {error}", "errorLoadingCategory": "خطأ في تحميل تفاصيل الفئة: {error}", "noChanges": "لم يتم اكتشاف أي تغييرات.", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 500 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 500 حرف.", "invalidParentCategory": "الفئة الأصل المحددة غير صالحة.", "expenseAccountRequired": "حساب المصروف مطلوب.", "expenseAccountInvalid": "حساب المصرو<PERSON> المحدد غير صالح."}}}, "AdminTaxesPage": {"title": "الضرائب", "description": "إدارة معدلات الضرائب المطبقة داخل النظام.", "searchPlaceholder": "ابحث بالاسم...", "addTaxButton": "إضافة ضريبة", "resetFilters": "إعادة تعيين", "errorLoading": "خطأ في تحميل الضرائب: {error}", "noResults": "لم يتم العثور على ضرائب.", "table": {"nameEn": "الاسم (إنجليزي)", "nameAr": "الا<PERSON><PERSON> (عربي)", "percent": "النسبة المئوية (%)", "descriptionEn": "الوصف (إنجليزي)", "descriptionAr": "الوصف (عربي)", "actions": "الإجراءات", "actionsOpenMenu": "فتح قائمة الإجراءات", "actionsEdit": "تعديل الضريبة", "actionsDelete": "حذ<PERSON> الضريبة", "deleteSuccessToast": "تم حذف الضريبة '{name}' بنجاح.", "deleteErrorToast": "فشل حذف الضريبة: {error}", "taxItem": "الضريبة", "chartOfAccount": "دليل الحسابات"}, "AddDialog": {"triggerButton": "إضافة ضريبة", "title": "إنشاء ضريبة جديدة", "description": "أدخل تفاصيل معدل الضريبة الجديد.", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Value Added Tax", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: ضريبة القيمة المضافة", "percentLabel": "النسبة المئوية", "percentPlaceholder": "مثال: 15.00", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "chartOfAccountLabel": "دليل الحسابات (التزام)", "chartOfAccountPlaceholder": "اختر حساب التزام...", "searchAccountPlaceholder": "ابحث عن حساب...", "noAccountFound": "لم يتم العثور على حساب.", "saveButton": "إنشاء ضريبة", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء الضريبة '{name}' بنجاح.", "errorToast": "فشل إنشاء الضريبة: {error}"}, "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "percentRequired": "النسبة المئوية مطلوبة.", "percentInvalid": "يجب أن تكون النسبة المئوية رقمًا صالحًا.", "percentMin": "يجب أن تكون النسبة المئوية بين 0 و 100.", "percentMax": "يجب أن تكون النسبة المئوية بين 0 و 100.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 500 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 500 حرف.", "chartOfAccountIdRequired": "دليل الحسابات مطلوب."}, "EditDialog": {"title": "تعديل الضريبة", "description": "تحديث تفاصيل الضريبة: {name}", "nameEnLabel": "الاسم (إنجليزي)", "nameEnPlaceholder": "مثال: Value Added Tax", "nameArLabel": "الا<PERSON><PERSON> (عربي)", "nameArPlaceholder": "مثال: ضريبة القيمة المضافة", "percentLabel": "النسبة المئوية", "percentPlaceholder": "مثال: 15.00", "descriptionEnLabel": "الوصف (إنجليزي، اختياري)", "descriptionEnPlaceholder": "أدخل الوصف بالإنجليزي", "descriptionArLabel": "الوصف (عربي، اختياري)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "chartOfAccountLabel": "دليل الحسابات (التزام)", "chartOfAccountPlaceholder": "اختر حساب التزام...", "searchAccountPlaceholder": "ابحث عن حساب...", "noAccountFound": "لم يتم العثور على حساب.", "saveButton": "حفظ التغييرات", "savingButton": "جارٍ الحفظ...", "cancelButton": "إلغاء", "successToast": "تم تحديث الضريبة '{name}' بنجاح.", "errorToast": "فشل تحديث الضريبة: {error}", "noChanges": "لم يتم اكتشاف أي تغييرات.", "validation": {"nameEnRequired": "الاسم الإنجليزي مطلوب.", "nameEnTooLong": "لا يمكن أن يتجاوز الاسم الإنجليزي 100 حرف.", "nameArRequired": "الاسم العربي مطلوب.", "nameArTooLong": "لا يمكن أن يتجاوز الاسم العربي 100 حرف.", "percentRequired": "النسبة المئوية مطلوبة.", "percentInvalid": "يجب أن تكون النسبة المئوية رقمًا صالحًا.", "percentMin": "يجب أن تكون النسبة المئوية بين 0 و 100.", "percentMax": "يجب أن تكون النسبة المئوية بين 0 و 100.", "descriptionEnTooLong": "لا يمكن أن يتجاوز الوصف الإنجليزي 500 حرف.", "descriptionArTooLong": "لا يمكن أن يتجاوز الوصف العربي 500 حرف.", "chartOfAccountIdRequired": "دليل الحسابات مطلوب."}}}, "StudentsPage": {"title": "إدارة الطلاب", "addStudent": "إضافة طالب", "errorTitle": "خطأ في تحميل الطلاب", "unknownError": "حد<PERSON> خطأ غير معروف", "studentsListTitle": "الطلاب", "studentsListDescription": "عرض وإدارة جميع الطلاب في النظام", "noResults": "لم يتم العثور على طلاب", "columns": {"firstName": "الاسم الأول", "lastName": "اسم العائلة", "email": "الب<PERSON>يد الإلكتروني", "admissionNumber": "رقم القبول", "nationalId": "الهوية الوطنية", "idType": "نوع الهوية", "guardians": "أولياء الأمور"}, "idTypes": {"national_id": "الهوية الوطنية", "passport": "جواز السفر"}, "actions": {"openMenu": "فتح القائمة", "actions": "الإجراءات", "viewDetails": "عرض التفاصيل", "edit": "تعديل", "manageGuardians": "إدارة أولياء الأمور"}, "addDialog": {"title": "إضافة طالب جديد", "description": "إنشاء حساب طالب جديد مع جميع المعلومات المطلوبة", "personalInfo": "المعلومات الشخصية", "idInfo": "معلومات الهوية", "schoolInfo": "معلومات المدرسة", "firstName": "الاسم الأول", "lastName": "اسم العائلة", "email": "الب<PERSON>يد الإلكتروني", "password": "كلمة المرور", "phoneNumber": "رقم الهاتف", "gender": "الجنس", "dateOfBirth": "تاريخ الميلاد", "address": "العنوان", "nationalId": "الهوية الوطنية", "idType": "نوع الهوية", "guardian": "ولي الأمر", "guardianType": "صلة القرابة", "educationalStage": "المرحلة التعليمية", "gradeLevel": "المستوى الدراسي", "selectUser": "اختر مستخدم", "selectIdType": "اختر نوع الهوية", "selectStage": "اختر مرحلة", "selectGradeLevel": "اختر مستوى دراسي", "selectGender": "اختر الجنس", "selectDate": "اختر تاريخ", "selectGuardian": "اختر ولي أمر", "selectGuardianType": "اختر نوع صلة القرابة", "guardianOptional": "اختيار ولي الأمر اختياري", "guardianTypes": {"father": "الأب", "mother": "الأم", "brother": "الأخ", "sister": "الأخت", "uncle": "العم", "aunt": "العمة", "grandfather": "الجد", "grandmother": "الجدة", "other": "أ<PERSON><PERSON><PERSON>", "self": "الطالب نفسه (بدون ولي أمر)"}, "genders": {"male": "ذكر", "female": "<PERSON>ن<PERSON>ى"}, "idTypes": {"national_id": "الهوية الوطنية", "passport": "جواز السفر", "unknown": "غير معروف"}, "cancel": "إلغاء", "create": "إنشاء طالب", "creating": "جاري الإنشاء...", "successToast": "تم إنشاء الطالب بنجاح", "errorToast": "فشل في إنشاء الطالب: {error}", "errors": {"firstNameRequired": "الاسم الأول مطلوب", "firstNameTooLong": "الاسم الأول لا يمكن أن يتجاوز 50 حرفًا", "lastNameRequired": "اسم العائلة مطلوب", "lastNameTooLong": "اسم العائلة لا يمكن أن يتجاوز 50 حرفًا", "emailRequired": "البريد الإلكتروني مطلوب", "invalidEmail": "عنوان بريد إلكتروني غير صالح", "emailTooLong": "البريد الإلكتروني لا يمكن أن يتجاوز 100 حرف", "passwordRequired": "كلمة المرور مطلوبة", "passwordTooShort": "كلمة المرور يجب أن تكون على الأقل 8 أحرف", "passwordTooLong": "كلمة المرور لا يمكن أن تتجاوز 100 حرف", "phoneNumberTooLong": "رقم الهاتف لا يمكن أن يتجاوز 20 حرفًا", "dateOfBirthRequired": "تاريخ الميلاد مطلوب", "genderRequired": "الجنس مطلوب", "addressTooLong": "العنوان لا يمكن أن يتجاوز 255 حرفًا", "nationalIdRequired": "الهوية الوطنية مطلوبة", "nationalIdTooLong": "الهوية الوطنية لا يمكن أن تتجاوز 50 حرفًا", "invalidGradeLevel": "الرجاء اختيار مستوى دراسي صالح", "invalidBranch": "الرجاء اختيار فرع صالح", "invalidUserId": "الرجاء اختيار مستخدم صالح", "invalidGuardian": "الرجاء اختيار ولي أمر صالح", "guardianTypeRequired": "الرجاء اختيار نوع صلة القرابة"}}, "toolbar": {"search": "البحث عن طلاب...", "resetFilters": "إعادة تعيين الفلاتر"}}, "StudentDetailPage": {"errorTitle": "خطأ في تحميل تفاصيل الطالب", "unknownError": "حد<PERSON> خطأ غير معروف", "manageGuardians": "إدارة أولياء الأمور", "editStudent": "تعديل الطالب", "personalInfo": "المعلومات الشخصية", "personalInfoDesc": "تفاصيل الطالب الشخصية والتعريفية", "firstName": "الاسم الأول", "lastName": "اسم العائلة", "email": "الب<PERSON>يد الإلكتروني", "phone": "الهاتف", "admissionNumber": "رقم القبول", "nationalId": "الهوية الوطنية", "academicInfo": "المعلومات الأكاديمية", "academicInfoDesc": "تفاصيل التسجيل والتنسيب الأكاديمي للطالب", "branch": "الفرع", "gradeLevel": "المستوى الدراسي", "guardians": "أولياء الأمور", "guardiansDesc": "الأشخاص المسؤولون عن هذا الطالب", "name": "الاسم", "occupation": "المهنة", "notProvided": "<PERSON>ير متوفر", "noGuardians": "لا يوجد أولياء أمور معينين لهذا الطالب", "idTypes": {"national_id": "الهوية الوطنية", "passport": "جواز السفر"}}, "EditStudentPage": {"title": "تعديل الطالب: {name}", "errorTitle": "خطأ في تحميل تفاصيل الطالب", "unknownError": "حد<PERSON> خطأ غير معروف", "formTitle": "تعديل معلومات الطالب", "formDescription": "تحديث معلومات ملف الطالب", "admissionNumber": "رقم القبول", "nationalId": "الهوية الوطنية", "idType": "نوع الهوية", "selectIdType": "اختر نوع الهوية", "idTypes": {"national_id": "الهوية الوطنية", "passport": "جواز السفر"}, "educationalStage": "المرحلة التعليمية", "selectStage": "اختر مرحلة", "gradeLevel": "المستوى الدراسي", "selectGradeLevel": "اختر المستوى الدراسي", "cancel": "إلغاء", "save": "حفظ التغييرات", "saving": "جاري الحفظ...", "successToast": "تم تحديث ملف الطالب بنجاح", "errorToast": "فشل في تحديث ملف الطالب: {error}", "errors": {"admissionNumberRequired": "رقم القبول مطلوب", "nationalIdRequired": "الهوية الوطنية مطلوبة", "invalidGradeLevel": "الرجاء اختيار مستوى دراسي صالح", "invalidBranch": "الرجاء اختيار فرع صالح"}}, "ManageGuardiansPage": {"title": "إدارة أولياء الأمور لـ {name}", "errorTitle": "خطأ في تحميل تفاصيل الطالب", "unknownError": "حد<PERSON> خطأ غير معروف", "addGuardian": "إضافة ولي أمر", "addGuardianTitle": "إضافة ولي أمر للطالب", "addGuardianDescription": "اختر ولي أمر لربطه بهذا الطالب", "selectGuardian": "اختر ولي أمر", "noAvailableGuardians": "لا يوجد أولياء أمور متاحين للإضافة", "cancel": "إلغاء", "link": "ربط ولي الأمر", "linking": "جاري الربط...", "currentGuardiansTitle": "أولياء الأمور الحاليين", "currentGuardiansDescription": "أولياء الأمور المرتبطين حاليًا بهذا الطالب", "name": "الاسم", "email": "الب<PERSON>يد الإلكتروني", "phone": "الهاتف", "occupation": "المهنة", "nationalId": "الهوية الوطنية", "actions": "الإجراءات", "unlink": "إلغاء الربط", "noGuardians": "لا يوجد أولياء أمور معينين لهذا الطالب", "addFirstGuardian": "إضافة أول ولي أمر", "notProvided": "<PERSON>ير متوفر", "linkSuccess": "تم ربط ولي الأمر بنجاح", "linkError": "فشل في ربط ولي الأمر: {error}", "unlinkSuccess": "تم إلغاء ربط ولي الأمر بنجاح", "unlinkError": "فشل في إلغاء ربط ولي الأمر: {error}", "idTypes": {"national_id": "الهوية الوطنية", "passport": "جواز السفر"}}, "AdminUsersPage": {"title": "المستخدمون", "description": "إدارة جميع المستخدمين المسجلين في النظام.", "searchPlaceholder": "ابحث عن المستخدمين بالاسم أو البريد الإلكتروني...", "addUserButton": "إضافة مستخدم", "errorLoading": "خطأ في تحميل المستخدمين: {error}", "noResults": "لم يتم العثور على مستخدمين.", "table": {"firstName": "الاسم الأول", "lastName": "اسم العائلة", "email": "الب<PERSON>يد الإلكتروني", "roles": "الأدوار", "status": "الحالة", "createdAt": "تاريخ الإنشاء", "actions": "الإجراءات", "statusEnabled": "م<PERSON>عل", "statusDisabled": "معطل", "actionsCopyId": "نسخ معرف المستخدم", "actionsViewDetails": "عرض تفاصيل المستخدم", "actionsEdit": "تعديل المستخدم", "actionsDelete": "<PERSON>ذ<PERSON> المستخدم"}, "pagination": {"rowsPerPage": "صفوف لكل صفحة", "selectedCount": "تم تحديد {count} من {total} صف(وف).", "pageInfo": "صفحة {currentPage} من {totalPages}", "goToFirstPage": "ا<PERSON><PERSON><PERSON> إلى الصفحة الأولى", "goToPreviousPage": "اذه<PERSON> إلى الصفحة السابقة", "goToNextPage": "اذه<PERSON> إلى الصفحة التالية", "goToLastPage": "اذه<PERSON> إلى الصفحة الأخيرة"}}, "AdminRolesPage": {"rolesTitle": "الأدوار", "permissionsTitle": "الصلاحيات", "permissionsDescription": "إدارة صلاحيات الدور: {roleName}", "permissionsDescriptionDefault": "اختر دورًا لإدارة صلاحياته.", "saveButton": "حفظ التغييرات", "cancelButton": "إلغاء", "selectRolePrompt": "اختر دورًا من القائمة لعرض وتعديل صلاحياته.", "noRolesFound": "لم يتم العثور على أدوار. أضف دورًا جديدًا للبدء.", "errorLoadingRoles": "خطأ في تحميل الأدوار: {error}", "errorLoadingPermissions": "خطأ في تحميل الصلاحيات المتاحة.", "savePermissionsSuccessToast": "تم تحديث الصلاحيات بنجاح للدور: {roleName}", "savePermissionsErrorToast": "فشل تحديث الصلاحيات: {error}", "AddDialog": {"triggerButton": "إضافة دور", "title": "إنشاء دور جديد", "description": "أدخل تفاصيل الدور الجديد.", "nameLabel": "اسم الدور", "namePlaceholder": "مثال: م<PERSON><PERSON><PERSON> محتوى", "descriptionLabel": "الوصف (اختياري)", "descriptionPlaceholder": "صف الغرض من هذا الدور", "saveButton": "إنشاء الدور", "savingButton": "جارٍ الإنشاء...", "cancelButton": "إلغاء", "successToast": "تم إنشاء الدور '{roleName}' بنجاح.", "errorToast": "فشل إنشاء الدور: {error}"}}, "AdminGuardiansPage": {"title": "إدارة أولياء الأمور", "description": "إدارة ملفات أولياء الأمور وارتباطاتهم بالطلاب.", "table": {"firstName": "الاسم الأول", "lastName": "اسم العائلة", "email": "الب<PERSON>يد الإلكتروني", "phoneNumber": "رقم الهاتف", "occupation": "المهنة", "nationalId": "رقم الهوية", "students": "الطلاب", "createdAt": "تاريخ الإنشاء", "studentsCount": "{count} طالب/طلاب", "noStudents": "لا يو<PERSON>د طلاب", "noResults": "لم يتم العثور على أولياء أمور."}, "toolbar": {"searchPlaceholder": "البحث بواسطة البريد الإلكتروني..."}, "rowActions": {"openMenu": "فتح قائمة الإجراءات", "edit": "تعديل ولي الأمر", "viewDetails": "عرض التفاصيل", "viewStudents": "عر<PERSON> الطلاب", "editSuccess": "تم تحديث ولي الأمر '{name}' بنجاح.", "viewingDetails": "عرض تفاصيل {name}", "viewingStudents": "عرض طلاب {name}", "noStudentsToView": "لا يوجد طلاب مرتبطين بولي الأمر."}, "AddDialog": {"triggerButton": "إضافة ولي أمر (مستخدم موجود)", "title": "إنشاء ملف ولي أمر جديد", "description": "ربط ملف ولي أمر بحساب مستخدم موجود.", "userIdLabel": "حساب المستخدم", "userIdPlaceholder": "اختر مستخدم", "userIdDescription": "حساب المستخدم المراد ربطه بملف ولي الأمر.", "occupationLabel": "المهنة", "occupationPlaceholder": "مثال: مهندس", "addressLabel": "العنوان (اختياري)", "addressPlaceholder": "أدخل عنوان ولي الأمر", "nationalIdLabel": "رقم الهوية", "nationalIdPlaceholder": "أدخل رقم الهوية", "idTypeLabel": "نوع الهوية", "idTypePlaceholder": "اختر نوع الهوية", "idTypeNationalId": "بطاقة هوية وطنية", "idTypePassport": "جواز سفر", "submit": "إنشاء ولي أمر", "submitting": "جاري الإنشاء...", "successToast": "تم إنشاء ملف ولي الأمر '{name}' بنجاح.", "errorToast": "فشل في إنشاء ملف ولي الأمر: {error}", "validation": {"userIdInvalid": "الرجاء اختيار حساب مستخدم صالح.", "occupationRequired": "المهنة مطلوبة.", "occupationTooLong": "لا يمكن أن تتجاوز المهنة 100 حرف.", "addressTooLong": "لا يمكن أن يتجاوز العنوان 255 حرف.", "nationalIdRequired": "رقم الهوية مطلوب.", "nationalIdTooLong": "لا يمكن أن يتجاوز رقم الهوية 50 حرف.", "idTypeRequired": "نوع الهوية مطلوب."}}, "AddWithUserDialog": {"triggerButton": "إضافة ولي أمر (مستخدم جديد)", "title": "إنشاء ولي أمر مع حساب مستخدم جديد", "description": "إنشاء حساب مستخدم جديد وملف ولي أمر في آن واحد.", "firstNameLabel": "الاسم الأول", "firstNamePlaceholder": "مثال: محمد", "lastNameLabel": "اسم العائلة", "lastNamePlaceholder": "مثال: <PERSON><PERSON><PERSON><PERSON>", "emailLabel": "الب<PERSON>يد الإلكتروني", "emailPlaceholder": "مثال: <EMAIL>", "passwordLabel": "كلمة المرور", "passwordPlaceholder": "أد<PERSON>ل كلمة المرور", "passwordDescription": "يجب أن تكون على الأقل 8 أحرف", "phoneNumberLabel": "رقم الهاتف (اختياري)", "phoneNumberPlaceholder": "مثال: +966 50 123 4567", "occupationLabel": "المهنة", "occupationPlaceholder": "مثال: مهندس", "addressLabel": "العنوان (اختياري)", "addressPlaceholder": "أدخل عنوان ولي الأمر", "nationalIdLabel": "رقم الهوية", "nationalIdPlaceholder": "أدخل رقم الهوية", "idTypeLabel": "نوع الهوية", "idTypePlaceholder": "اختر نوع الهوية", "idTypeNationalId": "بطاقة هوية وطنية", "idTypePassport": "جواز سفر", "submit": "إنشاء ولي أمر", "submitting": "جاري الإنشاء...", "successToast": "تم إنشاء ملف ولي الأمر '{name}' بنجاح.", "errorToast": "فشل في إنشاء ملف ولي الأمر: {error}", "validation": {"firstNameRequired": "الاسم الأول مطلوب.", "firstNameTooShort": "يجب أن يكون الاسم الأول على الأقل حرفين.", "firstNameTooLong": "لا يمكن أن يتجاوز الاسم الأول 50 حرف.", "lastNameRequired": "اسم العائلة مطلوب.", "lastNameTooShort": "يجب أن يكون اسم العائلة على الأقل حرفين.", "lastNameTooLong": "لا يمكن أن يتجاوز اسم العائلة 50 حرف.", "emailRequired": "البريد الإلكتروني مطلوب.", "emailInvalid": "الرجاء إدخال عنوان بريد إلكتروني صالح.", "emailTooLong": "لا يمكن أن يتجاوز البريد الإلكتروني 100 حرف.", "passwordRequired": "كلمة المرور مطلوبة.", "passwordTooShort": "يجب أن تكون كلمة المرور على الأقل 8 أحرف.", "passwordTooLong": "لا يمكن أن تتجاوز كلمة المرور 100 حرف.", "phoneNumberTooLong": "لا يمكن أن يتجاوز رقم الهاتف 20 حرف.", "occupationRequired": "المهنة مطلوبة.", "occupationTooLong": "لا يمكن أن تتجاوز المهنة 100 حرف.", "addressTooLong": "لا يمكن أن يتجاوز العنوان 255 حرف.", "nationalIdRequired": "رقم الهوية مطلوب.", "nationalIdTooLong": "لا يمكن أن يتجاوز رقم الهوية 50 حرف.", "idTypeRequired": "نوع الهوية مطلوب."}}, "EditDialog": {"title": "تعديل ملف ولي الأمر", "description": "تحديث تفاصيل ولي الأمر: {name}", "occupationLabel": "المهنة", "occupationPlaceholder": "مثال: مهندس", "addressLabel": "العنوان (اختياري)", "addressPlaceholder": "أدخل عنوان ولي الأمر", "nationalIdLabel": "رقم الهوية", "nationalIdPlaceholder": "أدخل رقم الهوية", "idTypeLabel": "نوع الهوية", "idTypePlaceholder": "اختر نوع الهوية", "idTypeNationalId": "بطاقة هوية وطنية", "idTypePassport": "جواز سفر", "submit": "حفظ التغييرات", "submitting": "جاري الحفظ...", "successToast": "تم تحديث ملف ولي الأمر '{name}' بنجاح.", "errorToast": "فشل في تحديث ملف ولي الأمر: {error}", "validation": {"occupationRequired": "المهنة مطلوبة.", "occupationTooLong": "لا يمكن أن تتجاوز المهنة 100 حرف.", "addressTooLong": "لا يمكن أن يتجاوز العنوان 255 حرف.", "nationalIdRequired": "رقم الهوية مطلوب.", "nationalIdTooLong": "لا يمكن أن يتجاوز رقم الهوية 50 حرف.", "idTypeRequired": "نوع الهوية مطلوب."}}}, "NotFound": {"title": "الصفحة غير موجودة", "message": "عفوًا! الصفحة التي تبحث عنها غير موجودة.", "goBackButton": "العودة إلى الرئيسية", "adminTitle": "منطقة الإدارة - غير موجود", "adminMessage": "تعذر العثور على صفحة الإدارة أو المورد المحدد الذي طلبته.", "adminGoBackButton": "الذهاب إلى لوحة تحكم الإدارة"}, "Home": {"title": "الصفحة الرئيسية"}, "ThemeToggle": {"light": "الوضع الفاتح", "dark": "الوضع المظلم", "system": "الوضع المحلي", "toggleTheme": "تبديل الوضع"}, "Shared": {"statusActive": "نشط", "statusInactive": "غير نشط", "loading": "جار التحميل...", "saving": "جار الحفظ...", "confirmationDialog": {"title": "هل أنت متأكد؟", "deleteMessage": "لا يمكن التراجع عن هذا الإجراء. سيؤدي هذا إلى حذف {item} بشكل دائم.", "cancel": "إلغاء", "delete": "<PERSON><PERSON><PERSON>", "deleting": "جارٍ الحذف...", "activating": "جارٍ التفعيل...", "deactivating": "جارٍ إلغاء التفعيل...", "postTitle": "تأكيد الترحيل", "postMessage": "هل أنت متأكد من رغبتك في ترحيل {item} {id}؟ لا يمكن التراجع عن هذا الإجراء.", "postConfirm": "ترحيل", "posting": "جارٍ الترحيل..."}, "actions": {"posting": "جار الترحيل..."}, "errors": {"genericError": "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى."}, "dataTable": {"view": "<PERSON><PERSON><PERSON>", "toggleColumns": "تبديل الأعمدة", "hide": "إخفاء", "asc": "تصاعدي", "desc": "تنازلي", "sortedAscending": "مرتب تصاعديًا", "sortedDescending": "مرتب تنازليًا", "notSorted": "<PERSON>ير مرتب", "sortAscending": "فرز تصاعدي", "sortDescending": "فرز تنازلي", "hideColumn": "إخفاء العمود", "selectAll": "تحدي<PERSON> الكل", "selectRow": "تحديد الصف", "resetFilters": "إعادة تعيين المرشحات", "pagination": {"rowsPerPage": "صفوف لكل صفحة", "selectedCount": "تم تحديد {count} من {total} صف(وف).", "pageInfo": "صفحة {currentPage} من {totalPages}", "goToFirstPage": "ا<PERSON><PERSON><PERSON> إلى الصفحة الأولى", "goToPreviousPage": "اذه<PERSON> إلى الصفحة السابقة", "goToNextPage": "اذه<PERSON> إلى الصفحة التالية", "goToLastPage": "اذه<PERSON> إلى الصفحة الأخيرة"}, "actions": "الإجراءات", "openMenu": "فتح القائمة", "edit": "تعديل", "delete": "<PERSON><PERSON><PERSON>", "errorLoadingData": "خطأ في تحميل البيانات: {error}", "noResults": "لم يتم العثور على نتائج.", "searchPlaceholder": "بحث..."}, "dateRangePicker": {"placeholder": "اختر نطاق تاريخ"}, "Actions": {"edit": "تعديل", "delete": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "saveChanges": "حفظ التغييرات", "create": "إنشاء", "cancel": "إلغاء", "close": "إغلاق", "saving": "جارٍ الحفظ...", "deleting": "جارٍ الحذف...", "loading": "جارٍ التحميل...", "submit": "إرسال", "submitting": "جارٍ الإرسال...", "refresh": "تحديث"}, "Messages": {"loading": "جارٍ التحميل، يرجى الانتظار...", "error": "حدث خطأ: {errorDetails}", "success": "تمت العملية بنجاح!", "noData": "لا توجد بيانات متاحة.", "notFound": "لم يتم العثور على المورد المطلوب.", "confirm": "هل أنت متأكد؟", "unsavedChanges": "لديك تغييرات غير محفوظة. هل أنت متأكد أنك تريد المغادرة؟", "unexpectedError": "حد<PERSON> خطأ غير متوقع"}, "Table": {"Headers": {"name": "الاسم", "description": "الوصف", "status": "الحالة", "createdAt": "تاريخ الإنشاء", "updatedAt": "تاريخ التحديث", "actions": "الإجراءات"}, "Pagination": {"rowsPerPage": "عدد الصفوف لكل صفحة:", "of": "من", "previous": "السابق", "next": "التالي"}, "Filters": {"filter": "تصفية", "clearFilters": "مس<PERSON> عوا<PERSON>ل التصفية", "searchPlaceholder": "بحث..."}}, "Forms": {"requiredIndicator": "*", "optionalIndicator": "(اختياري)"}}, "GeneralLedger": {"title": "دفتر الأستاذ العام", "description": "عرض المعاملات المالية التفصيلية عبر جميع الحسابات.", "columns": {"date": "التاريخ", "journalEntryId": "معرف قيد اليومية", "accountCode": "<PERSON><PERSON><PERSON> الحساب", "accountName": "اسم الحساب", "description": "الوصف", "debit": "مدين", "credit": "دائن", "runningBalance": "الر<PERSON>يد الجاري"}, "loading": "جارٍ تحميل دفتر الأستاذ العام...", "errorFetching": "خطأ في جلب بيانات دفتر الأستاذ العام.", "filterPlaceholder": "تصفية...", "resetFilters": "إعادة تعيين المرشحات", "selectAccountPlaceholder": "اختر حساب...", "searchAccountPlaceholder": "ابحث عن حساب...", "noAccountFound": "لم يتم العثور على حساب.", "filterByStatusPlaceholder": "تصفية حسب الحالة...", "allStatuses": "كل الحالات", "posted": "مرحّل", "unposted": "غير مرحّل"}, "TrialBalance": {"title": "ميزان المراجعة", "description": "إنشاء وعرض تقرير ميزان المراجعة.", "accountCode": "<PERSON><PERSON><PERSON> الحساب", "accountName": "اسم الحساب", "debitBalance": "رصي<PERSON> مدين", "creditBalance": "رصيد دائن", "totalDebits": "إجمالي المدين", "totalCredits": "إجمالي الدائن", "generateReport": "إنشاء التقرير", "startDate": "تاريخ البدء", "endDate": "تاريخ الانتهاء", "errorFetchingTrialBalance": "خطأ في جلب بيانات ميزان المراجعة.", "noDataFound": "لم يتم العثور على بيانات للنطاق الزمني المحدد.", "selectDatesAndGenerate": "حدد نطاقًا زمنيًا وانقر على 'إنشاء التقرير'."}, "BalanceSheetPage": {"title": "قائمة المركز المالي", "description": "عرض المركز المالي للمدرسة في نقطة زمنية محددة.", "errorLoading": "خطأ في تحميل قائمة المركز المالي", "assets": "الأصول", "liabilities": "الالتزامات", "equityTitle": "حقوق الملكية", "total": "الإجمالي", "totalLiabilitiesAndEquity": "إجمالي الالتزامات وحقوق الملكية", "reportDate": "تاريخ التقرير", "asset": "الأصل", "liability": "الالتزام", "equity": "حقوق الملكية", "currentAssets": "الأصول المتداولة", "nonCurrentAssets": "الأصول غير المتداولة", "currentLiabilities": "الالتزامات المتداولة", "nonCurrentLiabilities": "الالتزامات غير المتداولة"}, "IncomeStatementPage": {"title": "قائمة الدخل", "description": "عرض الأداء المالي للمدرسة خلال فترة زمنية محددة.", "errorLoading": "خطأ في تحميل قائمة الدخل", "revenue": "الإيرادات", "expenses": "المصروفات", "total": "الإجمالي", "netIncome": "صافي الدخل", "reportPeriod": "الفترة المالية", "selectDateRange": "اختر نطاق تاريخ", "generateReport": "إنشاء التقرير", "generating": "جارٍ الإنشاء...", "selectDatesAndGenerate": "حدد نطاقًا زمنيًا وانقر على 'إنشاء التقرير'."}, "ExpenseReportPage": {"title": "تقرير المصروفات", "description": "عرض تقارير المصروفات التفصيلية وملخصات حسب الفئة.", "errorLoading": "خطأ في تحميل تقرير المصروفات: {error}", "reportPeriod": "الفترة المالية", "selectDateRange": "اختر نطاق تاريخ", "generateReport": "إنشاء التقرير", "generating": "جارٍ الإنشاء...", "selectDatesAndGenerate": "حدد نطاقًا زمنيًا وانقر على 'إنشاء التقرير'.", "categorySummary": "ملخص فئات المصروفات", "categorySummaryDescription": "تفصيل المصروفات حسب الفئة", "expenseDetails": "تفاصيل المصروفات", "expenseDetailsDescription": "قائمة مفصلة بجميع المصروفات في الفترة المحددة", "category": "الفئة", "expenseCount": "العدد", "amountBeforeTax": "المبلغ (قبل الضريبة)", "taxAmount": "مبلغ الضريبة", "totalAmount": "المبلغ الإجمالي", "percentage": "النسبة المئوية", "total": "الإجمالي", "date": "التاريخ", "descriptionField": "الوصف", "vendor": "المورد"}, "AiReportsPage": {"title": "تقارير الذكاء الاصطناعي", "description": "إنشاء وعرض التقارير المالية المدعومة بالذكاء الاصطناعي.", "errorFetching": "خطأ في جلب التقارير", "errorLoadingReport": "خطأ في تحميل تفاصيل التقرير", "selectDateRange": "اختر نطاق تاريخ", "selectReportType": "اختر نوع التقرير", "generateReport": "إنشاء التقرير", "generating": "جارٍ الإنشاء...", "resetFilters": "إعادة تعيين", "back": "رجوع", "reportDetail": "تفاصيل التقرير", "reportInfo": "معلومات التقرير", "reportContent": "محتوى التقرير", "chart": "عرض الرسم البياني", "fromCache": "من الذاكرة المؤقتة", "yes": "نعم", "no": "لا", "exportPdf": "تصدير PDF", "exporting": "جاري التصدير...", "exportingPdf": "جاري إعداد ملف PDF...", "exportSuccess": "تم تصدير التقرير بنجاح", "exportError": "فشل تصدير التقرير", "general_ledger": "دفتر الأستاذ العام", "expenses": "المصروفات", "income_statement": "قائمة الدخل", "trial_balance": "ميزان المراجعة", "balance_sheet": "قائمة المركز المالي", "table": {"reportType": "نوع التقرير", "startDate": "تاريخ البداية", "endDate": "تاريخ النهاية", "status": "الحالة", "createdDate": "تاريخ الإنشاء", "view": "<PERSON><PERSON><PERSON>", "completed": "مكتمل", "pending": "قيد الإنشاء", "failed": "فشل", "general_ledger": "دفتر الأستاذ العام", "expenses": "المصروفات", "income_statement": "قائمة الدخل", "trial_balance": "ميزان المراجعة", "balance_sheet": "قائمة المركز المالي"}}, "AdminSectionsPage": {"title": "إدارة الشعب الدراسية", "description": "إدارة الشعب الدراسية بالمدرسة، وتعيينها للمراحل الدراسية والفروع والسنوات الأكاديمية.", "addNewButton": "إضافة شعبة جديدة", "filterByAcademicYear": "تصفية حسب السنة الأكاديمية", "filterByBranch": "تصفية حسب الفرع", "filterByGradeLevel": "تصفية حسب المرحلة الدراسية", "allAcademicYears": "جميع السنوات الأكاديمية", "allBranches": "جميع الفروع", "allGradeLevels": "جميع المراحل الدراسية", "table": {"name": "اسم الشعبة", "capacity": "الطاقة الاستيعابية", "academicYear": "السنة الأكاديمية", "branch": "الفرع", "gradeLevel": "المرحلة الدراسية", "actions": "الإجراءات"}, "form": {"addTitle": "إضافة شعبة جديدة", "editTitle": "تعديل الشعبة", "nameLabel": "اسم الشعبة", "namePlaceholder": "أدخل اسم الشعبة (مثال: شعبة أ، المجموعة الزرقاء)", "capacityLabel": "الطاقة الاستيعابية", "capacityPlaceholder": "<PERSON><PERSON><PERSON><PERSON> الحد الأقصى لعدد الطلاب", "academicYearLabel": "السنة الأكاديمية", "selectAcademicYearPlaceholder": "اختر السنة الأكاديمية", "branchLabel": "الفرع", "selectBranchPlaceholder": "اختر الفرع", "gradeLevelLabel": "المرحلة الدراسية", "selectGradeLevelPlaceholder": "اختر المرحلة الدراسية", "addSuccess": "تم إنشاء الشعبة بنجاح.", "updateSuccess": "تم تحديث الشعبة بنجاح.", "deleteSuccess": "تم حذف الشعبة بنجاح.", "addError": "فشل إنشاء الشعبة.", "updateError": "فشل تحديث الشعبة.", "deleteError": "فشل حذف الشعبة.", "confirmDeleteTitle": "تأ<PERSON>يد حذف الشعبة", "confirmDeleteMessage": "هل أنت متأكد أنك تريد حذف هذه الشعبة؟ لا يمكن التراجع عن هذا الإجراء."}, "validation": {"nameRequired": "اسم الشعبة مطلوب.", "nameTooShort": "يجب أن يتكون اسم الشعبة من حرفين على الأقل.", "nameTooLong": "لا يمكن أن يتجاوز اسم الشعبة 100 حرف.", "capacityRequired": "الطاقة الاستيعابية مطلوبة.", "capacityMustBeNumber": "يجب أن تكون الطاقة الاستيعابية رقمًا.", "capacityMin": "يجب أن تكون الطاقة الاستيعابية 1 على الأقل.", "capacityMax": "لا يمكن أن تتجاوز الطاقة الاستيعابية 999.", "academicYearRequired": "السنة الأكاديمية مطلوبة.", "branchRequired": "الفرع مطلوب.", "gradeLevelRequired": "المرحلة الدراسية مطلوبة."}}, "AdminGradeLevelsPage": {"title": "إدارة المراحل الدراسية", "description": "إدارة المراحل الدراسية ضمن المراحل التعليمية. تحدد المراحل الدراسية التقدم الأكاديمي ضمن كل مرحلة.", "addNewButton": "إضافة مرحلة دراسية جديدة", "filterByStage": "تصفية حسب المرحلة التعليمية", "allStages": "جميع المراحل التعليمية", "table": {"nameEn": "الاسم بالإنجليزية", "nameAr": "الاسم بالعربية", "levelOrder": "الترتيب", "educationalStage": "المرحلة التعليمية", "actions": "الإجراءات"}, "form": {"addTitle": "إضافة مرحلة دراسية جديدة", "editTitle": "تعديل المرحلة الدراسية", "nameEnLabel": "الاسم بالإنجليزية", "nameEnPlaceholder": "أدخل اسم المرحلة الدراسية بالإنجليزية", "nameArLabel": "الاسم بالعربية", "nameArPlaceholder": "أدخل اسم المرحلة الدراسية بالعربية", "levelOrderLabel": "ترتيب المرحلة", "levelOrderPlaceholder": "أدخل رقم الترتيب (مثل: 1، 2، 3)", "educationalStageLabel": "المرحلة التعليمية", "selectEducationalStagePlaceholder": "اختر المرحلة التعليمية", "addSuccess": "تم إنشاء المرحلة الدراسية بنجاح.", "updateSuccess": "تم تحديث المرحلة الدراسية بنجاح.", "deleteSuccess": "تم حذف المرحلة الدراسية بنجاح.", "addError": "فشل إنشاء المرحلة الدراسية.", "updateError": "فشل تحديث المرحلة الدراسية.", "deleteError": "فشل حذف المرحلة الدراسية.", "cancel": "إلغاء", "add": "إضافة مرحلة دراسية", "update": "تحديث المرحلة الدراسية", "adding": "جاري الإضافة...", "updating": "جاري التحديث..."}, "validation": {"nameEnRequired": "الاسم بالإنجليزية مطلوب.", "nameEnMaxLength": "لا يمكن أن يتجاوز الاسم بالإنجليزية 100 حرف.", "nameArRequired": "الاسم بالعربية مطلوب.", "nameArMaxLength": "لا يمكن أن يتجاوز الاسم بالعربية 100 حرف.", "levelOrderRequired": "ترتيب المرحلة مطلوب.", "levelOrderMustBeNumber": "يجب أن يكون ترتيب المرحلة رقمًا.", "levelOrderMin": "يجب أن يكون ترتيب المرحلة 1 على الأقل.", "levelOrderMax": "لا يمكن أن يتجاوز ترتيب المرحلة 999.", "educationalStageRequired": "المرحلة التعليمية مطلوبة."}, "deleteDialog": {"title": "حذ<PERSON> المرحلة الدراسية", "description": "هل أنت متأكد أنك تريد حذف هذه المرحلة الدراسية؟ لا يمكن التراجع عن هذا الإجراء.", "cancel": "إلغاء", "delete": "<PERSON><PERSON><PERSON>", "deleting": "جاري الحذف..."}}}