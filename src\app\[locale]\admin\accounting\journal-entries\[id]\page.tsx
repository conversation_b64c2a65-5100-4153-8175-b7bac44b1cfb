"use client"; // Convert to client component

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Link, useRouter } from "@/i18n/navigation"; // Use i18n Link and useRouter
import { getJournalEntryById, postJournalEntry } from "@/lib/api/admin/accounting/journal-entries";
import { ArrowLeft, Send } from "lucide-react"; // Added Send icon
import { useEffect, useState } from "react"; // Added React imports

import { Badge } from "@/components/ui/badge"; // Import Badge
import { Button } from "@/components/ui/button";
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation'; // For confirmation dialog
import { useAuthStore } from "@/stores/auth"; // Import auth store
import { useTranslations } from "next-intl"; // useTranslations for client component
import { notFound, useParams } from "next/navigation";
import { toast } from "sonner"; // Import toast

export default function ViewJournalEntryPage() {
    const t = useTranslations("AdminJournalEntryViewPage");
    const tStatus = useTranslations("AdminJournalEntriesPage.table");
    const tForm = useTranslations("AdminJournalEntriesPage.form"); // For toast messages
    const tShared = useTranslations("Shared"); // For shared translations like 'confirm'

    const params = useParams();
    const id = params.id as string;
    const router = useRouter();
    const accessToken = useAuthStore((state) => state.accessToken);

    const [journalEntry, setJournalEntry] = useState<Awaited<ReturnType<typeof getJournalEntryById>> | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isPosting, setIsPosting] = useState(false);

    useEffect(() => {
        async function fetchData() {
            setIsLoading(true);
            try {
                const entry = await getJournalEntryById(id, accessToken);
                setJournalEntry(entry);
            } catch (error) {
                console.error("Failed to fetch journal entry:", error);
                toast.error(tShared("errors.genericError"));
                notFound();
            }
            setIsLoading(false);
        }
        if (accessToken) {
            fetchData();
        }
    }, [id, accessToken, tShared]);

    const handlePostEntry = async () => {
        if (!journalEntry || !accessToken) return;
        setIsPosting(true);
        try {
            await postJournalEntry(journalEntry.id, accessToken);
            toast.success(tForm("postSuccess"));
            // Refetch data to update status
            const updatedEntry = await getJournalEntryById(id, accessToken);
            setJournalEntry(updatedEntry);
            router.refresh(); // To re-run server components and update cache if needed
        } catch (error) {
            console.error("Failed to post journal entry:", error);
            toast.error(tForm("postError"));
        }
        setIsPosting(false);
    };

    const { confirm, ConfirmDialog } = useDeleteConfirmation();

    const confirmPost = async () => {
        const confirmed = await confirm({
            title: tForm("confirmPostTitle"),
            message: tForm("confirmPostMessage"),
            confirmText: tStatus("postAction"), // Use existing translation
        });

        if (confirmed) {
            handlePostEntry();
        }
    };

    if (isLoading) {
        // You can return a loading spinner or skeleton here
        return <p>{tShared("loading")}</p>;
    }

    if (!journalEntry) {
        notFound();
    }

    const totalDebits = journalEntry.lines
        .filter(line => line.type === 'DEBIT')
        .reduce((sum, line) => sum + line.amount, 0);
    const totalCredits = journalEntry.lines
        .filter(line => line.type === 'CREDIT')
        .reduce((sum, line) => sum + line.amount, 0);

    const isPosted = !!journalEntry.postedDate;

    return (
        <>
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-start">
                        <div>
                            <CardTitle>{t("title", { id: journalEntry.id.substring(0, 8) })}</CardTitle>
                            <CardDescription>{t("description")}</CardDescription>
                        </div>
                        <div className="flex space-x-2">
                            {!isPosted && (
                                <Button onClick={confirmPost} disabled={isPosting}>
                                    <Send className="mr-2 h-4 w-4" />
                                    {isPosting ? tShared("actions.posting") : tStatus("postAction")}
                                </Button>
                            )}
                            <Link href="/admin/accounting/journal-entries" passHref>
                                <Button variant="outline">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    {t("backButton")}
                                </Button>
                            </Link>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">{t("journalEntryNumberLabel")}</h3>
                                    <p>{journalEntry.journalEntryNumber}</p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">{t("entryDateLabel")}</h3>
                                    <p>{new Date(journalEntry.entryDate).toLocaleDateString()}</p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">{t("referenceLabel")}</h3>
                                    <p>{journalEntry.referenceNumber || 'N/A'}</p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-muted-foreground">{t("statusLabel")}</h3>
                                    <p>
                                        {isPosted ? (
                                            <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 hover:text-green-700">
                                                {tStatus("posted")}
                                            </Badge>
                                        ) : (
                                            <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 hover:text-amber-700">
                                                {tStatus("unposted")}
                                            </Badge>
                                        )}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 className="text-sm font-medium text-muted-foreground">{t("descriptionLabel")}</h3>
                            <p>{journalEntry.description}</p>
                            {isPosted && journalEntry.postedDate && (
                                <p className="text-sm text-muted-foreground mt-2">
                                    {t("postedOn", { date: new Date(journalEntry.postedDate).toLocaleDateString() })}
                                </p>
                            )}
                        </div>
                    </div>

                    <div>
                        <h3 className="text-lg font-medium mb-2">{t("linesTitle")}</h3>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>{t("tableAccount")}</TableHead>
                                        <TableHead>{t("tableLineDescription")}</TableHead>
                                        <TableHead className="text-right">{t("tableDebit")}</TableHead>
                                        <TableHead className="text-right">{t("tableCredit")}</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {journalEntry.lines.map((line, index) => (
                                        <TableRow key={index}>
                                            <TableCell>{line.chartOfAccount?.nameEn || 'Unknown Account'}</TableCell>
                                            <TableCell>{line.description || '-'}</TableCell>
                                            <TableCell className="text-right">
                                                {line.type === 'DEBIT' ? new Intl.NumberFormat('en-SA', { style: 'currency', currency: 'SAR' }).format(line.amount) : '-'}
                                            </TableCell>
                                            <TableCell className="text-right">
                                                {line.type === 'CREDIT' ? new Intl.NumberFormat('en-SA', { style: 'currency', currency: 'SAR' }).format(line.amount) : '-'}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                    <TableRow className="font-medium">
                                        <TableCell colSpan={2} className="text-right">{t("totalDebits")}/{t("totalCredits")}</TableCell>
                                        <TableCell className="text-right">{new Intl.NumberFormat('en-SA', { style: 'currency', currency: 'SAR' }).format(totalDebits)}</TableCell>
                                        <TableCell className="text-right">{new Intl.NumberFormat('en-SA', { style: 'currency', currency: 'SAR' }).format(totalCredits)}</TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                </CardContent>
            </Card>
            <ConfirmDialog />
        </>
    );
}
