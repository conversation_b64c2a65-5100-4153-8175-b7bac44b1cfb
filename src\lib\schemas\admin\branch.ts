import { z } from 'zod';
import { UseTranslations } from 'next-intl';

// Schema for creating a branch
export const createBranchSchema = (t: UseTranslations<string>) => z.object({
    nameEn: z.string()
        .min(1, { message: t('validation.nameEnRequired') })
        .max(150, { message: t('validation.nameEnTooLong') }),
    nameAr: z.string()
        .min(1, { message: t('validation.nameArRequired') })
        .max(150, { message: t('validation.nameArTooLong') }),
    address: z.string()
        .max(255, { message: t('validation.addressTooLong') })
        .optional(),
});

// Schema for updating a branch (all fields optional)
export const updateBranchSchema = (t: UseTranslations<string>) => z.object({
    nameEn: z.string()
        .min(1, { message: t('validation.nameEnRequired') })
        .max(150, { message: t('validation.nameEnTooLong') })
        .optional(),
    nameAr: z.string()
        .min(1, { message: t('validation.nameArRequired') })
        .max(150, { message: t('validation.nameArTooLong') })
        .optional(),
    address: z.string()
        .max(255, { message: t('validation.addressTooLong') })
        .optional()
        .nullable(), // Allow null to clear the address
});

// TypeScript types inferred from schemas
export type CreateBranchInput = z.infer<ReturnType<typeof createBranchSchema>>;
export type UpdateBranchInput = z.infer<ReturnType<typeof updateBranchSchema>>;
