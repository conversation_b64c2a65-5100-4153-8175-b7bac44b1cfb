import { API_ENDPOINTS, DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE } from '@/lib/constants';
import { BranchDto } from '@/lib/dto/admin/branch.dto';
import { Page } from '@/lib/dto/common.dto';

import { fetchWithAuth } from '@/lib/fetch-with-auth';
import { useQuery } from '@tanstack/react-query';

// Parameters for fetching branches
export interface GetBranchesParams {
  page?: number;
  size?: number;
  search?: string;
  sort?: string[];
}

export type PageBranchDto = Page<BranchDto>;

// API Hooks

/**
 * Fetches a paginated list of branches.
 */
export const useGetBranches = (
  params: GetBranchesParams = {}
) => {
  const {
    page = DEFAULT_PAGE_INDEX,
    size = DEFAULT_PAGE_SIZE,
    search,
    sort
  } = params;

  return useQuery<PageBranchDto | BranchDto[], Error>({
    queryKey: [API_ENDPOINTS.ADMIN_BRANCHES, { page, size, search, sort }],
    queryFn: async () => {
      const queryParams = new URLSearchParams();

      if (page !== undefined) queryParams.append('page', page.toString());
      if (size !== undefined) queryParams.append('size', size.toString());
      if (search) queryParams.append('search', search);
      if (sort && sort.length > 0) {
        sort.forEach(s => queryParams.append('sort', s));
      }

      const url = queryParams.toString()
        ? `${API_ENDPOINTS.ADMIN_BRANCHES}?${queryParams.toString()}`
        : API_ENDPOINTS.ADMIN_BRANCHES;

      const response = await fetchWithAuth(url);

      if (!response.ok) {
        throw new Error('Failed to fetch branches');
      }

      return response.json();
    },
  });
};

/**
 * Fetches a single branch by its ID.
 */
export const useGetBranchById = (id: string | undefined, options?: { enabled?: boolean }) => {
  return useQuery<BranchDto, Error>({
    queryKey: [API_ENDPOINTS.ADMIN_BRANCHES, id],
    queryFn: async () => {
      if (!id) throw new Error('Branch ID is required');
      const response = await fetchWithAuth(`${API_ENDPOINTS.ADMIN_BRANCHES}/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch branch');
      }

      return response.json();
    },
    enabled: options?.enabled !== undefined ? options.enabled && !!id : !!id,
  });
};
