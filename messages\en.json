{"LoginPage": {"title": "<PERSON><PERSON>", "description": "Enter your email below to login to your account.", "emailLabel": "Email", "emailPlaceholder": "<EMAIL>", "passwordLabel": "Password", "passwordPlaceholder": "********", "loginButton": "<PERSON><PERSON>", "loggingInButton": "Logging in...", "loginSuccessToast": "Login successful!", "loginErrorToast": "<PERSON><PERSON> failed. Please try again.", "invalidEmailError": "Invalid email address", "passwordRequiredError": "Password is required"}, "LanguageSwitcher": {"switchToEnglish": "Switch to English", "switchToArabic": "Switch to Arabic", "selectLanguageLabel": "Select language"}, "AdminLayout": {"Navbar": {"toggleMenu": "Toggle navigation menu", "searchPlaceholder": "Search...", "toggleSidebar": "Toggle sidebar", "toggleUserMenu": "Toggle user menu", "myAccount": "My Account", "settings": "Settings", "support": "Support", "logout": "Logout", "logoutSuccess": "Logged out successfully"}, "Sidebar": {"panelTitle": "Admin Panel", "dashboard": "Dashboard", "userManagementGroup": "User Management", "users": "Users", "roles": "Roles", "studentsManagementGroup": "Students Management", "students": "Students", "guardians": "Guardians", "schoolManagementGroup": "School Management", "stages": "Stages", "gradeLevels": "Grade Levels", "sections": "Sections", "academicYears": "Academic Years", "holidays": "Holidays", "branches": "Branches", "accountingGroup": "Accounting", "journalEntries": "Journal Entries", "configurations": "Configurations", "chartOfAccounts": "Chart of Accounts", "paymentMethods": "Payment Methods", "feeCategories": "Fee Categories", "fees": "Fees", "expenseCategories": "Expense Categories", "expenses": "Expenses", "taxes": "Taxes", "reportsGroup": "Reports", "generalLedger": "General <PERSON><PERSON>", "trialBalance": "Trial Balance", "balanceSheet": "Balance Sheet", "incomeStatement": "Income Statement", "aiReports": "AI Reports", "settings": "Settings"}, "Footer": {"copyright": "© {year} Maali School. All rights reserved."}}, "AdminDashboard": {"title": "Dashboard", "Cards": {"totalRevenueTitle": "Total Revenue", "totalRevenueValue": "$45,231.89", "totalRevenueDescription": "+20.1% from last month", "subscriptionsTitle": "Subscriptions", "subscriptionsValue": "+2350", "subscriptionsDescription": "+180.1% from last month", "salesTitle": "Sales", "salesValue": "+12,234", "salesDescription": "+19% from last month", "activeNowTitle": "Active Now", "activeNowValue": "+573", "activeNowDescription": "+201 since last hour"}, "overviewTitle": "Overview", "chartPlaceholder": "Chart Placeholder", "recentActivityTitle": "Recent Activity", "recentActivityDescription": "Latest updates and actions.", "activity1Title": "New User Registered", "activity1Description": "<EMAIL>", "activity1Value": "+$0.00", "welcomeTitle": "Welcome Back, <PERSON><PERSON>!", "welcomeDescription": "Here's an overview of the school's status.", "contentPlaceholder": "Dashboard content goes here. You can add charts, stats, and quick links."}, "AdminJournalEntriesPage": {"title": "Journal Entries", "description": "Manage and track financial transactions through detailed journal entries", "table": {"postAction": "Post Entry", "journalEntryNumber": "Entry #", "entryDate": "Entry Date", "referenceNumber": "Reference #", "description": "Description", "totalAmount": "Total Amount", "status": "Status", "posted": "Posted", "unposted": "Unposted", "filterByReference": "Filter by reference...", "filterByDate": "Filter by date range...", "filterByStatus": "Filter by status...", "statusAll": "All Statuses", "statusPosted": "Posted", "statusUnposted": "Unposted", "addJournalEntry": "Add Journal Entry", "journalEntry": "journal entry"}, "form": {"addTitle": "Add New Journal Entry", "editTitle": "Edit Journal Entry", "entryDateLabel": "Entry Date", "referenceLabel": "Reference Number (Optional)", "descriptionLabel": "Description", "linesLabel": "Journal Lines", "accountLabel": "Account", "typeLabel": "Type", "debitLabel": "Debit", "creditLabel": "Credit", "amountLabel": "Amount", "lineDescriptionLabel": "Line Description (Optional)", "addLine": "Add Line", "removeLine": "Remove Line", "selectAccountPlaceholder": "Select an account...", "selectTypePlaceholder": "Select type...", "save": "Save Journal Entry", "saving": "Saving...", "addSuccess": "Journal entry created successfully.", "updateSuccess": "Journal entry updated successfully.", "addError": "Failed to create journal entry.", "updateError": "Failed to update journal entry.", "postSuccess": "Journal entry posted successfully.", "postError": "Failed to post journal entry.", "deleteSuccess": "Journal entry deleted successfully.", "deleteError": "Failed to delete journal entry.", "confirmPostTitle": "Confirm Post Entry", "confirmPostMessage": "Are you sure you want to post this journal entry? This action cannot be undone and will finalize the entry.", "confirmDeleteTitle": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this journal entry? This action cannot be undone.", "totalDebits": "Total Debits", "totalCredits": "Total Credits", "balance": "Balance", "errorLoadingAccounts": "Error loading accounts", "searchAccountPlaceholder": "Search account...", "noAccountFound": "No account found."}, "validation": {"entryDateRequired": "Entry date is required.", "descriptionRequired": "Description is required.", "descriptionTooLong": "Description cannot exceed 500 characters.", "referenceTooLong": "Reference number cannot exceed 100 characters.", "accountIdRequired": "Account is required.", "typeRequired": "Type (Debit/Credit) is required.", "amountPositive": "Amount must be positive.", "lineDescriptionTooLong": "Line description cannot exceed 500 characters.", "atLeastTwoLines": "At least two lines are required.", "debitsMustEqualCredits": "Total debits must equal total credits.", "atLeastOneDebit": "At least one debit line is required.", "atLeastOneCredit": "At least one credit line is required."}}, "AdminJournalEntryViewPage": {"title": "Journal Entry Details", "description": "View the details of the selected journal entry.", "backButton": "Back to List", "journalEntryNumberLabel": "Entry #", "entryDateLabel": "Entry Date", "referenceLabel": "Reference #", "statusLabel": "Status", "descriptionLabel": "Description", "postedOn": "Posted on {date}", "linesTitle": "Journal Lines", "tableAccount": "Account", "tableLineDescription": "Line Description", "tableDebit": "Debit", "tableCredit": "Credit", "totalDebits": "Total Debits", "totalCredits": "Total Credits"}, "AdminChartOfAccountsPage": {"title": "Chart of Accounts", "description": "Manage the financial accounts used by the school.", "searchPlaceholder": "Search by number or name...", "addAccountButton": "Add Account", "showActiveOnly": "Show Active Only", "errorLoading": "Error loading accounts: {error}", "noResults": "No accounts found.", "table": {"accountNumber": "Acc. Number", "nameEn": "Name (EN)", "nameAr": "Name (AR)", "category": "Category", "status": "Status", "parentAccount": "Parent Acc.", "actions": "Actions", "actionsOpenMenu": "Open actions menu", "actionsEdit": "Edit Account", "actionsDelete": "Delete Account", "deleteSuccessToast": "Account '{accountNumber}' deleted successfully.", "deleteErrorToast": "Failed to delete account: {error}", "accountItem": "account"}, "AddDialog": {"triggerButton": "Add Account", "title": "Create New Account", "description": "Enter the details for the new financial account.", "accountNumberLabel": "Account Number", "accountNumberPlaceholder": "e.g., 1010", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Cash on Hand", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., نقدية بالصندوق", "categoryLabel": "Category", "categoryPlaceholder": "Select a category", "parentAccountLabel": "Parent Account (Optional)", "parentAccountPlaceholder": "Select a parent account", "parentAccountDescription": "Link this account under another existing account.", "noParentValue": "--- No Parent ---", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "Create Account", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Account '{accountNumber}' created successfully.", "errorToast": "Failed to create account: {error}", "validation": {"accountNumberRequired": "Account number is required.", "accountNumberTooLong": "Account number cannot exceed 50 characters.", "nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 255 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 255 characters.", "descriptionEnTooLong": "English description cannot exceed 1000 characters.", "descriptionArTooLong": "Arabic description cannot exceed 1000 characters.", "categoryRequired": "Account category is required.", "invalidParentAccount": "Invalid parent account selected."}}, "categories": {"ASSET": "<PERSON><PERSON>", "LIABILITY": "Liability", "EQUITY": "Equity", "REVENUE": "Revenue", "EXPENSE": "Expense"}, "EditDialog": {"title": "Edit Account", "description": "Update the details for account: {accountNumber}", "accountNumberLabel": "Account Number", "accountNumberDescription": "Account number cannot be changed.", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Cash on Hand", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., نقدية بالصندوق", "categoryLabel": "Category", "categoryPlaceholder": "Select a category", "parentAccountLabel": "Parent Account (Optional)", "parentAccountPlaceholder": "Select a parent account", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "activeLabel": "Active", "activeDescription": "Allow transactions for this account.", "saveButton": "Save Changes", "savingButton": "Saving...", "cancelButton": "Cancel", "successToast": "Account '{accountNumber}' updated successfully.", "errorToast": "Failed to update account: {error}", "errorLoadingAccount": "Error loading account details: {error}", "noChanges": "No changes detected.", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 255 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 255 characters.", "descriptionEnTooLong": "English description cannot exceed 1000 characters.", "descriptionArTooLong": "Arabic description cannot exceed 1000 characters.", "categoryRequired": "Account category is required.", "invalidParentAccount": "Invalid parent account selected."}}}, "AdminEducationalStagesPage": {"title": "Educational Stages", "description": "Manage the educational stages within the school (e.g., Kindergarten, Primary).", "addStageButton": "Add Stage", "errorLoadingTitle": "Error Loading Stages", "errorLoading": "Could not load educational stages: {error}", "noResults": "No educational stages found.", "table": {"nameEn": "Name (EN)", "nameAr": "Name (AR)", "sortOrder": "Sort Order", "actions": "Actions", "actionsOpenMenu": "Open actions menu", "actionsEdit": "Edit Stage", "actionsDelete": "Delete Stage", "deleteSuccessToast": "Educational Stage '{name}' deleted successfully.", "deleteErrorToast": "Failed to delete stage: {error}", "educationalStageItem": "educational stage"}, "AddDialog": {"triggerButton": "Add Stage", "title": "Create New Educational Stage", "description": "Enter the details for the new educational stage.", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Primary School", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., المرحلة الابتدائية", "sortOrderLabel": "Sort Order", "sortOrderPlaceholder": "e.g., 1", "saveButton": "Create Stage", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Educational Stage '{name}' created successfully.", "errorToast": "Failed to create stage: {error}", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "sortOrderRequired": "Sort order is required.", "sortOrderInteger": "Sort order must be a whole number.", "sortOrderMin": "Sort order must be at least 1."}}, "EditDialog": {"title": "Edit Educational Stage", "description": "Update the details for stage: {name}", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Primary School", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., المرحلة الابتدائية", "sortOrderLabel": "Sort Order", "sortOrderPlaceholder": "e.g., 1", "saveButton": "Save Changes", "savingButton": "Saving...", "cancelButton": "Cancel", "successToast": "Educational Stage '{name}' updated successfully.", "errorToast": "Failed to update stage: {error}", "noChanges": "No changes detected.", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "sortOrderRequired": "Sort order is required.", "sortOrderInteger": "Sort order must be a whole number.", "sortOrderMin": "Sort order must be at least 1."}}}, "AdminFeeCategoriesPage": {"title": "Fee Categories", "description": "Manage categories used to group school fees.", "searchPlaceholder": "Search by name...", "addFeeCategoryButton": "Add Category", "resetFilters": "Reset", "errorLoading": "Error loading fee categories: {error}", "noResults": "No fee categories found.", "table": {"nameEn": "Name (EN)", "nameAr": "Name (AR)", "descriptionEn": "Description (EN)", "descriptionAr": "Description (AR)", "actions": "Actions", "actionsOpenMenu": "Open actions menu", "actionsEdit": "Edit Category", "actionsDelete": "Delete Category", "deleteSuccessToast": "Fee Category '{name}' deleted successfully.", "deleteErrorToast": "Failed to delete fee category: {error}", "feeCategoryItem": "fee category"}, "AddDialog": {"triggerButton": "Add Category", "title": "Create New Fee Category", "description": "Enter the details for the new fee category.", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., <PERSON><PERSON>", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., رسوم دراسية", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "Create Category", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Fee Category '{name}' created successfully.", "errorToast": "Failed to create fee category: {error}", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "descriptionEnTooLong": "English description cannot exceed 500 characters.", "descriptionArTooLong": "Arabic description cannot exceed 500 characters."}}, "EditDialog": {"title": "Edit Fee Category", "description": "Update the details for fee category: {name}", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., <PERSON><PERSON>", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., رسوم دراسية", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "Save Changes", "savingButton": "Saving...", "cancelButton": "Cancel", "successToast": "Fee Category '{name}' updated successfully.", "errorToast": "Failed to update fee category: {error}", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "descriptionEnTooLong": "English description cannot exceed 500 characters.", "descriptionArTooLong": "Arabic description cannot exceed 500 characters."}}}, "AdminAcademicYearsPage": {"title": "Academic Years", "description": "Manage school academic years and their terms.", "searchPlaceholder": "Search by name...", "addAcademicYearButton": "Add Year", "showActiveOnly": "Show Active Only", "resetFilters": "Reset", "errorLoading": "Error loading academic years: {error}", "noResults": "No academic years found.", "table": {"name": "Name", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "actions": {"openMenu": "Open actions menu", "edit": "Edit Year", "delete": "Delete Year", "activate": "Set Active", "activating": "Activating...", "activateSuccessToast": "Academic Year '{name}' set as active.", "activateErrorToast": "Failed to set academic year active: {error}", "activateConfirmTitle": "Set Active Academic Year?", "activateConfirmMessage": "Are you sure you want to set '{name}' as the active academic year? This might affect other parts of the system.", "academicYearItem": "academic year"}, "actionsOpenMenu": "Open actions menu", "actionsEdit": "Edit Year", "actionsDelete": "Delete Year", "actionsActivate": "Set Active", "deleteSuccessToast": "Academic Year '{name}' deleted successfully.", "deleteErrorToast": "Failed to delete academic year: {error}", "activateSuccessToast": "Academic Year '{name}' set as active.", "activateErrorToast": "Failed to set academic year active: {error}", "activateConfirmTitle": "Set Active Academic Year?", "activateConfirmMessage": "Are you sure you want to set '{name}' as the active academic year? This might affect other parts of the system.", "activating": "Activating...", "academicYearItem": "academic year"}, "AddDialog": {"triggerButton": "Add Year", "title": "Create New Academic Year", "description": "Enter the details for the new academic year.", "nameLabel": "Name", "namePlaceholder": "e.g., 2024-2025", "startDateLabel": "Start Date", "startDatePlaceholder": "Select start date", "endDateLabel": "End Date", "endDatePlaceholder": "Select end date", "saveButton": "Create Year", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Academic Year '{name}' created successfully.", "errorToast": "Failed to create academic year: {error}", "validation": {"nameRequired": "Name is required.", "nameTooLong": "Name cannot exceed 50 characters.", "startDateRequired": "Start date is required.", "startDateInvalidFormat": "Start date must be in YYYY-MM-DD format.", "startDateInvalidDate": "Invalid start date.", "endDateRequired": "End date is required.", "endDateInvalidFormat": "End date must be in YYYY-MM-DD format.", "endDateInvalidDate": "Invalid end date.", "endDateBeforeStartDate": "End date must be after start date."}}, "EditDialog": {"title": "Edit Academic Year", "description": "Update the details for academic year: {name}", "nameLabel": "Name", "namePlaceholder": "e.g., 2024-2025", "startDateLabel": "Start Date", "startDatePlaceholder": "Select start date", "endDateLabel": "End Date", "endDatePlaceholder": "Select end date", "saveButton": "Save Changes", "savingButton": "Saving...", "cancelButton": "Cancel", "successToast": "Academic Year '{name}' updated successfully.", "errorToast": "Failed to update academic year: {error}", "noChanges": "No changes detected.", "validation": {"nameRequired": "Name is required.", "nameTooLong": "Name cannot exceed 50 characters.", "startDateRequired": "Start date is required.", "startDateInvalidFormat": "Start date must be in YYYY-MM-DD format.", "startDateInvalidDate": "Invalid start date.", "endDateRequired": "End date is required.", "endDateInvalidFormat": "End date must be in YYYY-MM-DD format.", "endDateInvalidDate": "Invalid end date.", "endDateBeforeStartDate": "End date must be after start date."}}}, "AdminBranchesPage": {"title": "Branches", "description": "Manage the school's branches.", "addBranchButton": "Add Branch", "errorLoading": "Error loading branches: {error}", "noResults": "No branches found.", "table": {"nameEn": "Name (EN)", "nameAr": "Name (AR)", "address": "Address", "actions": "Actions", "actionsOpenMenu": "Open actions menu", "actionsEdit": "Edit Branch", "actionsDelete": "Delete Branch", "deleteSuccessToast": "Branch '{name}' deleted successfully.", "deleteErrorToast": "Failed to delete branch: {error}", "branchItem": "branch"}, "AddDialog": {"triggerButton": "Add Branch", "title": "Create New Branch", "description": "Enter the details for the new school branch.", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Main Campus", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., الحرم الرئيسي", "addressLabel": "Address (Optional)", "addressPlaceholder": "Enter branch address", "saveButton": "Create Branch", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Branch '{name}' created successfully.", "errorToast": "Failed to create branch: {error}", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 150 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 150 characters.", "addressTooLong": "Address cannot exceed 255 characters."}}, "EditDialog": {"title": "Edit Branch", "description": "Update the details for branch: {name}", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Main Campus", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., الحرم الرئيسي", "addressLabel": "Address (Optional)", "addressPlaceholder": "Enter branch address", "saveButton": "Save Changes", "savingButton": "Saving...", "cancelButton": "Cancel", "successToast": "Branch '{name}' updated successfully.", "errorToast": "Failed to update branch: {error}", "noChanges": "No changes detected.", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 150 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 150 characters.", "addressTooLong": "Address cannot exceed 255 characters."}}}, "AdminPaymentMethodsPage": {"title": "Payment Methods", "description": "Manage the payment methods accepted by the school.", "searchPlaceholder": "Search by name...", "addPaymentMethodButton": "Add Payment Method", "showActiveOnly": "Show Active Only", "resetFilters": "Reset", "errorLoading": "Error loading payment methods: {error}", "noResults": "No payment methods found.", "table": {"nameEn": "Name (EN)", "nameAr": "Name (AR)", "type": "Type", "status": "Status", "actions": "Actions", "actionsOpenMenu": "Open actions menu", "actionsEdit": "Edit Method", "actionsDelete": "Delete Method", "actionsActivate": "Activate Method", "actionsDeactivate": "Deactivate Method", "deleteSuccessToast": "Payment Method '{name}' deleted successfully.", "deleteErrorToast": "Failed to delete payment method: {error}", "activateSuccessToast": "Payment Method '{name}' activated successfully.", "activateErrorToast": "Failed to activate payment method: {error}", "deactivateSuccessToast": "Payment Method '{name}' deactivated successfully.", "deactivateErrorToast": "Failed to deactivate payment method: {error}", "paymentMethodItem": "payment method"}, "AddDialog": {"triggerButton": "Add Method", "title": "Create New Payment Method", "description": "Enter the details for the new payment method.", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Cash", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., نقداً", "typeLabel": "Type", "typePlaceholder": "Select a type", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "Create Method", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Payment Method '{name}' created successfully.", "errorToast": "Failed to create payment method: {error}", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "typeRequired": "Payment method type is required.", "descriptionEnTooLong": "English description cannot exceed 500 characters.", "descriptionArTooLong": "Arabic description cannot exceed 500 characters."}}, "EditDialog": {"title": "Edit Payment Method", "description": "Update the details for payment method: {name}", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Cash", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., نقداً", "typeLabel": "Type", "typePlaceholder": "Select a type", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "activeLabel": "Active", "activeDescription": "Allow this payment method to be used.", "saveButton": "Save Changes", "savingButton": "Saving...", "cancelButton": "Cancel", "successToast": "Payment Method '{name}' updated successfully.", "errorToast": "Failed to update payment method: {error}", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "typeRequired": "Payment method type is required.", "descriptionEnTooLong": "English description cannot exceed 500 characters.", "descriptionArTooLong": "Arabic description cannot exceed 500 characters."}}}, "AdminFeesPage": {"title": "Fees", "description": "Manage school fees and their applicability.", "filterByAcademicYearPlaceholder": "Filter by Academic Year...", "filterByCategoryPlaceholder": "Filter by Category...", "allCategories": "All Categories", "allAcademicYears": "All Academic Years", "addFeeButton": "Add Fee", "resetFilters": "Reset", "errorLoading": "Error loading fees: {error}", "noResults": "No fees found.", "table": {"nameEn": "Name (EN)", "nameAr": "Name (AR)", "amount": "Amount", "academicYear": "Academic Year", "dueDate": "Due Date", "category": "Category", "status": "Status", "actions": "Actions", "actionsOpenMenu": "Open actions menu", "actionsEdit": "<PERSON>", "actionsDelete": "Delete Fee", "actionsActivate": "Activate Fee", "actionsDeactivate": "Deactivate Fee", "deleteSuccessToast": "Fee '{name}' deleted successfully.", "deleteErrorToast": "Failed to delete fee: {error}", "activateSuccessToast": "Fee '{name}' activated successfully.", "activateErrorToast": "Failed to activate fee: {error}", "deactivateSuccessToast": "Fee '{name}' deactivated successfully.", "deactivateErrorToast": "Failed to deactivate fee: {error}", "activateConfirmTitle": "Activate Fee?", "activateConfirmMessage": "Are you sure you want to activate the fee '{name}'?", "deactivateConfirmTitle": "Deactivate Fee?", "deactivateConfirmMessage": "Are you sure you want to deactivate the fee '{name}'? This might prevent it from being assigned.", "feeItem": "fee"}, "AddDialog": {"triggerButton": "Add Fee", "title": "Create New Fee", "description": "Enter the details for the new school fee.", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Term 1 Tuition", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., رسوم الفصل الأول", "amountLabel": "Amount", "amountPlaceholder": "e.g., 1500.00", "academicYearLabel": "Academic Year", "academicYearPlaceholder": "e.g., 2024-2025", "academicYearDescription": "Format: YYYY-YYYY", "dueDateLabel": "Due Date", "dueDatePlaceholder": "Select due date", "categoryLabel": "Fee Category", "categoryPlaceholder": "Select a category", "applicableGradeLabel": "Applicable Grade (Optional)", "applicableGradePlaceholder": "Select grade", "allGradesValue": "All Grades", "applicableStageLabel": "Applicable Stage (Optional)", "applicableStagePlaceholder": "Select stage", "allStagesValue": "All Stages", "applicableBranchLabel": "Applicable Branch (Optional)", "applicableBranchPlaceholder": "Select branch", "allBranchesValue": "All Branches", "applicabilityDescription": "Leave blank to apply to all.", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "saveButton": "Create Fee", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Fee '{name}' created successfully.", "errorToast": "Failed to create fee: {error}", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 255 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 255 characters.", "amountRequired": "Amount is required.", "amountInvalid": "Amount must be a valid number.", "amountMin": "Amount must be zero or positive.", "academicYearRequired": "Academic year is required.", "academicYearInvalidUuid": "Please select a valid academic year.", "dueDateRequired": "Due date is required.", "dueDateInvalidDate": "Invalid due date.", "feeCategoryRequired": "Fee category is required.", "descriptionEnTooLong": "English description cannot exceed 1000 characters.", "descriptionArTooLong": "Arabic description cannot exceed 1000 characters."}}, "EditDialog": {"title": "<PERSON>", "description": "Update the details for fee: {name}", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Term 1 Tuition", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., رسوم الفصل الأول", "amountLabel": "Amount", "amountPlaceholder": "e.g., 1500.00", "academicYearLabel": "Academic Year", "academicYearPlaceholder": "e.g., 2024-2025", "academicYearDescription": "Format: YYYY-YYYY", "dueDateLabel": "Due Date", "dueDatePlaceholder": "Select due date", "categoryLabel": "Fee Category", "categoryPlaceholder": "Select a category", "applicableGradeLabel": "Applicable Grade (Optional)", "applicableGradePlaceholder": "Select grade", "allGradesValue": "All Grades", "applicableStageLabel": "Applicable Stage (Optional)", "applicableStagePlaceholder": "Select stage", "allStagesValue": "All Stages", "applicableBranchLabel": "Applicable Branch (Optional)", "applicableBranchPlaceholder": "Select branch", "allBranchesValue": "All Branches", "applicabilityDescription": "Leave blank to apply to all.", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "activeLabel": "Active", "activeDescription": "Allow this fee to be assigned or used.", "saveButton": "Save Changes", "savingButton": "Saving...", "cancelButton": "Cancel", "successToast": "Fee '{name}' updated successfully.", "errorToast": "Failed to update fee: {error}", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 255 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 255 characters.", "amountRequired": "Amount is required.", "amountInvalid": "Amount must be a valid number.", "amountMin": "Amount must be zero or positive.", "academicYearRequired": "Academic year is required.", "academicYearInvalidUuid": "Please select a valid academic year.", "dueDateRequired": "Due date is required.", "dueDateInvalidDate": "Invalid due date.", "feeCategoryRequired": "Fee category is required.", "descriptionEnTooLong": "English description cannot exceed 1000 characters.", "descriptionArTooLong": "Arabic description cannot exceed 1000 characters."}}}, "AdminExpensesPage": {"title": "Expenses", "description": "Record and manage school expenses.", "filterByDatePlaceholder": "Filter by date range...", "filterByCategoryPlaceholder": "Filter by Category...", "allCategories": "All Categories", "addExpenseButton": "Add Expense", "resetFilters": "Reset", "errorLoading": "Error loading expenses: {error}", "noResults": "No expenses found.", "table": {"expenseDate": "Date", "amount": "Amount", "description": "Description", "category": "Category", "paymentAccount": "<PERSON><PERSON>", "vendor": "<PERSON><PERSON><PERSON>", "referenceNumber": "Reference #", "actions": "Actions", "deleteSuccessToast": "Expense record deleted successfully.", "deleteErrorToast": "Failed to delete expense: {error}", "expenseItem": "expense", "amountBeforeTax": "Amount (Net)", "taxAmount": "Tax Amount", "totalAmount": "Total Amount", "tax": "Tax"}, "form": {"addTitle": "Record New Expense", "editTitle": "Edit Expense", "addDescription": "Enter the details for the new expense.", "editDescription": "Update the details of the expense.", "expenseDateLabel": "Expense Date", "expenseDatePlaceholder": "Select date", "amountLabel": "Amount", "amountPlaceholder": "e.g., 150.50", "categoryLabel": "Expense Category", "categoryPlaceholder": "Select a category", "paymentAccountLabel": "Payment Account", "paymentAccountPlaceholder": "Select account paid from...", "searchAccountPlaceholder": "Search account...", "noAccountFound": "No account found.", "descriptionEnLabel": "Description (English)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "vendorLabel": "<PERSON><PERSON><PERSON> (Optional)", "vendorPlaceholder": "e.g., Stationery Shop", "referenceLabel": "Reference # (Optional)", "referencePlaceholder": "e.g., Invoice #123", "saveButton": "Save Expense", "savingButton": "Saving...", "cancelButton": "Cancel", "addSuccess": "Expense recorded successfully.", "updateSuccess": "Expense updated successfully.", "addError": "Failed to record expense: {error}", "updateError": "Failed to update expense: {error}", "errorLoadingExpense": "Error loading expense details.", "amountBeforeTaxLabel": "Amount (Before Tax)", "amountBeforeTaxDescription": "Enter the amount excluding tax.", "taxLabel": "Tax (Optional)", "taxPlaceholder": "Select a tax rate...", "taxNoneOption": "None", "calculatedAmountsTitle": "Calculated Amounts", "netAmountLabel": "Net Amount", "taxAmountLabel": "Tax Amount", "totalAmountLabel": "Total Amount"}, "validation": {"expenseDateRequired": "Expense date is required.", "expenseDateInvalid": "Invalid expense date.", "amountRequired": "Amount is required.", "amountInvalid": "Amount must be a valid number.", "amountPositive": "Amount must be positive.", "descriptionEnRequired": "English description is required.", "descriptionEnTooLong": "English description cannot exceed 500 characters.", "descriptionArRequired": "Arabic description is required.", "descriptionArTooLong": "Arabic description cannot exceed 500 characters.", "vendorTooLong": "Vendor name cannot exceed 255 characters.", "referenceTooLong": "Reference number cannot exceed 100 characters.", "categoryRequired": "Expense category is required.", "paymentAccountRequired": "Payment account is required.", "taxInvalid": "Invalid tax selected.", "noChanges": "No changes detected."}}, "AdminExpenseCategoriesPage": {"title": "Expense Categories", "description": "Manage categories used for classifying expenses.", "searchPlaceholder": "Search by name...", "addExpenseCategoryButton": "Add Category", "resetFilters": "Reset", "errorLoading": "Error loading expense categories: {error}", "noResults": "No expense categories found.", "table": {"nameEn": "Name (EN)", "nameAr": "Name (AR)", "descriptionEn": "Description (EN)", "descriptionAr": "Description (AR)", "parentCategory": "Parent Category", "actions": "Actions", "actionsOpenMenu": "Open actions menu", "actionsEdit": "Edit Category", "actionsDelete": "Delete Category", "deleteSuccessToast": "Expense Category '{name}' deleted successfully.", "deleteErrorToast": "Failed to delete expense category: {error}", "expenseCategoryItem": "expense category", "expenseAccount": "Expense Account"}, "AddDialog": {"triggerButton": "Add Category", "title": "Create New Expense Category", "description": "Enter the details for the new expense category.", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Office Supplies", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., مستلزمات مكتبية", "parentCategoryLabel": "Parent Category (Optional)", "parentCategoryPlaceholder": "Select a parent category", "parentCategoryDescription": "Group this category under another existing category.", "noParentValue": "--- No Parent ---", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "expenseAccountLabel": "Expense Account", "expenseAccountPlaceholder": "Select an expense account...", "searchAccountPlaceholder": "Search account...", "noAccountFound": "No account found.", "searchParentPlaceholder": "Search parent category...", "noParentFound": "No parent category found.", "saveButton": "Create Category", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Expense Category '{name}' created successfully.", "errorToast": "Failed to create expense category: {error}", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "descriptionEnTooLong": "English description cannot exceed 500 characters.", "descriptionArTooLong": "Arabic description cannot exceed 500 characters.", "invalidParentCategory": "Invalid parent category selected.", "expenseAccountRequired": "Expense account is required.", "expenseAccountInvalid": "Invalid expense account selected."}}, "EditDialog": {"title": "Edit Expense Category", "description": "Update the details for category: {name}", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Office Supplies", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., مستلزمات مكتبية", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "expenseAccountLabel": "Expense Account", "expenseAccountPlaceholder": "Select an expense account...", "searchAccountPlaceholder": "Search account...", "noAccountFound": "No account found.", "parentCategoryLabel": "Parent Category (Optional)", "parentCategoryPlaceholder": "Select a parent category", "searchParentPlaceholder": "Search parent category...", "noParentFound": "No parent category found.", "saveButton": "Save Changes", "savingButton": "Saving...", "cancelButton": "Cancel", "successToast": "Expense Category '{name}' updated successfully.", "errorToast": "Failed to update expense category: {error}", "errorLoadingCategory": "Error loading category details: {error}", "noChanges": "No changes detected.", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "descriptionEnTooLong": "English description cannot exceed 500 characters.", "descriptionArTooLong": "Arabic description cannot exceed 500 characters.", "invalidParentCategory": "Invalid parent category selected.", "expenseAccountRequired": "Expense account is required.", "expenseAccountInvalid": "Invalid expense account selected."}}}, "AdminTaxesPage": {"title": "Taxes", "description": "Manage tax rates applicable within the system.", "searchPlaceholder": "Search by name...", "addTaxButton": "Add Tax", "resetFilters": "Reset", "errorLoading": "Error loading taxes: {error}", "noResults": "No taxes found.", "table": {"nameEn": "Name (EN)", "nameAr": "Name (AR)", "percent": "Percentage (%)", "descriptionEn": "Description (EN)", "descriptionAr": "Description (AR)", "actions": "Actions", "actionsOpenMenu": "Open actions menu", "actionsEdit": "Edit Tax", "actionsDelete": "Delete Tax", "deleteSuccessToast": "Tax '{name}' deleted successfully.", "deleteErrorToast": "Failed to delete tax: {error}", "taxItem": "tax", "chartOfAccount": "Chart of Account"}, "AddDialog": {"triggerButton": "Add Tax", "title": "Create New Tax", "description": "Enter the details for the new tax rate.", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Value Added Tax", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., ضريبة القيمة المضافة", "percentLabel": "Percentage", "percentPlaceholder": "e.g., 15.00", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "chartOfAccountLabel": "Chart of Account (Liability)", "chartOfAccountPlaceholder": "Select a liability account...", "searchAccountPlaceholder": "Search account...", "noAccountFound": "No account found.", "saveButton": "Create Tax", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Tax '{name}' created successfully.", "errorToast": "Failed to create tax: {error}"}, "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "percentRequired": "Percentage is required.", "percentInvalid": "Percentage must be a valid number.", "percentMin": "Percentage must be between 0 and 100.", "percentMax": "Percentage must be between 0 and 100.", "descriptionEnTooLong": "English description cannot exceed 500 characters.", "descriptionArTooLong": "Arabic description cannot exceed 500 characters.", "chartOfAccountIdRequired": "Chart of Account is required."}, "EditDialog": {"title": "Edit Tax", "description": "Update the details for tax: {name}", "nameEnLabel": "Name (English)", "nameEnPlaceholder": "e.g., Value Added Tax", "nameArLabel": "Name (Arabic)", "nameArPlaceholder": "e.g., ضريبة القيمة المضافة", "percentLabel": "Percentage", "percentPlaceholder": "e.g., 15.00", "descriptionEnLabel": "Description (English, Optional)", "descriptionEnPlaceholder": "Enter English description", "descriptionArLabel": "Description (Arabic, Optional)", "descriptionArPlaceholder": "أدخل الوصف بالعربية", "chartOfAccountLabel": "Chart of Account (Liability)", "chartOfAccountPlaceholder": "Select a liability account...", "searchAccountPlaceholder": "Search account...", "noAccountFound": "No account found.", "saveButton": "Save Changes", "savingButton": "Saving...", "cancelButton": "Cancel", "successToast": "Tax '{name}' updated successfully.", "errorToast": "Failed to update tax: {error}", "noChanges": "No changes detected.", "validation": {"nameEnRequired": "English name is required.", "nameEnTooLong": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArTooLong": "Arabic name cannot exceed 100 characters.", "percentRequired": "Percentage is required.", "percentInvalid": "Percentage must be a valid number.", "percentMin": "Percentage must be between 0 and 100.", "percentMax": "Percentage must be between 0 and 100.", "descriptionEnTooLong": "English description cannot exceed 500 characters.", "descriptionArTooLong": "Arabic description cannot exceed 500 characters.", "chartOfAccountIdRequired": "Chart of Account is required."}}}, "AdminUsersPage": {"title": "Users", "description": "Manage all registered users in the system.", "searchPlaceholder": "Search users by name or email...", "addUserButton": "Add User", "errorLoading": "Error loading users: {error}", "noResults": "No users found.", "table": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "roles": "Roles", "status": "Status", "createdAt": "Created At", "actions": "Actions", "statusEnabled": "Enabled", "statusDisabled": "Disabled", "actionsCopyId": "Copy User ID", "actionsViewDetails": "View User Details", "actionsEdit": "Edit User", "actionsDelete": "Delete User"}, "pagination": {"rowsPerPage": "Rows per page", "selectedCount": "{count} of {total} row(s) selected.", "pageInfo": "Page {currentPage} of {totalPages}", "goToFirstPage": "Go to first page", "goToPreviousPage": "Go to previous page", "goToNextPage": "Go to next page", "goToLastPage": "Go to last page"}}, "AdminRolesPage": {"rolesTitle": "Roles", "permissionsTitle": "Permissions", "permissionsDescription": "Manage permissions for role: {roleName}", "permissionsDescriptionDefault": "Select a role to manage its permissions.", "saveButton": "Save Changes", "cancelButton": "Cancel", "selectRolePrompt": "Select a role from the list to view and edit its permissions.", "noRolesFound": "No roles found. Add a new role to get started.", "errorLoadingRoles": "Error loading roles: {error}", "errorLoadingPermissions": "Error loading available permissions.", "savePermissionsSuccessToast": "Permissions updated successfully for role: {roleName}", "savePermissionsErrorToast": "Failed to update permissions: {error}", "AddDialog": {"triggerButton": "Add Role", "title": "Create New Role", "description": "Enter the details for the new role.", "nameLabel": "Role Name", "namePlaceholder": "e.g., Content Editor", "descriptionLabel": "Description (Optional)", "descriptionPlaceholder": "Describe the purpose of this role", "saveButton": "Create Role", "savingButton": "Creating...", "cancelButton": "Cancel", "successToast": "Role '{roleName}' created successfully.", "errorToast": "Failed to create role: {error}"}}, "StudentsPage": {"title": "Student Management", "addStudent": "Add Student", "errorTitle": "Error loading students", "unknownError": "An unknown error occurred", "studentsListTitle": "Students", "studentsListDescription": "View and manage all students in the system", "noResults": "No students found", "columns": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "admissionNumber": "Admission #", "nationalId": "National ID", "idType": "ID Type", "guardians": "Guardians"}, "idTypes": {"national_id": "National ID", "passport": "Passport", "unknown": "Unknown"}, "actions": {"openMenu": "Open menu", "actions": "Actions", "viewDetails": "View Details", "edit": "Edit", "manageGuardians": "Manage Guardians"}, "addDialog": {"title": "Add New Student", "description": "Create a new student account with all required information", "personalInfo": "Personal Information", "idInfo": "ID Information", "schoolInfo": "School Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "phoneNumber": "Phone Number", "gender": "Gender", "dateOfBirth": "Date of Birth", "address": "Address", "nationalId": "National ID", "idType": "ID Type", "guardian": "Guardian", "guardianType": "Guardian Relationship", "educationalStage": "Educational Stage", "gradeLevel": "Grade Level", "selectUser": "Select a user", "selectIdType": "Select ID type", "selectStage": "Select a stage", "selectGradeLevel": "Select a grade level", "selectGender": "Select gender", "selectDate": "Select a date", "selectGuardian": "Select a guardian", "selectGuardianType": "Select relationship type", "guardianOptional": "Guardian selection is optional", "guardianTypes": {"father": "Father", "mother": "Mother", "brother": "Brother", "sister": "Sister", "uncle": "Uncle", "aunt": "Aunt", "grandfather": "Grandfather", "grandmother": "Grandmother", "other": "Other", "self": "Self (No Guardian)"}, "genders": {"male": "Male", "female": "Female"}, "idTypes": {"national_id": "National ID", "passport": "Passport"}, "cancel": "Cancel", "create": "Create Student", "creating": "Creating...", "successToast": "Student created successfully", "errorToast": "Failed to create student: {error}", "errors": {"firstNameRequired": "First name is required", "firstNameTooLong": "First name cannot exceed 50 characters", "lastNameRequired": "Last name is required", "lastNameTooLong": "Last name cannot exceed 50 characters", "emailRequired": "Email is required", "invalidEmail": "Invalid email address", "emailTooLong": "Email cannot exceed 100 characters", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordTooLong": "Password cannot exceed 100 characters", "phoneNumberTooLong": "Phone number cannot exceed 20 characters", "dateOfBirthRequired": "Date of birth is required", "genderRequired": "Gender is required", "addressTooLong": "Address cannot exceed 255 characters", "nationalIdRequired": "National ID is required", "nationalIdTooLong": "National ID cannot exceed 50 characters", "invalidGradeLevel": "Please select a valid grade level", "invalidBranch": "Please select a valid branch", "invalidUserId": "Please select a valid user", "invalidGuardian": "Please select a valid guardian", "guardianTypeRequired": "Please select a guardian relationship type"}}, "toolbar": {"search": "Search students...", "resetFilters": "Reset filters"}}, "StudentDetailPage": {"errorTitle": "Error loading student details", "unknownError": "An unknown error occurred", "manageGuardians": "Manage Guardians", "editStudent": "Edit Student", "personalInfo": "Personal Information", "personalInfoDesc": "Student's personal and identification details", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "admissionNumber": "Admission Number", "nationalId": "National ID", "academicInfo": "Academic Information", "academicInfoDesc": "Student's academic placement and enrollment details", "branch": "Branch", "gradeLevel": "Grade Level", "guardians": "Guardians", "guardiansDesc": "People responsible for this student", "name": "Name", "occupation": "Occupation", "notProvided": "Not provided", "noGuardians": "No guardians assigned to this student", "idTypes": {"national_id": "National ID", "passport": "Passport"}}, "EditStudentPage": {"title": "Edit Student: {name}", "errorTitle": "Error loading student details", "unknownError": "An unknown error occurred", "formTitle": "Edit Student Information", "formDescription": "Update the student's profile information", "admissionNumber": "Admission Number", "nationalId": "National ID", "idType": "ID Type", "selectIdType": "Select ID type", "idTypes": {"national_id": "National ID", "passport": "Passport"}, "educationalStage": "Educational Stage", "selectStage": "Select a stage", "gradeLevel": "Grade Level", "selectGradeLevel": "Select a grade level", "cancel": "Cancel", "save": "Save Changes", "saving": "Saving...", "successToast": "Student profile updated successfully", "errorToast": "Failed to update student profile: {error}", "errors": {"admissionNumberRequired": "Admission number is required", "nationalIdRequired": "National ID is required", "invalidGradeLevel": "Please select a valid grade level", "invalidBranch": "Please select a valid branch"}}, "ManageGuardiansPage": {"title": "Manage Guardians for {name}", "errorTitle": "Error loading student details", "unknownError": "An unknown error occurred", "addGuardian": "Add Guardian", "addGuardianTitle": "Add Guardian to Student", "addGuardianDescription": "Select a guardian to link to this student", "selectGuardian": "Select a guardian", "noAvailableGuardians": "No available guardians to add", "cancel": "Cancel", "link": "Link Guardian", "linking": "Linking...", "currentGuardiansTitle": "Current Guardians", "currentGuardiansDescription": "Guardians currently linked to this student", "name": "Name", "email": "Email", "phone": "Phone", "occupation": "Occupation", "nationalId": "National ID", "actions": "Actions", "unlink": "Unlink", "noGuardians": "No guardians assigned to this student", "addFirstGuardian": "Add First Guardian", "notProvided": "Not provided", "linkSuccess": "Guardian linked successfully", "linkError": "Failed to link guardian: {error}", "unlinkSuccess": "Guardian unlinked successfully", "unlinkError": "Failed to unlink guardian: {error}", "idTypes": {"national_id": "National ID", "passport": "Passport"}}, "AdminGuardiansPage": {"title": "Guardian Management", "description": "Manage guardian profiles and their associations with students.", "table": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phoneNumber": "Phone Number", "occupation": "Occupation", "nationalId": "National ID", "students": "Students", "createdAt": "Created At", "studentsCount": "{count} student(s)", "noStudents": "No students", "noResults": "No guardians found."}, "toolbar": {"searchPlaceholder": "Search by email..."}, "rowActions": {"openMenu": "Open actions menu", "edit": "Edit Guardian", "viewDetails": "View Details", "viewStudents": "View Students", "editSuccess": "Guardian '{name}' updated successfully.", "viewingDetails": "Viewing details for {name}", "viewingStudents": "Viewing students for {name}", "noStudentsToView": "This guardian has no associated students."}, "AddDialog": {"triggerButton": "Add Guardian (Existing User)", "title": "Create New Guardian Profile", "description": "Link a guardian profile to an existing user account.", "userIdLabel": "User Account", "userIdPlaceholder": "Select a user", "userIdDescription": "The user account to associate with this guardian profile.", "occupationLabel": "Occupation", "occupationPlaceholder": "e.g., Engineer", "addressLabel": "Address (Optional)", "addressPlaceholder": "Enter guardian's address", "nationalIdLabel": "National ID", "nationalIdPlaceholder": "Enter ID number", "idTypeLabel": "ID Type", "idTypePlaceholder": "Select ID type", "idTypeNationalId": "National ID", "idTypePassport": "Passport", "submit": "Create Guardian", "submitting": "Creating...", "successToast": "Guardian profile for '{name}' created successfully.", "errorToast": "Failed to create guardian profile: {error}", "validation": {"userIdInvalid": "Please select a valid user account.", "occupationRequired": "Occupation is required.", "occupationTooLong": "Occupation cannot exceed 100 characters.", "addressTooLong": "Address cannot exceed 255 characters.", "nationalIdRequired": "National ID is required.", "nationalIdTooLong": "National ID cannot exceed 50 characters.", "idTypeRequired": "ID type is required."}}, "AddWithUserDialog": {"triggerButton": "Add Guardian (New User)", "title": "Create New Guardian with User Account", "description": "Create a new user account and guardian profile at once.", "firstNameLabel": "First Name", "firstNamePlaceholder": "e.g., <PERSON>", "lastNameLabel": "Last Name", "lastNamePlaceholder": "e.g., <PERSON><PERSON>", "emailLabel": "Email", "emailPlaceholder": "e.g., <EMAIL>", "passwordLabel": "Password", "passwordPlaceholder": "Enter password", "passwordDescription": "Must be at least 8 characters long", "phoneNumberLabel": "Phone Number (Optional)", "phoneNumberPlaceholder": "e.g., +966 50 123 4567", "occupationLabel": "Occupation", "occupationPlaceholder": "e.g., Engineer", "addressLabel": "Address (Optional)", "addressPlaceholder": "Enter guardian's address", "nationalIdLabel": "National ID", "nationalIdPlaceholder": "Enter ID number", "idTypeLabel": "ID Type", "idTypePlaceholder": "Select ID type", "idTypeNationalId": "National ID", "idTypePassport": "Passport", "submit": "Create Guardian", "submitting": "Creating...", "successToast": "Guardian profile for '{name}' created successfully.", "errorToast": "Failed to create guardian profile: {error}", "validation": {"firstNameRequired": "First name is required.", "firstNameTooShort": "First name must be at least 2 characters.", "firstNameTooLong": "First name cannot exceed 50 characters.", "lastNameRequired": "Last name is required.", "lastNameTooShort": "Last name must be at least 2 characters.", "lastNameTooLong": "Last name cannot exceed 50 characters.", "emailRequired": "Email is required.", "emailInvalid": "Please enter a valid email address.", "emailTooLong": "Email cannot exceed 100 characters.", "passwordRequired": "Password is required.", "passwordTooShort": "Password must be at least 8 characters.", "passwordTooLong": "Password cannot exceed 100 characters.", "phoneNumberTooLong": "Phone number cannot exceed 20 characters.", "occupationRequired": "Occupation is required.", "occupationTooLong": "Occupation cannot exceed 100 characters.", "addressTooLong": "Address cannot exceed 255 characters.", "nationalIdRequired": "National ID is required.", "nationalIdTooLong": "National ID cannot exceed 50 characters.", "idTypeRequired": "ID type is required."}}, "EditDialog": {"title": "Edit Guardian Profile", "description": "Update the details for guardian: {name}", "occupationLabel": "Occupation", "occupationPlaceholder": "e.g., Engineer", "addressLabel": "Address (Optional)", "addressPlaceholder": "Enter guardian's address", "nationalIdLabel": "National ID", "nationalIdPlaceholder": "Enter ID number", "idTypeLabel": "ID Type", "idTypePlaceholder": "Select ID type", "idTypeNationalId": "National ID", "idTypePassport": "Passport", "submit": "Save Changes", "submitting": "Saving...", "successToast": "Guardian profile for '{name}' updated successfully.", "errorToast": "Failed to update guardian profile: {error}", "validation": {"occupationRequired": "Occupation is required.", "occupationTooLong": "Occupation cannot exceed 100 characters.", "addressTooLong": "Address cannot exceed 255 characters.", "nationalIdRequired": "National ID is required.", "nationalIdTooLong": "National ID cannot exceed 50 characters.", "idTypeRequired": "ID type is required."}}}, "NotFound": {"title": "Page Not Found", "message": "Oops! The page you are looking for does not exist.", "goBackButton": "Go Back Home", "adminTitle": "Admin Area - Not Found", "adminMessage": "The specific admin page or resource you requested could not be found.", "adminGoBackButton": "Go to Admin Dashboard"}, "Home": {"title": "Nay thing"}, "ThemeToggle": {"light": "Light", "dark": "Dark", "system": "System", "toggleTheme": "Toggle theme"}, "Shared": {"statusActive": "Active", "statusInactive": "Inactive", "loading": "Loading...", "saving": "Saving...", "confirmationDialog": {"title": "Are you sure?", "deleteMessage": "This action cannot be undone. This will permanently delete the {item}.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "activating": "Activating...", "deactivating": "Deactivating...", "postConfirm": "Post", "postTitle": "Confirm Post", "postMessage": "Are you sure you want to post this journal entry? This action cannot be undone.", "posting": "Posting..."}, "actions": {"posting": "Posting..."}, "errors": {"genericError": "An unexpected error occurred. Please try again."}, "dataTable": {"view": "View", "toggleColumns": "Toggle columns", "hide": "<PERSON>de", "asc": "Asc", "desc": "Desc", "sortedAscending": "Sorted ascending", "sortedDescending": "Sorted descending", "notSorted": "Not sorted", "sortAscending": "Sort ascending", "sortDescending": "Sort descending", "hideColumn": "Hide column", "selectAll": "Select all", "selectRow": "Select row", "resetFilters": "Reset filters", "pagination": {"rowsPerPage": "Rows per page", "selectedCount": "{count} of {total} row(s) selected.", "pageInfo": "Page {currentPage} of {totalPages}", "goToFirstPage": "Go to first page", "goToPreviousPage": "Go to previous page", "goToNextPage": "Go to next page", "goToLastPage": "Go to last page"}, "actions": "Actions", "openMenu": "Open menu", "edit": "Edit", "delete": "Delete", "errorLoadingData": "Error loading data: {error}", "noResults": "No results found.", "searchPlaceholder": "Search..."}, "dateRangePicker": {"placeholder": "Pick a date range"}, "Actions": {"edit": "Edit", "delete": "Delete", "save": "Save", "saveChanges": "Save Changes", "create": "Create", "cancel": "Cancel", "close": "Close", "saving": "Saving...", "deleting": "Deleting...", "loading": "Loading...", "submit": "Submit", "submitting": "Submitting...", "refresh": "Refresh"}, "Messages": {"loading": "Loading, please wait...", "error": "An error occurred: {errorDetails}", "success": "Operation successful!", "noData": "No data available.", "notFound": "The requested resource was not found.", "confirm": "Are you sure?", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "unexpectedError": "An unexpected error occurred"}, "Table": {"Headers": {"name": "Name", "description": "Description", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions"}, "Pagination": {"rowsPerPage": "Rows per page:", "of": "of", "previous": "Previous", "next": "Next"}, "Filters": {"filter": "Filter", "clearFilters": "Clear Filters", "searchPlaceholder": "Search..."}}, "Forms": {"requiredIndicator": "*", "optionalIndicator": "(optional)"}}, "GeneralLedger": {"title": "General <PERSON><PERSON>", "description": "View detailed financial transactions across all accounts.", "columns": {"date": "Date", "journalEntryId": "Journal Entry ID", "accountCode": "Account Code", "accountName": "Account Name", "description": "Description", "debit": "Debit", "credit": "Credit", "runningBalance": "Running Balance"}, "loading": "Loading General <PERSON><PERSON>...", "errorFetching": "Error fetching General Ledger data.", "filterPlaceholder": "Filter...", "resetFilters": "Reset Filters", "selectAccountPlaceholder": "Select an account...", "searchAccountPlaceholder": "Search account...", "noAccountFound": "No account found.", "filterByStatusPlaceholder": "Filter by status...", "allStatuses": "All Statuses", "posted": "Posted", "unposted": "Unposted"}, "TrialBalance": {"title": "Trial Balance", "description": "Generate and view the trial balance report.", "accountCode": "Account Code", "accountName": "Account Name", "debitBalance": "Debit Balance", "creditBalance": "Credit Balance", "totalDebits": "Total Debits", "totalCredits": "Total Credits", "generateReport": "Generate Report", "startDate": "Start Date", "endDate": "End Date", "errorFetchingTrialBalance": "Error fetching trial balance data.", "noDataFound": "No data found for the selected date range.", "selectDatesAndGenerate": "Select a date range and click 'Generate Report'."}, "BalanceSheetPage": {"title": "Balance Sheet", "description": "View the financial position of the school at a specific point in time.", "errorLoading": "Error loading balance sheet", "assets": "Assets", "liabilities": "Liabilities", "total": "Total", "totalLiabilitiesAndEquity": "Total Liabilities and Equity", "reportDate": "Report Date", "asset": "<PERSON><PERSON>", "liability": "Liability", "equity": "Equity", "currentAssets": "Current Assets", "nonCurrentAssets": "Non-Current Assets", "currentLiabilities": "Current Liabilities", "nonCurrentLiabilities": "Non-Current Liabilities"}, "IncomeStatementPage": {"title": "Income Statement", "description": "View the financial performance of the school over a specific period.", "errorLoading": "Error loading income statement", "revenue": "Revenue", "expenses": "Expenses", "total": "Total", "netIncome": "Net Income", "reportPeriod": "Report Period", "selectDateRange": "Select date range", "generateReport": "Generate Report", "generating": "Generating...", "selectDatesAndGenerate": "Select a date range and click 'Generate Report'."}, "ExpenseReportPage": {"title": "Expenses Report", "description": "View detailed expense reports and summaries by category.", "errorLoading": "Error loading expense report: {error}", "reportPeriod": "Report Period", "selectDateRange": "Select date range", "generateReport": "Generate Report", "generating": "Generating...", "selectDatesAndGenerate": "Select a date range and click 'Generate Report'.", "categorySummary": "Expense Categories Summary", "categorySummaryDescription": "Breakdown of expenses by category", "expenseDetails": "Expense Details", "expenseDetailsDescription": "Detailed list of all expenses in the selected period", "category": "Category", "expenseCount": "Count", "amountBeforeTax": "Amount (Before Tax)", "taxAmount": "Tax Amount", "totalAmount": "Total Amount", "percentage": "Percentage", "total": "Total", "date": "Date", "descriptionField": "Description", "vendor": "<PERSON><PERSON><PERSON>"}, "AiReportsPage": {"title": "AI Reports", "description": "Generate and view AI-powered financial reports.", "errorFetching": "Error fetching reports", "errorLoadingReport": "Error loading report details", "selectDateRange": "Select date range", "selectReportType": "Select report type", "generateReport": "Generate Report", "generating": "Generating...", "resetFilters": "Reset", "back": "Back", "reportDetail": "Report Details", "reportInfo": "Report Information", "reportContent": "Report Content", "chart": "Chart Visualization", "fromCache": "From Cache", "yes": "Yes", "no": "No", "exportPdf": "Export PDF", "exporting": "Exporting...", "exportingPdf": "Preparing PDF export...", "exportSuccess": "Report exported successfully", "exportError": "Failed to export report", "general_ledger": "General <PERSON><PERSON>", "expenses": "Expenses", "income_statement": "Income Statement", "trial_balance": "Trial Balance", "balance_sheet": "Balance Sheet", "table": {"reportType": "Report Type", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "createdDate": "Created Date", "view": "View", "completed": "Completed", "pending": "Pending", "failed": "Failed", "general_ledger": "General <PERSON><PERSON>", "expenses": "Expenses", "income_statement": "Income Statement", "trial_balance": "Trial Balance", "balance_sheet": "Balance Sheet"}}, "AdminSectionsPage": {"title": "Sections Management", "description": "Manage school sections, assign them to grade levels, branches, and academic years.", "addNewButton": "Add New Section", "filterByAcademicYear": "Filter by Academic Year", "filterByBranch": "Filter by Branch", "filterByGradeLevel": "Filter by Grade Level", "allAcademicYears": "All Academic Years", "allBranches": "All Branches", "allGradeLevels": "All Grade Levels", "table": {"name": "Section Name", "capacity": "Capacity", "academicYear": "Academic Year", "branch": "Branch", "gradeLevel": "Grade Level", "actions": "Actions"}, "form": {"addTitle": "Add New Section", "editTitle": "Edit Section", "nameLabel": "Section Name", "namePlaceholder": "Enter section name (e.g., Section A, Blue Group)", "capacityLabel": "Capacity", "capacityPlaceholder": "Enter maximum number of students", "academicYearLabel": "Academic Year", "selectAcademicYearPlaceholder": "Select academic year", "branchLabel": "Branch", "selectBranchPlaceholder": "Select branch", "gradeLevelLabel": "Grade Level", "selectGradeLevelPlaceholder": "Select grade level", "addSuccess": "Section created successfully.", "updateSuccess": "Section updated successfully.", "deleteSuccess": "Section deleted successfully.", "addError": "Failed to create section.", "updateError": "Failed to update section.", "deleteError": "Failed to delete section.", "confirmDeleteTitle": "Confirm Delete Section", "confirmDeleteMessage": "Are you sure you want to delete this section? This action cannot be undone."}, "validation": {"nameRequired": "Section name is required.", "nameTooShort": "Section name must be at least 2 characters.", "nameTooLong": "Section name cannot exceed 100 characters.", "capacityRequired": "Capacity is required.", "capacityMustBeNumber": "Capacity must be a number.", "capacityMin": "Capacity must be at least 1.", "capacityMax": "Capacity cannot exceed 999.", "academicYearRequired": "Academic year is required.", "branchRequired": "Branch is required.", "gradeLevelRequired": "Grade level is required."}}, "AdminGradeLevelsPage": {"title": "Grade Levels Management", "description": "Manage grade levels within educational stages. Grade levels define the academic progression within each stage.", "addNewButton": "Add New Grade Level", "filterByStage": "Filter by Educational Stage", "allStages": "All Educational Stages", "table": {"nameEn": "English Name", "nameAr": "Arabic Name", "levelOrder": "Order", "educationalStage": "Educational Stage", "actions": "Actions"}, "form": {"addTitle": "Add New Grade Level", "editTitle": "Edit Grade Level", "nameEnLabel": "English Name", "nameEnPlaceholder": "Enter grade level name in English", "nameArLabel": "Arabic Name", "nameArPlaceholder": "Enter grade level name in Arabic", "levelOrderLabel": "Level Order", "levelOrderPlaceholder": "Enter order number (e.g., 1, 2, 3)", "educationalStageLabel": "Educational Stage", "selectEducationalStagePlaceholder": "Select educational stage", "addSuccess": "Grade level created successfully.", "updateSuccess": "Grade level updated successfully.", "deleteSuccess": "Grade level deleted successfully.", "addError": "Failed to create grade level.", "updateError": "Failed to update grade level.", "deleteError": "Failed to delete grade level.", "cancel": "Cancel", "add": "Add Grade Level", "update": "Update Grade Level", "adding": "Adding...", "updating": "Updating..."}, "validation": {"nameEnRequired": "English name is required.", "nameEnMaxLength": "English name cannot exceed 100 characters.", "nameArRequired": "Arabic name is required.", "nameArMaxLength": "Arabic name cannot exceed 100 characters.", "levelOrderRequired": "Level order is required.", "levelOrderMustBeNumber": "Level order must be a number.", "levelOrderMin": "Level order must be at least 1.", "levelOrderMax": "Level order cannot exceed 999.", "educationalStageRequired": "Educational stage is required."}, "deleteDialog": {"title": "Delete Grade Level", "description": "Are you sure you want to delete this grade level? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting..."}}}