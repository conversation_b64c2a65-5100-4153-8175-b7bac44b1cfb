import { API_BASE_URL } from "@/lib/constants";
import { type AssignPermissionsRequest, type CreateRoleRequest, type RoleDto } from "@/lib/dto/admin/role.dto";
import { fetchWithAuth } from "@/lib/fetch-with-auth";

export async function getAllRoles(): Promise<RoleDto[]> {
  const url = `${API_BASE_URL}/admin/roles`;
  try {
    const response = await fetchWithAuth(url);
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData?.message || `Failed to fetch roles: ${response.statusText}`);
    }
    const data: unknown = await response.json();
    // TODO: Add type validation if necessary
    return data as RoleDto[];
  } catch (error) {
    console.error("Error fetching roles:", error);
    throw error instanceof Error ? error : new Error("An unknown error occurred while fetching roles.");
  }
}

export async function getRoleById(id: string): Promise<RoleDto> {
   const url = `${API_BASE_URL}/admin/roles/${id}`;
   try {
     const response = await fetchWithAuth(url);
     if (!response.ok) {
       const errorData = await response.json().catch(() => ({}));
       throw new Error(errorData?.message || `Failed to fetch role ${id}: ${response.statusText}`);
     }
     const data: unknown = await response.json();
     // TODO: Add type validation if necessary
     return data as RoleDto;
   } catch (error) {
     console.error(`Error fetching role ${id}:`, error);
     throw error instanceof Error ? error : new Error(`An unknown error occurred while fetching role ${id}.`);
   }
}


export async function createRole(roleData: CreateRoleRequest): Promise<RoleDto> {
  const url = `${API_BASE_URL}/admin/roles`;
  try {
    const response = await fetchWithAuth(url, {
      method: "POST",
      body: JSON.stringify(roleData),
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData?.message || `Failed to create role: ${response.statusText}`);
    }
    const data: unknown = await response.json();
     // TODO: Add type validation if necessary
    return data as RoleDto;
  } catch (error) {
    console.error("Error creating role:", error);
    throw error instanceof Error ? error : new Error("An unknown error occurred while creating role.");
  }
}

export async function assignPermissionsToRole({ roleId, permissionNames }: { roleId: string; permissionNames: AssignPermissionsRequest }): Promise<RoleDto> {
  const url = `${API_BASE_URL}/admin/roles/${roleId}/permissions`;
  try {
    const response = await fetchWithAuth(url, {
      method: "POST", // API uses POST to replace permissions
      body: JSON.stringify(permissionNames),
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData?.message || `Failed to assign permissions to role ${roleId}: ${response.statusText}`);
    }
    const data: unknown = await response.json();
     // TODO: Add type validation if necessary
    return data as RoleDto;
  } catch (error) {
    console.error(`Error assigning permissions to role ${roleId}:`, error);
    throw error instanceof Error ? error : new Error(`An unknown error occurred while assigning permissions to role ${roleId}.`);
  }
}

// Add updateRole and deleteRole functions here if needed later
