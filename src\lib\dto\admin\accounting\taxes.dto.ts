import { Page } from "@/lib/dto/page.dto";
import { SimpleChartOfAccountDto } from "@/lib/dto/admin/accounting/chart-of-accounts.dto"; // Import SimpleChartOfAccountDto

/**
 * Data Transfer Object for representing a Tax.
 */
export interface TaxDto {
    id: string;
    nameAr: string;
    nameEn: string;
    descriptionAr?: string;
    descriptionEn?: string;
    percent: number;
    chartOfAccount: SimpleChartOfAccountDto; // Add the linked chart of account object
    // Consider adding createdAt, updatedAt if available and needed
}

/**
 * Request body for creating a new Tax.
 */
export interface CreateTaxRequest {
    nameAr: string;
    nameEn: string;
    descriptionAr?: string;
    descriptionEn?: string;
    percent: number;
    chartOfAccountId: string; // Add required chartOfAccountId
}

/**
 * Request body for updating an existing Tax.
 * All fields are optional for partial updates.
 */
export interface UpdateTaxRequest {
    nameAr?: string;
    nameEn?: string;
    descriptionAr?: string;
    descriptionEn?: string;
    percent?: number;
    chartOfAccountId?: string; // Add optional chartOfAccountId (assuming PUT allows changing it)
}

/**
 * Represents a paginated response for TaxDto.
 */
export type PageTaxDto = Page<TaxDto>;

/**
 * Parameters for fetching taxes, supporting pagination and filtering.
 */
export interface GetTaxesParams {
    page?: number; // Zero-based page index
    size?: number;
    sort?: string[]; // e.g., ["nameEn,asc", "percent,desc"]
    name?: string; // Filter by name (assuming API supports it)
    // Add other potential filter params if API supports them
}
