import { Page } from "@/lib/dto/common.dto"; // Use common Page DTO
import { SimpleChartOfAccountDto } from "./chart-of-accounts.dto";
import { SimpleExpenseCategoryDto } from "./expense-categories.dto";
import { SimpleTaxDto } from "./taxes.dto"; // Import SimpleTaxDto

// Based on ExpenseDto in api-docs.json
export interface ExpenseDto {
    id: string;
    expenseDate: string; // ISO Date string "YYYY-MM-DD"
    amountBeforeTax: number; // New field
    taxAmount: number; // New field
    totalAmount: number; // New field (replaces old 'amount')
    descriptionEn: string;
    descriptionAr: string;
    vendor?: string | null;
    referenceNumber?: string | null;
    category: SimpleExpenseCategoryDto;
    tax?: SimpleTaxDto | null; // Optional Tax relationship
    paymentAccount: SimpleChartOfAccountDto; // Assuming this is the account used for payment
    createdDate: string; // ISO DateTime string
    lastModifiedDate: string; // ISO DateTime string
}

// Based on CreateExpenseRequest in api-docs.json
export interface CreateExpenseRequest {
    expenseDate: string; // "YYYY-MM-DD"
    amount: number;
    descriptionEn: string;
    descriptionAr: string;
    vendor?: string | null;
    referenceNumber?: string | null;
    categoryId: string; // UUID
    paymentAccountId: string; // UUID of the ChartOfAccount used for payment
    taxId?: string | null; // Optional UUID
}

// Based on UpdateExpenseRequest in api-docs.json
export interface UpdateExpenseRequest {
    expenseDate?: string; // "YYYY-MM-DD"
    amount?: number;
    descriptionEn?: string;
    descriptionAr?: string;
    vendor?: string | null;
    referenceNumber?: string | null;
    categoryId?: string; // UUID
    paymentAccountId?: string; // UUID
    taxId?: string | null; // Optional UUID
}

// Based on findExpenses parameters in api-docs.json
export interface GetExpensesParams {
    page?: number;
    size?: number;
    sort?: string[];
    startDate?: string; // "YYYY-MM-DD"
    endDate?: string; // "YYYY-MM-DD"
    categoryId?: string; // UUID
    // Add other potential filters if needed (e.g., vendor, referenceNumber)
}

// Define the paginated response DTO
export type PageExpenseDto = Page<ExpenseDto>;
