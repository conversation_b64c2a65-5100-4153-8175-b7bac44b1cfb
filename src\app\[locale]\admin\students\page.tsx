"use client";

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { getStudents } from '@/lib/api/admin/students';
import { DataTable } from './data-table';
import { columns } from './columns';
import { AddStudentDialog } from './add-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';

export default function StudentsPage() {
  const t = useTranslations('StudentsPage');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  // Initial fetch of students data
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['students-initial'],
    queryFn: () => getStudents({ page: 0, size: 10 }),
  });

  const handleStudentAdded = () => {
    refetch();
  };

  return (
    <div className="container mx-auto py-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{t('title')}</h1>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          {t('addStudent')}
        </Button>
      </div>

      {isLoading ? (
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      ) : isError ? (
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertTitle>{t('errorTitle')}</AlertTitle>
          <AlertDescription>
            {error instanceof Error ? error.message : t('unknownError')}
          </AlertDescription>
        </Alert>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>{t('studentsListTitle')}</CardTitle>
            <CardDescription>{t('studentsListDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable data={data} columns={columns} />
          </CardContent>
        </Card>
      )}

      <AddStudentDialog 
        isOpen={isAddDialogOpen} 
        setIsOpen={setIsAddDialogOpen} 
        onStudentAdded={handleStudentAdded} 
      />
    </div>
  );
}
