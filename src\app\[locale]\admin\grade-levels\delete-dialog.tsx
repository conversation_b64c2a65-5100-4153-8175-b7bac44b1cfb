"use client";

import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { deleteGradeLevel } from "@/lib/api/admin/grade-levels";
import { GradeLevelDto } from "@/lib/dto/admin/grade-level.dto";
import { getErrorMessage } from "@/lib/utils";

interface DeleteGradeLevelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  gradeLevel: GradeLevelDto | null;
}

export function DeleteGradeLevelDialog({
  open,
  onOpenChange,
  onSuccess,
  gradeLevel,
}: DeleteGradeLevelDialogProps) {
  const t = useTranslations('AdminGradeLevelsPage.deleteDialog');
  const tForm = useTranslations('AdminGradeLevelsPage.form');

  const deleteMutation = useMutation({
    mutationFn: deleteGradeLevel,
    onSuccess: () => {
      toast.success(tForm('deleteSuccess'));
      onSuccess();
      onOpenChange(false);
    },
    onError: (error) => {
      const errorMessage = getErrorMessage(error);
      toast.error(tForm('deleteError') + ': ' + errorMessage);
    },
  });

  const handleDelete = () => {
    if (!gradeLevel) return;
    deleteMutation.mutate(gradeLevel.id);
  };

  if (!gradeLevel) return null;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('title')}</AlertDialogTitle>
          <AlertDialogDescription>
            {t('description')}
            <br />
            <br />
            <strong>Grade Level:</strong> {gradeLevel.nameEn} / {gradeLevel.nameAr}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteMutation.isPending}>
            {t('cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {deleteMutation.isPending ? t('deleting') : t('delete')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
