import { LoginSchema } from '@/lib/schemas/auth';
import { AuthResponse } from '@/stores/auth'; // Assuming AuthResponse is defined in the store

// TODO: Replace with actual API base URL, potentially from environment variables
const API_BASE_URL = 'http://localhost:8081/api/v1';

export async function loginUser(credentials: LoginSchema): Promise<AuthResponse> {
  const response = await fetch(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(credentials),
  });

  if (!response.ok) {
    // Attempt to parse error response from API
    const errorData = await response.json().catch(() => ({ message: 'Login failed' }));
    // Use the message from the API error response, or a default
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  const data: AuthResponse = await response.json();
  return data;
}

// TODO: Add functions for register, forgotPassword, resetPassword, activateAccount as needed
