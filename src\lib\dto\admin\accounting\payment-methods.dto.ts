import { PageableObject, SortObject } from "@/lib/dto/common.dto";

export type PaymentMethodType =
    | "CASH"
    | "BANK_TRANSFER"
    | "CHEQUE"
    | "CREDIT_CARD"
    | "DEBIT_CARD"
    | "ONLINE_PAYMENT_GATEWAY"
    | "MOBILE_MONEY";

export interface PaymentMethodDto {
    id: string;
    nameEn: string;
    nameAr: string;
    type: PaymentMethodType;
    descriptionEn?: string | null; // Make optional based on API docs (not required in response)
    descriptionAr?: string | null; // Make optional
    active: boolean;
    createdDate: string; // ISO date string
    lastModifiedDate: string; // ISO date string
}

export interface SimplePaymentMethodDto {
    id: string;
    nameEn: string;
    nameAr: string;
    type: PaymentMethodType;
}

export interface PagePaymentMethodDto {
    content: PaymentMethodDto[];
    pageable: PageableObject;
    last: boolean;
    totalElements: number;
    totalPages: number;
    size: number;
    number: number;
    sort: SortObject[];
    first: boolean;
    numberOfElements: number;
    empty: boolean;
}
