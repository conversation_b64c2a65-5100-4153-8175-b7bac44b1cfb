Project Context
Project Name: Maali School Next.js
Project Description: A comprehensive school management application that handles school information, student details, accounting, bus management, and learning operations. The frontend interfaces with existing Spring Boot APIs to provide a complete administrative solution for educational institutions.
Key Technologies & Libraries:
* UI Components & Styling: Shadcn UI built on Tailwind CSS and Radix UI primitives
* Client State Management: Zustand for persistent UI state and auth token storage
* Form Handling & Validation: React Hook Form with Zod schema validation
* API Interaction & Server State: TanStack Query for data fetching, caching, and state management
* Authentication: Custom token management integrated with Zustand stores
* Internationalization: next-intl for multi-language support
* Table Logic: TanStack Table for complex data grids and lists
* Notifications: Sonner for toast notifications and alerts
Design Considerations:
* Brand Colors: Primary palette includes #B14E00 (rust), #D6AA48 (gold), #1C5954 (teal), and #C2C1B1 (stone)
* Code Architecture: Follow Next.js App Router conventions with domain-driven folder structure
* Rendering Strategy: Leverage React Server Components where appropriate, with client components for interactive elements
Instructions
When working on this project, please follow these guidelines:
1. Use the established tech stack exclusively - Create components using Shadcn UI, manage forms with React Hook Form + Zod, and handle API requests with TanStack Query.
2. Follow the application architecture - Place new components in appropriate folders based on their domain and functionality. Separate UI components from data-fetching logic.
3. Apply appropriate rendering strategies - Use Server Components for static content and SEO-critical pages. Use Client Components for interactive elements requiring hooks or browser APIs.
4. Implement responsive designs - Ensure all UI components work across mobile, tablet, and desktop viewports using Tailwind's responsive classes.
5. Maintain consistent styling - Incorporate the brand color palette (#B14E00, #D6AA48, #1C5954, #C2C1B1) throughout the UI to maintain visual coherence.
6. Optimize for performance - Implement proper data fetching patterns with TanStack Query, including prefetching, caching, and pagination where appropriate.
7. Handle authentication properly - Work with the existing token management system, ensuring protected routes and API calls include proper authorization.
8. Support internationalization - Use next-intl for all user-facing text to support multiple languages.
9. Implement proper form validation - Create robust Zod schemas that validate all form inputs and provide helpful error messages.
10. Write clean, maintainable code - Include appropriate TypeScript types, meaningful variable names, and concise comments explaining complex logic.
project currency SAR (Saudi Riyal)
When implementing new features, focus on delivering functional code that integrates seamlessly with the existing application structure and these libraries rather than explaining basic Next.js concepts.
keep UI and code design consistent with the existing design.
when create / refactor components, ensure to update/add needed translations in ar.json and en.json files.
you have a green card to add any files to chat, New Pages: Mirror the structure and functionality of the Fees page, using  context.md as references.
Prioritize delivering functional, secure, and high-quality code that integrates seamlessly with the existing application. Avoid lengthy explanations of basic Next.js concepts—focus on implementation.
use  context.md as context for the upcoming requests.
don't add comments to json files.
Focus please on every request and provide the best solution, you should understand the project structure very well and go on solving problems and tasks step by step.