import { z } from "zod";
import { UseTranslations } from "next-intl";

// Helper function for date validation messages
const dateRequiredError = (t: UseTranslations<string>, fieldName: string) => ({
    required_error: t(`${fieldName}Required`),
    invalid_type_error: t(`${fieldName}InvalidDate`),
});

// Base schema for common fields using z.date()
const baseAcademicYearSchema = (t: UseTranslations<string>) => ({
    name: z.string()
        .min(1, { message: t("nameRequired") })
        .max(50, { message: t("nameTooLong") }),
    startDate: z.date(dateRequiredError(t, "startDate")),
    endDate: z.date(dateRequiredError(t, "endDate")),
});

// Schema for creating an academic year
export const createAcademicYearSchema = (t: UseTranslations<string>) => z.object({
    ...baseAcademicYearSchema(t),
}).refine(data => data.endDate > data.startDate, {
    message: t("endDateBeforeStartDate"),
    path: ["endDate"], // Attach error to endDate field
});

// Schema for updating an academic year (all fields optional)
// Note: For optional dates, refine might need adjustment if partial updates are complex
export const updateAcademicYearSchema = (t: UseTranslations<string>) => z.object({
    name: z.string()
        .min(1, { message: t("nameRequired") })
        .max(50, { message: t("nameTooLong") })
        .optional(),
    startDate: z.date(dateRequiredError(t, "startDate")).optional(),
    endDate: z.date(dateRequiredError(t, "endDate")).optional(),
}).refine(data => {
    // Ensure end date is after start date if both are provided and valid
    if (data.startDate && data.endDate) {
        return data.endDate > data.startDate;
    }
    return true; // Validation passes if only one or neither date is provided/changed
}, {
    message: t("endDateBeforeStartDate"),
    path: ["endDate"],
});


// Infer TypeScript types from schemas
export type CreateAcademicYearInput = z.infer<ReturnType<typeof createAcademicYearSchema>>;
export type UpdateAcademicYearInput = z.infer<ReturnType<typeof updateAcademicYearSchema>>;
