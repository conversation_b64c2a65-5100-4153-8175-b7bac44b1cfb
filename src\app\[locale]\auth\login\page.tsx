import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import Image from "next/image"; // Import next/image
import { LanguageSwitcher } from "@/components/common/language-switcher"; // Import the switcher
import { LoginForm } from "@/components/auth/login-form";
import { ThemeToggleButton } from "@/components/common/theme-toggle-button"; // Import the theme toggle button
import { useTranslations } from "next-intl";

export default function LoginPage() {
  const t = useTranslations('LoginPage');

  return (
    // Use flex-col to stack logo, card, and switcher vertically
    <div className="flex min-h-screen flex-col items-center justify-center gap-6 bg-muted/40 p-4">
      {/* Logo */}
      <Image
        src="/logo.png" // Placeholder logo - replace with your actual logo path e.g., /logo.svg
        alt="Maali School Logo" // Add descriptive alt text
        width={240} // Adjust width as needed
        height={80} // Adjust height as needed
        priority // Add priority if logo is above the fold
      />

      {/* Login Card - Increased width */}
      <Card className="w-full max-w-md"> {/* Changed max-w-sm to max-w-md */}
        <CardHeader className="items-center text-center"> {/* Center header items */}
          {/* Optional: Move switcher inside header */}
          {/* <div className="absolute top-4 right-4"> <LanguageSwitcher /> </div> */}
          <CardTitle className="text-2xl">{t('title')}</CardTitle>
          <CardDescription>{t('description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <LoginForm />
        </CardContent>
      </Card>

      {/* Language and Theme Switchers below the card */}
      <div className="flex items-center gap-2">
        <LanguageSwitcher />
        <ThemeToggleButton />
      </div>
    </div>
  );
}
