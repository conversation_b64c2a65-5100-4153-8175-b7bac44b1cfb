import './globals.css';

import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "next/font/google";
import {NextIntlClientProvider, hasLocale} from "next-intl"; // Import the client provider

import type {Metadata} from "next";
import {Providers} from "@/components/providers"; // Import the Providers component
import {ThemeProvider} from "@/components/theme-provider"; // Import the ThemeProvider
import {cn} from "@/lib/utils"; // Import cn utility
import {getLangDir} from "rtl-detect";
import {getMessages} from 'next-intl/server';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';

const geistSans = Geist({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Geist_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

const tajawal = Tajawal({
    subsets: ['arabic'],
    weight: ['400', '700', '900'], // Choose desired weights
  });
export const metadata: Metadata = {
    title: "Maali Schools",
    description: "Maali Schools is a platform for learning and teaching",
};

export default async function RootLayout({
                                             children,
                                             params,
                                         }: Readonly<{
    children: React.ReactNode;
    params: Promise<{ locale: string }>;
}>) {
    // const p = await params;
    // const locale = p.locale;
    const {locale} = await params;
    if (!hasLocale(routing.locales, locale)) {
        notFound();
    }

    const messages = await getMessages(); // Fetch messages for the current locale

    const direction = getLangDir(locale);
    return (
        // <NextIntlClientProvider  messages={ messages} locale={locale}>
        <html lang={locale} dir={direction}>
        <body suppressHydrationWarning={true}
              className={cn(
                  'min-h-screen bg-background font-sans antialiased',
                  tajawal.className,
                  geistSans.variable,
                  geistMono.variable,
              )}
        >
        <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
        >
            <NextIntlClientProvider messages={messages} locale={locale}>
                <Providers>{children}</Providers>
            </NextIntlClientProvider>
        </ThemeProvider>
        </body>
        </html>
        // </NextIntlClientProvider>
    );
}
