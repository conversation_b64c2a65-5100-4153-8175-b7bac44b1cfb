"use client";

import * as React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, MoreHorizontal, Pen, Trash } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TaxDto } from "@/lib/dto/admin/accounting/taxes.dto";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header"; // Reusable header component
import { useDeleteConfirmation } from "@/hooks/use-delete-confirmation"; // Reusable hook for delete confirmation
import { deleteTax } from "@/lib/api/admin/accounting/taxes"; // API function
import { toast } from "sonner"; // For notifications

// Update props to accept values from hooks called in the parent component
interface ColumnsProps {
    locale: string;
    tTable: ReturnType<typeof useTranslations<'AdminTaxesPage.table'>>;
    tShared: ReturnType<typeof useTranslations<'Shared'>>;
    tSharedDataTable: ReturnType<typeof useTranslations<'Shared.dataTable'>>;
    confirm: ReturnType<typeof useDeleteConfirmation>['confirm']; // Pass confirm function type
    onTaxUpdated?: (updatedTax: TaxDto) => void;
    onTaxDeleted?: (taxId: string) => void;
    openEditDialog: (tax: TaxDto) => void;
}

// getColumns is now a pure function receiving props, not calling hooks directly
export const getColumns = ({
    locale,
    tTable,
    tShared,
    tSharedDataTable,
    confirm, // Receive confirm function
    onTaxDeleted,
    openEditDialog,
}: ColumnsProps): ColumnDef<TaxDto>[] => {
    // Hooks are no longer called here
    // const t = useTranslations("AdminTaxesPage.table"); // Removed
    // const tShared = useTranslations("Shared"); // Removed
    // const tSharedDataTable = useTranslations("Shared.dataTable"); // Removed
    // const locale = useLocale(); // Removed
    // const { confirm, ConfirmDialog } = useDeleteConfirmation(); // Removed

    const handleDelete = async (taxId: string, taxName: string) => {
        // Use the passed confirm function and translations
        const confirmed = await confirm({
            title: tShared("confirmationDialog.title"),
            message: tShared("confirmationDialog.deleteMessage", { item: `${tTable("taxItem")} '${taxName}'` }), // Use tTable
            confirmText: tShared("confirmationDialog.delete"),
            cancelText: tShared("confirmationDialog.cancel"),
        });

        if (confirmed) {
            const toastId = toast.loading(tShared("confirmationDialog.deleting")); // Use tShared
            try {
                await deleteTax(taxId);
                toast.success(tTable("deleteSuccessToast", { name: taxName }), { id: toastId }); // Use tTable
                if (onTaxDeleted) {
                    onTaxDeleted(taxId); // Notify parent component
                }
            } catch (error: any) {
                toast.error(tTable("deleteErrorToast", { error: error?.message || "Unknown error" }), { id: toastId }); // Use tTable
                console.error("Failed to delete tax:", error);
            }
        }
    };

    return [
        // Remove the ConfirmDialog cell, it's now rendered directly in TaxesDataTable
        // {
        //     id: 'deleteConfirmationDialog',
        //     header: () => null,
        //     cell: () => <ConfirmDialog />, // Removed
        //     enableSorting: false,
        //     enableHiding: false,
        // },
        {
            accessorKey: locale === "ar" ? "nameAr" : "nameEn", // Use passed locale
            header: ({ column }) => (
                // Pass simple string title. DataTableColumnHeader should get its own translations.
                <DataTableColumnHeader column={column} title={tTable(locale === "ar" ? "nameAr" : "nameEn")} /> // Use tTable
            ),
            cell: ({ row }) => {
                const name = locale === "ar" ? row.original.nameAr : row.original.nameEn; // Use passed locale
                return <div className="font-medium">{name}</div>;
            },
        },
        {
            accessorKey: "percent",
            header: ({ column }) => (
                 // Pass simple string title.
                <DataTableColumnHeader column={column} title={tTable("percent")} /> // Use tTable
            ),
            cell: ({ row }) => {
                const percent = parseFloat(row.getValue("percent"));
                // Format as percentage
                const formatted = new Intl.NumberFormat(locale, {
                    style: 'percent',
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                }).format(percent / 100); // Divide by 100 because Intl expects a decimal (0.15 for 15%)
                return <div className="text-right">{formatted}</div>;
            },
        },
        {
            accessorKey: "chartOfAccount",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={tTable("chartOfAccount")} />
            ),
            cell: ({ row }) => {
                const account = row.original.chartOfAccount;
                const accountLabel = account
                    ? `${account.accountNumber} - ${locale === 'ar' ? account.nameAr : account.nameEn}`
                    : "-";
                return <div className="truncate max-w-[150px]">{accountLabel}</div>;
            },
            enableSorting: false, // Usually don't sort by nested object display name
        },
        {
            accessorKey: locale === "ar" ? "descriptionAr" : "descriptionEn", // Use passed locale
            // Header can be a simple string
            header: tTable(locale === "ar" ? "descriptionAr" : "descriptionEn"), // Use tTable and passed locale
            cell: ({ row }) => {
                const description = locale === "ar" ? row.original.descriptionAr : row.original.descriptionEn; // Use passed locale
                return <div className="truncate max-w-xs">{description || "-"}</div>; // Show hyphen if empty
            },
            enableSorting: false, // Usually descriptions aren't sorted
        },
        {
            id: "actions",
            cell: ({ row }) => {
                const tax = row.original;
                const taxName = locale === "ar" ? tax.nameAr : tax.nameEn;

                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                {/* Use tSharedDataTable for specific table actions */}
                                <span className="sr-only">{tSharedDataTable("openMenu")}</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            {/*<DropdownMenuLabel>{tSharedDataTable("actions")}</DropdownMenuLabel>*/}
                            <DropdownMenuItem onClick={() => openEditDialog(tax)}>
                                <Pen className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                                {tSharedDataTable("edit")}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                                className="text-destructive focus:text-destructive focus:bg-destructive/10"
                                onClick={() => handleDelete(tax.id, taxName)}
                            >
                                <Trash className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
                                {tSharedDataTable("delete")}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
            enableSorting: false,
            enableHiding: false,
        },
    ];
};
