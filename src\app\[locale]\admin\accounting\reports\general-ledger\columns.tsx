"use client";

import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { GeneralLedgerEntryDto } from "@/lib/dto/admin/accounting/general-ledger.dto";
import { format } from "date-fns";

export const createColumns = () => {
  const columns: ColumnDef<GeneralLedgerEntryDto>[] = [
    {
      accessorKey: "date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Date" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("date");
        return <div>{date ? format(new Date(date as string), "yyyy-MM-dd") : "-"}</div>;
      },
    },
    {
      accessorKey: "journalEntryId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Journal Entry ID" />
      ),
      cell: ({ row }) => row.getValue("journalEntryId"),
    },
    {
      accessorKey: "accountCode",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Account Code" />
      ),
      cell: ({ row }) => row.getValue("accountCode"),
    },
    {
      accessorKey: "accountName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Account Name" />
      ),
      cell: ({ row }) => row.getValue("accountName"),
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      cell: ({ row }) => row.getValue("description"),
    },
    {
      accessorKey: "debit",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Debit" />
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("debit"));
        return <div className="text-right">{amount.toFixed(2)} SAR</div>; // Format as currency
      },
    },
    {
      accessorKey: "credit",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Credit" />
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("credit"));
        return <div className="text-right">{amount.toFixed(2)} SAR</div>; // Format as currency
      },
    },
    {
      accessorKey: "runningBalance",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Running Balance" />
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("runningBalance"));
        return <div className="text-right">{amount.toFixed(2)} SAR</div>; // Format as currency
      },
    },
  ];

  return columns;
};
