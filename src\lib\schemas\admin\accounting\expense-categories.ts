import { z } from "zod";
import { UseTranslations } from "next-intl";

// Schema for creating an expense category
// Expects t scoped to AdminExpenseCategoriesPage.AddDialog
export const createExpenseCategorySchema = (t: UseTranslations<"AdminExpenseCategoriesPage.AddDialog">) => z.object({
    nameEn: z.string()
        .min(1, { message: t("validation.nameEnRequired") }) // Use dot notation
        .max(100, { message: t("validation.nameEnTooLong") }),
    nameAr: z.string()
        .min(1, { message: t("validation.nameArRequired") })
        .max(100, { message: t("validation.nameArTooLong") }),
    descriptionEn: z.string()
        .max(500, { message: t("validation.descriptionEnTooLong") })
        .optional()
        .nullable(), // Allow null or empty string
    descriptionAr: z.string()
        .max(500, { message: t("validation.descriptionArTooLong") })
        .optional()
        .nullable(), // Allow null or empty string
    parentCategoryId: z.string()
        .uuid({ message: t("validation.invalidParentCategory") })
        .optional()
        .nullable(), // Allow null or empty string for no parent
    expenseAccountId: z.string({ required_error: t("validation.expenseAccountRequired") })
        .uuid({ message: t("validation.expenseAccountInvalid") }),
});

// Schema for updating an expense category
// Expects t scoped to AdminExpenseCategoriesPage.EditDialog
export const updateExpenseCategorySchema = (t: UseTranslations<"AdminExpenseCategoriesPage.EditDialog">) => z.object({
    nameEn: z.string()
        .min(1, { message: t("validation.nameEnRequired") }) // Use dot notation
        .max(100, { message: t("validation.nameEnTooLong") })
        .optional(), // Optional for update
    nameAr: z.string()
        .min(1, { message: t("validation.nameArRequired") })
        .max(100, { message: t("validation.nameArTooLong") })
        .optional(), // Optional for update
    descriptionEn: z.string()
        .max(500, { message: t("validation.descriptionEnTooLong") })
        .optional()
        .nullable(),
    descriptionAr: z.string()
        .max(500, { message: t("validation.descriptionArTooLong") })
        .optional()
        .nullable(),
    parentCategoryId: z.string()
        .uuid({ message: t("validation.invalidParentCategory") })
        .optional()
        .nullable(),
    expenseAccountId: z.string({ required_error: t("validation.expenseAccountRequired") })
        .uuid({ message: t("validation.expenseAccountInvalid") })
        .optional(), // Keep optional for partial updates if needed, but API requires it
});

export type CreateExpenseCategoryInput = z.infer<ReturnType<typeof createExpenseCategorySchema>>;
export type UpdateExpenseCategoryInput = z.infer<ReturnType<typeof updateExpenseCategorySchema>>;
