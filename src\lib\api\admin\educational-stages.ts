import { fetchWithAuth } from '@/lib/fetch-with-auth';
import { API_BASE_URL } from '@/lib/constants';
import {
    EducationalStageDto,
    CreateEducationalStageRequest,
    UpdateEducationalStageRequest,
} from '@/lib/dto/admin/educational-stage.dto';
import { handleApiError } from '@/lib/api-error-handler'; // Corrected import path

const ADMIN_STAGES_ENDPOINT = `${API_BASE_URL}/admin/educational-stages`;

/**
 * Fetches all educational stages.
 * Note: API spec indicates this returns an array, not a paginated response.
 * @returns A promise that resolves to an array of EducationalStageDto.
 */
export async function getAllEducationalStagesList(): Promise<EducationalStageDto[]> { // Renamed function
    const url = ADMIN_STAGES_ENDPOINT;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            await handleApiError(response, 'Failed to fetch educational stages');
        }
        const data: unknown = await response.json();
        // TODO: Add runtime validation if necessary (e.g., with <PERSON><PERSON>)
        return data as EducationalStageDto[];
    } catch (error) {
        console.error("Error fetching educational stages:", error);
        throw error; // Re-throw after logging or handling
    }
}

/**
 * Fetches a single educational stage by its ID.
 * @param id - The UUID of the educational stage.
 * @returns A promise that resolves to an EducationalStageDto.
 */
export async function getEducationalStageById(id: string): Promise<EducationalStageDto> {
    const url = `${ADMIN_STAGES_ENDPOINT}/${id}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            await handleApiError(response, `Failed to fetch educational stage with id ${id}`);
        }
        const data: unknown = await response.json();
        // TODO: Add runtime validation if necessary
        return data as EducationalStageDto;
    } catch (error) {
        console.error(`Error fetching educational stage ${id}:`, error);
        throw error;
    }
}

/**
 * Creates a new educational stage.
 * @param request - The data for the new educational stage.
 * @returns A promise that resolves to the created EducationalStageDto.
 */
export async function createEducationalStage(request: CreateEducationalStageRequest): Promise<EducationalStageDto> {
    const url = ADMIN_STAGES_ENDPOINT;
    try {
        const response = await fetchWithAuth(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request),
        });
        if (!response.ok) { // Check for 200 OK based on API spec (not 201)
             await handleApiError(response, 'Failed to create educational stage');
        }
        const data: unknown = await response.json();
         // TODO: Add runtime validation if necessary
        return data as EducationalStageDto;
    } catch (error) {
        console.error("Error creating educational stage:", error);
        throw error;
    }
}

/**
 * Updates an existing educational stage.
 * @param id - The UUID of the educational stage to update.
 * @param request - The updated data for the educational stage.
 * @returns A promise that resolves to the updated EducationalStageDto.
 */
export async function updateEducationalStage(id: string, request: UpdateEducationalStageRequest): Promise<EducationalStageDto> {
    const url = `${ADMIN_STAGES_ENDPOINT}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request),
        });
        if (!response.ok) {
             await handleApiError(response, `Failed to update educational stage ${id}`);
        }
        const data: unknown = await response.json();
         // TODO: Add runtime validation if necessary
        return data as EducationalStageDto;
    } catch (error) {
        console.error(`Error updating educational stage ${id}:`, error);
        throw error;
    }
}

/**
 * Deletes an educational stage.
 * @param id - The UUID of the educational stage to delete.
 * @returns A promise that resolves when the deletion is successful.
 */
export async function deleteEducationalStage(id: string): Promise<void> {
    const url = `${ADMIN_STAGES_ENDPOINT}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'DELETE',
        });
        if (!response.ok) { // Check for 200 OK based on API spec (not 204)
             await handleApiError(response, `Failed to delete educational stage ${id}`);
        }
        // No content expected on successful delete based on API spec returning 200 OK
    } catch (error) {
        console.error(`Error deleting educational stage ${id}:`, error);
        throw error;
    }
}
