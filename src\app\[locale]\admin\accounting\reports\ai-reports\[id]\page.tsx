"use client";

import { ArrowLeft, Download, FileDown } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PageHeader, PageHeaderDescription, PageHeaderHeading } from "@/components/ui/page-header";
import { useEffect, useRef, useState } from "react";
import { useParams, useRouter } from "next/navigation";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ReportStatus } from "@/lib/dto/admin/accounting/ai-reports.dto";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";
import { toast } from "sonner";
import { useAiReport } from "@/lib/api/admin/accounting/ai-reports";
import { useTranslations } from "next-intl";

export default function AiReportDetailPage() {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations("AiReportsPage");
  const tTable = useTranslations("AiReportsPage.table");

  const id = params.id as string;
  const { data: report, isLoading, isError } = useAiReport(id);

  const contentRef = useRef<HTMLDivElement>(null);
  const reportContainerRef = useRef<HTMLDivElement>(null);
  const [chartConfig, setChartConfig] = useState<any>(null);
  const [isExporting, setIsExporting] = useState(false);

  // Function to export the report as PDF
  const exportToPdf = async () => {
    if (!reportContainerRef.current || !report) return;

    setIsExporting(true);
    toast.info(t("exportingPdf"));

    try {
      // Dynamically import html2pdf to avoid SSR issues
      const html2pdf = (await import('html2pdf.js')).default;

      const reportTitle = `${tTable(report.reportType.toLowerCase())}_${report.startDate}_${report.endDate}`;

      // Create a clone of the report container to modify for PDF export
      const clonedContainer = reportContainerRef.current.cloneNode(true) as HTMLElement;

      // Create a wrapper for the first page content
      const firstPageWrapper = document.createElement('div');
      firstPageWrapper.className = 'first-page-wrapper';
      firstPageWrapper.style.position = 'relative';
      firstPageWrapper.style.pageBreakAfter = 'always';

      // Add logo to the top right
      const logoWrapper = document.createElement('div');
      logoWrapper.className = 'logo-wrapper';
      logoWrapper.style.position = 'absolute';
      logoWrapper.style.top = '10px';
      logoWrapper.style.right = '10px';
      logoWrapper.style.width = '120px';
      logoWrapper.style.height = 'auto';
      logoWrapper.innerHTML = `
        <img src="/logo.png" alt="Maali School Logo" style="width: 100%; height: auto;" />
      `;
      firstPageWrapper.appendChild(logoWrapper);

      // Add a title and header to the PDF
      const pdfHeader = document.createElement('div');
      pdfHeader.className = 'pdf-header';
      pdfHeader.style.paddingTop = '40px'; // Add space for logo
      pdfHeader.innerHTML = `
        <h1 style="color: #1C5954; font-size: 24px; text-align: center; margin-bottom: 8px;">
          ${tTable(report.reportType.toLowerCase())}
        </h1>
        <p style="text-align: center; margin-bottom: 16px; font-size: 16px;">
          ${report.startDate} - ${report.endDate}
        </p>
        <hr style="border: 1px solid #e2e8f0; margin-bottom: 24px;" />
      `;
      firstPageWrapper.appendChild(pdfHeader);

      // Extract report info and chart for first page
      const reportInfoCard = clonedContainer.querySelector('.card:first-child');
      const chartCard = clonedContainer.querySelector('.card:nth-child(2)');

      if (reportInfoCard) {
        const clonedReportInfo = reportInfoCard.cloneNode(true);
        firstPageWrapper.appendChild(clonedReportInfo);
        reportInfoCard.remove();
      }

      if (chartCard) {
        const clonedChartCard = chartCard.cloneNode(true);
        firstPageWrapper.appendChild(clonedChartCard);
        chartCard.remove();
      }

      // Insert the first page wrapper at the beginning of the container
      clonedContainer.insertBefore(firstPageWrapper, clonedContainer.firstChild);

      // Add CSS classes to elements for PDF styling
      const cardHeaders = clonedContainer.querySelectorAll('.card-header');
      if (cardHeaders.length === 0) {
        clonedContainer.querySelectorAll('[class*="CardHeader"]').forEach(el => {
          el.classList.add('card-header');
        });
      }

      const cardTitles = clonedContainer.querySelectorAll('.card-title');
      if (cardTitles.length === 0) {
        clonedContainer.querySelectorAll('[class*="CardTitle"]').forEach(el => {
          el.classList.add('card-title');
        });
      }

      const cardContents = clonedContainer.querySelectorAll('.card-content');
      if (cardContents.length === 0) {
        clonedContainer.querySelectorAll('[class*="CardContent"]').forEach(el => {
          el.classList.add('card-content');
        });
      }

      // Apply PDF-compatible styles (replace oklch colors with hex)
      const styleElement = document.createElement('style');
      styleElement.textContent = `
        /* Override modern color functions with PDF-compatible colors */
        :root {
          --background: #ffffff;
          --foreground: #000000;
          --card: #ffffff;
          --card-foreground: #000000;
          --popover: #ffffff;
          --popover-foreground: #000000;
          --primary: #1C5954;
          --primary-foreground: #ffffff;
          --secondary: #f1f5f9;
          --secondary-foreground: #0f172a;
          --muted: #f1f5f9;
          --muted-foreground: #64748b;
          --accent: #f1f5f9;
          --accent-foreground: #0f172a;
          --destructive: #ef4444;
          --destructive-foreground: #ffffff;
          --border: #e2e8f0;
          --input: #e2e8f0;
          --ring: #1C5954;
        }

        /* PDF-specific styles */
        .report-container {
          font-family: Arial, sans-serif !important;
          color: #000000 !important;
          background-color: #ffffff !important;
          padding: 10mm !important;
        }

        /* Ensure text is visible in PDF */
        .report-container * {
          color: #000000 !important;
          font-family: Arial, sans-serif !important;
        }

        /* Make sure badges have visible background colors */
        .badge {
          background-color: #1C5954 !important;
          color: #ffffff !important;
          padding: 4px 8px !important;
          border-radius: 4px !important;
        }

        /* Ensure card borders are visible */
        .card {
          border: 1px solid #e2e8f0 !important;
          background-color: #ffffff !important;
          border-radius: 8px !important;
          margin-bottom: 16px !important;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
          page-break-inside: avoid !important;
          width: 100% !important;
          box-sizing: border-box !important;
        }

        /* Card header styling */
        .card-header {
          border-bottom: 1px solid #e2e8f0 !important;
          padding: 16px !important;
        }

        /* Card title styling */
        .card-title {
          font-size: 18px !important;
          font-weight: bold !important;
          color: #1C5954 !important;
        }

        /* Card content styling */
        .card-content {
          padding: 16px !important;
        }

        /* Table styling */
        table {
          width: 100% !important;
          border-collapse: collapse !important;
          font-size: 12px !important;
          page-break-inside: auto !important;
        }

        tr {
          page-break-inside: avoid !important;
          page-break-after: auto !important;
        }

        th, td {
          border: 1px solid #e2e8f0 !important;
          padding: 6px !important;
          text-align: left !important;
          word-break: break-word !important;
          max-width: 150px !important;
        }

        th {
          background-color: #f8fafc !important;
          font-weight: bold !important;
        }

        /* Prose content styling */
        .prose {
          font-size: 12px !important;
          line-height: 1.5 !important;
        }

        .prose h1, .prose h2, .prose h3 {
          color: #1C5954 !important;
          margin-top: 16px !important;
          margin-bottom: 8px !important;
        }

        .prose p {
          margin-bottom: 8px !important;
        }

        .prose table {
          margin-top: 8px !important;
          margin-bottom: 16px !important;
        }

        /* Canvas/Chart styling */
        canvas {
          max-width: 100% !important;
          height: auto !important;
        }

        /* First page specific styling */
        .first-page-wrapper {
          position: relative !important;
          padding-top: 20px !important;
        }

        /* Force page breaks */
        .page-break {
          page-break-after: always !important;
        }
      `;

      clonedContainer.appendChild(styleElement);

      // If there's a chart, we need to ensure it's rendered in the cloned container
      if (chartConfig) {
        // Find the canvas in the cloned container
        const canvasElement = clonedContainer.querySelector('canvas');
        if (canvasElement) {
          // Wait a moment to ensure chart is fully rendered
          await new Promise(resolve => setTimeout(resolve, 500));

          // Get the original canvas
          const originalCanvas = document.getElementById('chart') as HTMLCanvasElement;
          if (originalCanvas) {
            // Copy the rendered chart to the cloned canvas
            const ctx = canvasElement.getContext('2d');
            if (ctx) {
              canvasElement.width = originalCanvas.width;
              canvasElement.height = originalCanvas.height;
              ctx.drawImage(originalCanvas, 0, 0);
            }
          }
        }
      }

      const opt = {
        margin: [10, 10, 10, 10], // [top, right, bottom, left] in mm
        filename: `${reportTitle}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          logging: false,
          letterRendering: true
        },
        jsPDF: {
          unit: 'mm',
          format: 'a4',
          orientation: 'landscape',
          compress: true
        },
        pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
      };

      // Generate PDF from the cloned and styled container
      await html2pdf().from(clonedContainer).set(opt).save();

      toast.success(t("exportSuccess"));
    } catch (error) {
      console.error("PDF export error:", error);
      toast.error(t("exportError"));
    } finally {
      setIsExporting(false);
    }
  };

  // Extract HTML content and chart configuration from the report content
  useEffect(() => {
    if (!report?.content) return;

    // Try to extract HTML content
    const htmlMatch = report.content.match(/```html\n([\s\S]*?)\n```/);
    const htmlContent = htmlMatch ? htmlMatch[1] : null;

    // Try to extract chart.js configuration
    const jsonMatch = report.content.match(/```json\n([\s\S]*?)\n```/);
    const jsonConfig = jsonMatch ? jsonMatch[1] : null;

    if (htmlContent && contentRef.current) {
      // Extract only the body content
      const bodyContentMatch = htmlContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
      const bodyContent = bodyContentMatch ? bodyContentMatch[1] : htmlContent;

      // Set the HTML content
      contentRef.current.innerHTML = bodyContent;
    }

    if (jsonConfig) {
      try {
        const config = JSON.parse(jsonConfig);
        setChartConfig(config);
      } catch (error) {
        console.error("Failed to parse chart configuration:", error);
      }
    }
  }, [report]);

  // Render chart if configuration is available
  useEffect(() => {
    if (!chartConfig) return;

    // Dynamically import Chart.js to avoid SSR issues
    import('chart.js').then(({ Chart, registerables }) => {
      // Register all Chart.js components
      Chart.register(...registerables);

      // Get the canvas element
      const canvas = document.getElementById('chart') as HTMLCanvasElement;
      if (!canvas) return;

      // Create the chart
      const chart = new Chart(canvas, chartConfig);

      // Cleanup on unmount
      return () => {
        chart.destroy();
      };
    }).catch(error => {
      console.error("Failed to load Chart.js:", error);
    });
  }, [chartConfig]);

  // Handle back button click
  const handleBack = () => {
    router.back();
  };

  // Get badge variant based on status
  const getBadgeVariant = (status: ReportStatus) => {
    switch (status) {
      case "COMPLETED":
        return "default";
      case "PENDING":
        return "secondary";
      case "FAILED":
        return "destructive";
      default:
        return "default";
    }
  };

  // Check if report is exportable
  const isExportable = report?.status === "COMPLETED";

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("back")}
          </Button>
        </div>
        <Skeleton className="h-8 w-64 mb-2" />
        <Skeleton className="h-6 w-full mb-4" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  if (isError || !report) {
    return (
      <div className="space-y-4">
        <Button variant="outline" size="sm" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t("back")}
        </Button>
        <div className="text-red-500">{t("errorLoadingReport")}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("back")}
          </Button>

          {isExportable && (
            <Button
              variant="outline"
              size="sm"
              onClick={exportToPdf}
              disabled={isExporting}
            >
              <FileDown className="mr-2 h-4 w-4" />
              {isExporting ? t("exporting") : t("exportPdf")}
            </Button>
          )}
        </div>

        <Badge variant={getBadgeVariant(report.status) as "default" | "secondary" | "destructive"}>
          {tTable(report.status.toLowerCase())}
        </Badge>
      </div>

      <PageHeader>
        <PageHeaderHeading>{t("reportDetail")}</PageHeaderHeading>
        <PageHeaderDescription>
          {tTable(report.reportType.toLowerCase())} • {report.startDate} - {report.endDate}
        </PageHeaderDescription>
      </PageHeader>

      {/* Container for PDF export */}
      <div ref={reportContainerRef} className="space-y-6 report-container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("reportInfo")}</CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="grid grid-cols-1 gap-2 text-sm">
                <div className="grid grid-cols-2 gap-1">
                  <dt className="font-medium">{tTable("reportType")}:</dt>
                  <dd>{tTable(report.reportType.toLowerCase())}</dd>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <dt className="font-medium">{tTable("startDate")}:</dt>
                  <dd>{report.startDate}</dd>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <dt className="font-medium">{tTable("endDate")}:</dt>
                  <dd>{report.endDate}</dd>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <dt className="font-medium">{tTable("createdDate")}:</dt>
                  <dd>{format(new Date(report.createdDate), "yyyy-MM-dd HH:mm")}</dd>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <dt className="font-medium">{t("fromCache")}:</dt>
                  <dd>{report.fromCache ? t("yes") : t("no")}</dd>
                </div>
              </dl>
            </CardContent>
          </Card>

          {chartConfig && (
            <Card>
              <CardHeader>
                <CardTitle>{t("chart")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="w-full h-[300px]">
                  <canvas id="chart"></canvas>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t("reportContent")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div
              ref={contentRef}
              className="prose prose-sm max-w-none dark:prose-invert"
            ></div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
