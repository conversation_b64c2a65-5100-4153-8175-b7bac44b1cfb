import { Page } from "@/lib/dto/page";
import { SimpleFeeCategoryDto } from "./fee-categories.dto"; // Assuming this exists

// Based on #/components/schemas/FeeDto
export interface FeeDto {
    id: string;
    nameEn: string;
    nameAr: string;
    descriptionEn?: string | null;
    descriptionAr?: string | null;
    amount: number;
    academicYearId: string; // Changed from academicYear to academicYearId (UUID)
    dueDate: string; // ISO Date string (YYYY-MM-DD)
    feeCategory?: SimpleFeeCategoryDto; // Optional based on schema, but likely present
    applicableGradeId?: string | null; // UUID
    applicableStageId?: string | null; // UUID
    applicableBranchId?: string | null; // UUID
    active: boolean;
    createdDate: string; // ISO DateTime string
    lastModifiedDate: string; // ISO DateTime string
}

// Based on #/components/schemas/SimpleFeeDto used in StudentFeeDto
export interface SimpleFeeDto {
    id: string;
    nameEn: string;
    nameAr: string;
    amount: number;
    academicYearId: string; // Changed from academicYear to academicYearId (UUID)
    dueDate: string; // ISO Date string (YYYY-MM-DD)
}

// Based on #/components/schemas/CreateFeeRequest
export interface CreateFeeRequest {
    nameEn: string;
    nameAr: string;
    descriptionEn?: string | null;
    descriptionAr?: string | null;
    amount: number;
    academicYearId: string; // Changed from academicYear to academicYearId (UUID)
    dueDate: string; // ISO Date string (YYYY-MM-DD)
    feeCategoryId: string; // UUID
    applicableGradeId?: string | null; // UUID
    applicableStageId?: string | null; // UUID
    applicableBranchId?: string | null; // UUID
}

// Based on #/components/schemas/UpdateFeeRequest
export interface UpdateFeeRequest {
    nameEn?: string;
    nameAr?: string;
    descriptionEn?: string | null;
    descriptionAr?: string | null;
    amount?: number;
    academicYearId?: string; // Changed from academicYear to academicYearId (UUID)
    dueDate?: string; // ISO Date string (YYYY-MM-DD)
    feeCategoryId?: string; // UUID
    applicableGradeId?: string | null; // UUID
    applicableStageId?: string | null; // UUID
    applicableBranchId?: string | null; // UUID
    active?: boolean;
}

// Based on #/components/schemas/PageFeeDto
export type PageFeeDto = Page<FeeDto>;

// Parameters for fetching Fees
// Based on GET /api/v1/accounting/fees query parameters
export interface GetFeesParams {
    page?: number;
    size?: number;
    sort?: string[];
    academicYearId?: string; // Changed from academicYear to academicYearId (UUID)
    categoryId?: string; // UUID
    // Add activeOnly if needed for client-side filtering or if API supports it
    activeOnly?: boolean;
}
