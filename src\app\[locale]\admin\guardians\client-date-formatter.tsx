"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";

interface ClientDateFormatterProps {
  date: string;
  formatString?: string;
}

export function ClientDateFormatter({ date, formatString = "PPP" }: ClientDateFormatterProps) {
  const [formattedDate, setFormattedDate] = useState<string | null>(null);

  useEffect(() => {
    // Only format the date on the client side
    setFormattedDate(format(new Date(date), formatString));
  }, [date, formatString]);

  // Return null during SSR to avoid hydration mismatch
  if (formattedDate === null) {
    return <span>{date}</span>;
  }

  return <span>{formattedDate}</span>;
}
