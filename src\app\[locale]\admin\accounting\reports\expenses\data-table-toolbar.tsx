"use client";

import { Button } from "@/components/ui/button";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { useTranslations } from "next-intl";

interface DataTableToolbarProps {
  onDateRangeChange: (date: DateRange | undefined) => void;
  onGenerateReport: () => void;
  isLoading: boolean;
  dateRange: DateRange | undefined;
}

export function DataTableToolbar({
  onDateRangeChange,
  onGenerateReport,
  isLoading,
  dateRange,
}: DataTableToolbarProps) {
  const t = useTranslations("ExpenseReportPage");
  const tSharedDate = useTranslations("Shared.dateRangePicker");

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <DateRangePicker
          date={dateRange}
          onDateChange={onDateRangeChange}
          placeholder={t("selectDateRange")}
          className="w-full sm:w-auto"
        />
      </div>
      <Button
        onClick={onGenerateReport}
        disabled={!dateRange?.from || !dateRange?.to || isLoading}
      >
        {isLoading ? t("generating") : t("generateReport")}
      </Button>
    </div>
  );
}
