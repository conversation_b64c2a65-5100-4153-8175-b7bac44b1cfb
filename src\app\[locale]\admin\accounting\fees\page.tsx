import { getTranslations } from "next-intl/server";
import { FeesDataTable } from "./data-table"; // Import the client component
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default async function FeesPage() {
    const t = await getTranslations("AdminFeesPage");

    return (
        <Card>
            <CardHeader>
                <CardTitle>{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent>
                 {/* Data table is a client component handling its own data fetching */}
                <FeesDataTable />
            </CardContent>
        </Card>
    );
}
