// Use environment variables for sensitive or environment-specific values
// Provide a default for local development if the variable isn't set
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081/api/v1";

// Log the API base URL during initialization to help with debugging
console.log(`[constants] API_BASE_URL: ${API_BASE_URL}`);

// Add other constants here as needed
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE_INDEX = 0;

export const API_ENDPOINTS = {
  AUTH_LOGIN: `${API_BASE_URL}/auth/login`,
  AUTH_REFRESH: `${API_BASE_URL}/auth/refresh-token`,
  ADMIN_USERS: `${API_BASE_URL}/admin/users`,
  ADMIN_ROLES: `${API_BASE_URL}/admin/roles`,
  ADMIN_PERMISSIONS: `${API_BASE_URL}/admin/permissions`,
  // School Management
  ADMIN_BRANCHES: `${API_BASE_URL}/admin/branches`,
  ADMIN_EDUCATIONAL_STAGES: `${API_BASE_URL}/admin/educational-stages`,
  ADMIN_GRADE_LEVELS: `${API_BASE_URL}/admin/grade-levels`,
  ADMIN_ACADEMIC_YEARS: `${API_BASE_URL}/admin/academic-years`,
  ADMIN_SECTIONS: `${API_BASE_URL}/admin/sections`, // Added Section Endpoint
  // Accounting
  ADMIN_ACCOUNTS: `${API_BASE_URL}/admin/accounts`,
  ADMIN_ACCOUNT_CATEGORIES: `${API_BASE_URL}/admin/account-categories`,
  ADMIN_TRANSACTIONS: `${API_BASE_URL}/admin/transactions`,
  ADMIN_FEES: `${API_BASE_URL}/admin/fees`,
  ADMIN_FEE_STRUCTURES: `${API_BASE_URL}/admin/fee-structures`,
  ADMIN_FEE_PAYMENTS: `${API_BASE_URL}/admin/fee-payments`,
  ADMIN_EXPENSES: `${API_BASE_URL}/admin/expenses`,
  ADMIN_EXPENSE_CATEGORIES: `${API_BASE_URL}/admin/expense-categories`,
  ADMIN_SALARIES: `${API_BASE_URL}/admin/salaries`,
  ADMIN_SALARY_STRUCTURES: `${API_BASE_URL}/admin/salary-structures`,
  ADMIN_PAYROLLS: `${API_BASE_URL}/admin/payrolls`,
  // Reports
  ADMIN_REPORTS_GENERAL_LEDGER: `${API_BASE_URL}/admin/reports/general-ledger`,
  ADMIN_REPORTS_BALANCE_SHEET: `${API_BASE_URL}/admin/reports/balance-sheet`,
  ADMIN_REPORTS_INCOME_STATEMENT: `${API_BASE_URL}/admin/reports/income-statement`,
  ADMIN_REPORTS_CASH_FLOW: `${API_BASE_URL}/admin/reports/cash-flow`,
  ADMIN_REPORTS_FEE_COLLECTION: `${API_BASE_URL}/admin/reports/fee-collection`,
  ADMIN_REPORTS_EXPENSE_SUMMARY: `${API_BASE_URL}/admin/reports/expense-summary`,
  ADMIN_REPORTS_SALARY_SHEET: `${API_BASE_URL}/admin/reports/salary-sheet`,
  // Student Management
  ADMIN_STUDENTS: `${API_BASE_URL}/admin/students`,
  ADMIN_STUDENT_ATTENDANCE: `${API_BASE_URL}/admin/student-attendance`,
  ADMIN_STUDENT_GRADES: `${API_BASE_URL}/admin/student-grades`,
  // Staff Management
  ADMIN_STAFF: `${API_BASE_URL}/admin/staff`,
  ADMIN_STAFF_ATTENDANCE: `${API_BASE_URL}/admin/staff-attendance`,
  // Settings
  ADMIN_SETTINGS: `${API_BASE_URL}/admin/settings`,
};
