import { Page } from "@/lib/dto/common.dto";

/**
 * Data Transfer Object for Academic Year details.
 * Based on GET /api/v1/admin/academic-years/{id} response schema.
 */
export interface AcademicYearDto {
    id: string;
    name: string;
    startDate: string; // ISO Date string (e.g., "YYYY-MM-DD")
    endDate: string;   // ISO Date string (e.g., "YYYY-MM-DD")
    active: boolean;
    // terms?: TermDto[]; // Assuming TermDto exists if needed later
    createdAt: string; // ISO DateTime string
    updatedAt: string; // ISO DateTime string
}

/**
 * Request body for creating a new Academic Year.
 * Based on POST /api/v1/admin/academic-years request schema.
 */
export interface CreateAcademicYearRequest {
    name: string;
    startDate: string; // ISO Date string (e.g., "YYYY-MM-DD")
    endDate: string;   // ISO Date string (e.g., "YYYY-MM-DD")
}

/**
 * Request body for updating an existing Academic Year.
 * Based on PUT /api/v1/admin/academic-years/{id} request schema.
 */
export interface UpdateAcademicYearRequest {
    name?: string;
    startDate?: string; // ISO Date string (e.g., "YYYY-MM-DD")
    endDate?: string;   // ISO Date string (e.g., "YYYY-MM-DD")
    // 'active' status is usually handled by a separate endpoint
}

/**
 * Represents a paginated response for Academic Years.
 * Based on GET /api/v1/admin/academic-years response schema.
 */
export type PageAcademicYearDto = Page<AcademicYearDto>;

/**
 * Parameters for fetching Academic Years.
 * Based on GET /api/v1/admin/academic-years and /active query parameters.
 */
export interface GetAcademicYearsParams {
    page?: number;
    size?: number;
    sort?: string[];
    search?: string; // For filtering by name
    activeOnly?: boolean; // Custom flag to decide which endpoint to call
}
