"use client";

import { Table } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options";
import { X } from "lucide-react";
import { AddFeeCategoryDialog } from "./add-dialog"; // Import the Add Dialog

interface DataTableToolbarProps<TData> {
    table: Table<TData>;
    globalFilter: string;
    setGlobalFilter: (value: string) => void;
    // No active/inactive toggle for Fee Categories based on API
}

export function DataTableToolbar<TData>({
    table,
    globalFilter,
    setGlobalFilter,
}: DataTableToolbarProps<TData>) {
    const t = useTranslations("AdminFeeCategoriesPage"); // Adjust translation namespace
    const isFiltered = globalFilter !== '' || table.getState().columnFilters.length > 0;

    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
                <Input
                    placeholder={t("searchPlaceholder")} // Use correct placeholder
                    value={globalFilter ?? ""}
                    onChange={(event) => setGlobalFilter(event.target.value)}
                    className="h-8 w-[150px] lg:w-[250px]"
                />
                {/* Add column-specific filters here if needed */}
                {isFiltered && (
                    <Button
                        variant="ghost"
                        onClick={() => {
                            setGlobalFilter('');
                            table.resetColumnFilters();
                        }}
                        className="h-8 px-2 lg:px-3"
                    >
                        {t("resetFilters")} {/* Use translation */}
                        <X className="ml-2 h-4 w-4" />
                    </Button>
                )}
            </div>
            <div className="flex items-center space-x-2">
                {/* No active/inactive toggle */}
                <DataTableViewOptions table={table} />
                <AddFeeCategoryDialog /> {/* Add the button/dialog trigger */}
            </div>
        </div>
    );
}
