import { API_BASE_URL } from "@/lib/constants";
import {
    StudentDto,
    CreateStudentProfileRequest,
    UpdateStudentProfileRequest,
    GetStudentsParams,
    PageStudentDto
} from "@/lib/dto/admin/student.dto";
import { ErrorResponse } from "@/lib/dto/error-response.dto";
import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { buildQueryString } from "@/lib/utils";

const STUDENTS_API_PATH = `${API_BASE_URL}/students`;

/**
 * Fetches all students with optional pagination and filtering
 * GET /api/v1/students
 */
export async function getStudents(params: GetStudentsParams = {}): Promise<PageStudentDto> {
    const queryString = buildQueryString({
        page: params.page,
        size: params.size,
        sort: params.sort,
        search: params.search
    });

    const url = `${STUDENTS_API_PATH}${queryString ? `?${queryString}` : ''}`;
    console.log(`[getStudents] Requesting students from: ${url}`);

    try {
        const response = await fetchWithAuth(url);

        if (!response.ok) {
            let errorMessage = `Failed to fetch students: ${response.statusText}`;
            try {
                const errorData = await response.json();
                if (errorData?.message) {
                    errorMessage = errorData.message;
                } else if (errorData?.error) {
                    errorMessage = errorData.error;
                }
                console.error("[getStudents] API error response:", errorData);
            } catch (e) {
                console.error("[getStudents] Failed to parse error response:", e);
            }
            throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log(`[getStudents] Successfully fetched ${data?.content?.length || 0} students`);
        return data as PageStudentDto;
    } catch (error) {
        console.error("[getStudents] Error fetching students:", error);
        throw error;
    }
}

/**
 * Fetches a student by ID
 * GET /api/v1/students/{studentId}
 */
export async function getStudentById(studentId: string): Promise<StudentDto> {
    const url = `${STUDENTS_API_PATH}/${studentId}`;

    try {
        const response = await fetchWithAuth(url);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch student: ${response.statusText}`);
        }

        const data = await response.json();
        return data as StudentDto;
    } catch (error) {
        console.error(`Error fetching student with ID ${studentId}:`, error);
        throw error;
    }
}

/**
 * Fetches a student by user ID
 * GET /api/v1/students/user/{userId}
 */
export async function getStudentByUserId(userId: string): Promise<StudentDto> {
    const url = `${STUDENTS_API_PATH}/user/${userId}`;

    try {
        const response = await fetchWithAuth(url);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch student by user ID: ${response.statusText}`);
        }

        const data = await response.json();
        return data as StudentDto;
    } catch (error) {
        console.error(`Error fetching student with user ID ${userId}:`, error);
        throw error;
    }
}

/**
 * Creates a new student profile
 * POST /api/v1/students/profiles
 */
export async function createStudentProfile(request: CreateStudentProfileRequest): Promise<StudentDto> {
    const url = `${STUDENTS_API_PATH}/profiles`;
    console.log(`[createStudentProfile] Creating student profile at: ${url}`, {
        firstName: request.firstName,
        lastName: request.lastName,
        email: request.email,
        // Don't log password
        // Other fields can be logged if needed
    });

    try {
        const response = await fetchWithAuth(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(request),
        });

        if (!response.ok) {
            let errorMessage = `Failed to create student profile: ${response.statusText}`;
            try {
                const errorData = await response.json();
                if (errorData?.message) {
                    errorMessage = errorData.message;
                } else if (errorData?.error) {
                    errorMessage = errorData.error;
                }
                // Check for field validation errors
                if (errorData?.fieldErrors && Object.keys(errorData.fieldErrors).length > 0) {
                    const fieldErrorsStr = Object.entries(errorData.fieldErrors)
                        .map(([field, errors]) => `${field}: ${errors.join(', ')}`)
                        .join('; ');
                    errorMessage += ` (Validation errors: ${fieldErrorsStr})`;
                }
                console.error("[createStudentProfile] API error response:", errorData);
            } catch (e) {
                console.error("[createStudentProfile] Failed to parse error response:", e);
            }
            throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("[createStudentProfile] Successfully created student profile");
        return data as StudentDto;
    } catch (error) {
        console.error("[createStudentProfile] Error creating student profile:", error);
        throw error;
    }
}

/**
 * Updates an existing student profile
 * PUT /api/v1/students/profiles
 */
export async function updateStudentProfile(studentId: string, request: UpdateStudentProfileRequest): Promise<StudentDto> {
    const url = `${STUDENTS_API_PATH}/${studentId}`;

    try {
        const response = await fetchWithAuth(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(request),
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to update student profile: ${response.statusText}`);
        }

        const data = await response.json();
        return data as StudentDto;
    } catch (error) {
        console.error(`Error updating student profile with ID ${studentId}:`, error);
        throw error;
    }
}

/**
 * Links a guardian to a student
 * POST /api/v1/students/{studentId}/link-guardian/{guardianId}
 */
export async function linkGuardianToStudent(studentId: string, guardianId: string): Promise<StudentDto> {
    const url = `${STUDENTS_API_PATH}/${studentId}/link-guardian/${guardianId}`;

    try {
        const response = await fetchWithAuth(url, {
            method: 'POST',
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to link guardian to student: ${response.statusText}`);
        }

        const data = await response.json();
        return data as StudentDto;
    } catch (error) {
        console.error(`Error linking guardian ${guardianId} to student ${studentId}:`, error);
        throw error;
    }
}

/**
 * Unlinks a guardian from a student
 * POST /api/v1/students/{studentId}/unlink-guardian
 */
export async function unlinkGuardianFromStudent(studentId: string, guardianId: string): Promise<StudentDto> {
    const url = `${STUDENTS_API_PATH}/${studentId}/unlink-guardian`;

    try {
        const response = await fetchWithAuth(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ guardianId }),
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to unlink guardian from student: ${response.statusText}`);
        }

        const data = await response.json();
        return data as StudentDto;
    } catch (error) {
        console.error(`Error unlinking guardian from student ${studentId}:`, error);
        throw error;
    }
}
