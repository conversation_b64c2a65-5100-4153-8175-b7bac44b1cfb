"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

import { createGradeLevel } from "@/lib/api/admin/grade-levels";
import { createGradeLevelSchema, CreateGradeLevelInput } from "@/lib/schemas/admin/grade-levels";
import { EducationalStageDto } from "@/lib/dto/admin/educational-stage.dto";
import { getErrorMessage } from "@/lib/utils";

interface AddGradeLevelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  educationalStages: EducationalStageDto[];
}

export function AddGradeLevelDialog({
  open,
  onOpenChange,
  onSuccess,
  educationalStages,
}: AddGradeLevelDialogProps) {
  const t = useTranslations('AdminGradeLevelsPage.form');
  const tValidation = useTranslations('AdminGradeLevelsPage.validation');

  const form = useForm<CreateGradeLevelInput>({
    resolver: zodResolver(createGradeLevelSchema),
    defaultValues: {
      nameEn: "",
      nameAr: "",
      levelOrder: 1,
      educationalStageId: "",
    },
  });

  const createMutation = useMutation({
    mutationFn: createGradeLevel,
    onSuccess: () => {
      toast.success(t('addSuccess'));
      form.reset();
      onSuccess();
    },
    onError: (error) => {
      const errorMessage = getErrorMessage(error);
      toast.error(t('addError') + ': ' + errorMessage);
    },
  });

  const onSubmit = (data: CreateGradeLevelInput) => {
    createMutation.mutate(data);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && !createMutation.isPending) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('addTitle')}</DialogTitle>
          <DialogDescription>
            Fill in the information below to create a new grade level.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="educationalStageId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('educationalStageLabel')}</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('selectEducationalStagePlaceholder')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {educationalStages.map((stage) => (
                        <SelectItem key={stage.id} value={stage.id}>
                          <div className="flex flex-col">
                            <span>{stage.nameEn}</span>
                            <span className="text-sm text-muted-foreground" dir="rtl">
                              {stage.nameAr}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="nameEn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('nameEnLabel')}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('nameEnPlaceholder')}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="nameAr"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('nameArLabel')}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('nameArPlaceholder')}
                        dir="rtl"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="levelOrder"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('levelOrderLabel')}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      max="999"
                      placeholder={t('levelOrderPlaceholder')}
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={createMutation.isPending}
              >
                {t('cancel')}
              </Button>
              <Button
                type="submit"
                disabled={createMutation.isPending}
              >
                {createMutation.isPending ? t('adding') : t('add')}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
