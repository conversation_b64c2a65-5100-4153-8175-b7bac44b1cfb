"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useFieldArray, useForm } from "react-hook-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/navigation";
import { format } from "date-fns";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { DatePicker } from "@/components/ui/date-picker";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Trash2 } from "lucide-react";
import { createJournalEntrySchema, CreateJournalEntryInput } from "@/lib/schemas/admin/accounting/journal-entries";
import { createJournalEntry } from "@/lib/api/admin/accounting/journal-entries";
import { getErrorMessage } from "@/lib/utils";
import { AccountCombobox } from "@/components/ui/combobox-coa"; // Assuming this exists and works

// Helper function to format currency (consistent with project)
const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-SA', { style: 'currency', currency: 'SAR' }).format(amount);
};

export function JournalEntryForm() {
    const t = useTranslations("AdminJournalEntriesPage.form");
    const tValidation = useTranslations("AdminJournalEntriesPage.validation");
    const tShared = useTranslations("Shared"); // For shared translations like 'Cancel'
    const router = useRouter();
    const queryClient = useQueryClient();

    const formSchema = createJournalEntrySchema(tValidation);

    const form = useForm<CreateJournalEntryInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            entryDate: new Date(),
            description: "",
            referenceNumber: "",
            lines: [
                { chartOfAccountId: "", type: "DEBIT", amount: 0, description: "" },
                { chartOfAccountId: "", type: "CREDIT", amount: 0, description: "" },
            ],
        },
    });

    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: "lines",
    });

    const createMutation = useMutation({
        mutationFn: createJournalEntry,
        onSuccess: (data) => {
            toast.success(t("addSuccess"));
            queryClient.invalidateQueries({ queryKey: ["journalEntries"] });
            // Redirect back to the main journal entries page after successful creation
            router.push("/admin/accounting/journal-entries");
        },
        onError: (error) => {
            toast.error(t("addError"), { description: getErrorMessage(error) });
        },
    });

    function onSubmit(values: CreateJournalEntryInput) {
        const requestData = {
            ...values,
            // Format date to ISO string YYYY-MM-DD for the API
            entryDate: format(values.entryDate, "yyyy-MM-dd"),
            // Ensure amounts are numbers
            lines: values.lines.map(line => ({
                ...line,
                amount: Number(line.amount) || 0, // Ensure amount is a number
            })),
        };
        console.log("Submitting Journal Entry:", requestData);
        createMutation.mutate(requestData);
    }

    // Calculate totals for display
    const lines = form.watch("lines");
    const totalDebits = lines.filter(l => l.type === 'DEBIT').reduce((sum, l) => sum + (Number(l.amount) || 0), 0);
    const totalCredits = lines.filter(l => l.type === 'CREDIT').reduce((sum, l) => sum + (Number(l.amount) || 0), 0);
    const balance = totalDebits - totalCredits;

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                        control={form.control}
                        name="entryDate"
                        render={({ field }) => (
                            <FormItem className="flex flex-col">
                                <FormLabel>{t("entryDateLabel")}</FormLabel>
                                <DatePicker
                                    date={field.value}
                                    setDate={field.onChange}
                                    placeholder={t("entryDateLabel")} // Use label as placeholder
                                    disabled={createMutation.isPending}
                                />
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>{t("descriptionLabel")}</FormLabel>
                                <FormControl>
                                    <Input placeholder={t("descriptionLabel")} {...field} disabled={createMutation.isPending} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="referenceNumber"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>{t("referenceLabel")}</FormLabel>
                                <FormControl>
                                    <Input placeholder={t("referenceLabel")} {...field} disabled={createMutation.isPending} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Journal Lines Section */}
                <div>
                    <h3 className="text-lg font-medium mb-2">{t("linesLabel")}</h3>
                    <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[300px]">{t("accountLabel")}</TableHead>
                                    <TableHead className="w-[120px]">{t("typeLabel")}</TableHead>
                                    <TableHead className="w-[150px] text-right">{t("amountLabel")}</TableHead>
                                    <TableHead>{t("lineDescriptionLabel")}</TableHead>
                                    <TableHead className="w-[50px]"></TableHead> {/* Action column */}
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {fields.map((field, index) => (
                                    <TableRow key={field.id}>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`lines.${index}.chartOfAccountId`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <AccountCombobox
                                                            value={field.value}
                                                            onChange={field.onChange}
                                                            disabled={createMutation.isPending}
                                                        />
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`lines.${index}.type`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <Select
                                                            onValueChange={field.onChange}
                                                            defaultValue={field.value}
                                                            disabled={createMutation.isPending}
                                                        >
                                                            <FormControl>
                                                                <SelectTrigger>
                                                                    <SelectValue placeholder={t("selectTypePlaceholder")} />
                                                                </SelectTrigger>
                                                            </FormControl>
                                                            <SelectContent>
                                                                <SelectItem value="DEBIT">{t("debitLabel")}</SelectItem>
                                                                <SelectItem value="CREDIT">{t("creditLabel")}</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`lines.${index}.amount`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Input
                                                                type="number"
                                                                step="0.01" // Allow decimals
                                                                placeholder={t("amountLabel")}
                                                                {...field}
                                                                onChange={event => field.onChange(+event.target.value)} // Ensure value is number
                                                                className="text-right"
                                                                disabled={createMutation.isPending}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <FormField
                                                control={form.control}
                                                name={`lines.${index}.description`}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <Input placeholder={t("lineDescriptionLabel")} {...field} disabled={createMutation.isPending} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            {fields.length > 2 && ( // Only allow removal if more than 2 lines
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => remove(index)}
                                                    disabled={createMutation.isPending}
                                                    aria-label={t("removeLine")}
                                                >
                                                    <Trash2 className="h-4 w-4 text-destructive" />
                                                </Button>
                                            )}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() => append({ chartOfAccountId: "", type: "DEBIT", amount: 0, description: "" })}
                        disabled={createMutation.isPending}
                    >
                        {t("addLine")}
                    </Button>
                    {/* Display Totals and Balance */}
                    <div className="mt-4 flex justify-end space-x-8 border-t pt-4">
                         <div className="text-right">
                            <p className="text-sm text-muted-foreground">{t("totalDebits")}</p>
                            <p className="font-semibold">{formatCurrency(totalDebits)}</p>
                        </div>
                         <div className="text-right">
                            <p className="text-sm text-muted-foreground">{t("totalCredits")}</p>
                            <p className="font-semibold">{formatCurrency(totalCredits)}</p>
                        </div>
                         <div className="text-right">
                            <p className="text-sm text-muted-foreground">{t("balance")}</p>
                            <p className={`font-semibold ${Math.abs(balance) > 0.001 ? 'text-destructive' : 'text-success'}`}>
                                {formatCurrency(balance)}
                            </p>
                        </div>
                    </div>
                     {/* Display overall form error for balance */}
                     {form.formState.errors.lines?.root && (
                        <p className="text-sm font-medium text-destructive mt-2 text-right">
                            {form.formState.errors.lines.root.message}
                        </p>
                    )}
                     {form.formState.errors.lines && !form.formState.errors.lines?.root && typeof form.formState.errors.lines === 'object' && (
                         <p className="text-sm font-medium text-destructive mt-2 text-right">
                             {tValidation.atLeastTwoLines} {/* Or a more generic line error message */}
                         </p>
                     )}
                </div>


                <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => router.back()} disabled={createMutation.isPending}>
                        {tShared("confirmationDialog.cancel")} {/* Using shared cancel */}
                    </Button>
                    <Button type="submit" disabled={createMutation.isPending}>
                        {createMutation.isPending ? t("saving") : t("save")}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
