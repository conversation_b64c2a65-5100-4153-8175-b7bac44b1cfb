"use client";

import * as React from "react";
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar";
import { getChartOfAccounts } from "@/lib/api/admin/accounting/chart-of-accounts";
import { ChartOfAccountDto } from "@/lib/dto/admin/accounting/chart-of-accounts.dto";
import { Skeleton } from "@/components/ui/skeleton";
import { getColumns } from "./columns"; // Import getColumns here

// Removed columns from props interface
interface DataTableProps<TData, TValue> {
    // columns: ColumnDef<TData, TValue>[]; // Removed prop
}

// Removed columns from function signature
export function ChartOfAccountsDataTable<TData extends ChartOfAccountDto, TValue>({}: DataTableProps<TData, TValue>) {
    const tPage = useTranslations("AdminChartOfAccountsPage"); // Renamed to avoid conflict
    const tTable = useTranslations("AdminChartOfAccountsPage.table"); // Get table translations
    const tShared = useTranslations("Shared"); // Get shared translations

    // Pass translation functions to getColumns inside useMemo
    const columns = React.useMemo(() => getColumns(tTable, tShared), [tTable, tShared]);

    const [rowSelection, setRowSelection] = React.useState({});
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [pagination, setPagination] = React.useState({
        pageIndex: 0, //initial page index
        pageSize: 10, //default page size
    });
    const [globalFilter, setGlobalFilter] = React.useState(''); // For general search
    const [showActiveOnly, setShowActiveOnly] = React.useState(true); // State for the toggle

    // Debounce search term
    const debouncedGlobalFilter = useDebounce(globalFilter, 300);

    // Update queryKey to include all search fields based on the debounced filter
    const queryKey = [
        "chart-of-accounts",
        {
            page: pagination.pageIndex,
            size: pagination.pageSize,
            sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
            // Only include nameEn in the key for filtering
            nameEn: debouncedGlobalFilter,
            // nameAr: debouncedGlobalFilter, // Removed
            // accountNumber: debouncedGlobalFilter, // Removed
            activeOnly: showActiveOnly,
        }
    ];

    const { data, isLoading, isError, error } = useQuery({
        queryKey, // Use the simplified queryKey
        queryFn: () => getChartOfAccounts({
            page: pagination.pageIndex,
            size: pagination.pageSize,
            sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
            // Only pass the search term to nameEn
            nameEn: debouncedGlobalFilter, // todo remove following comments
            // nameAr: debouncedGlobalFilter, // Removed
            // accountNumber: debouncedGlobalFilter, // Removed
            activeOnly: showActiveOnly,
        }),
        placeholderData: (previousData) => previousData, // Keep previous data while loading new
        // Consider adding keepPreviousData: true for smoother pagination
    });

    const table = useReactTable({
        data: data?.content ?? [], // Use fetched data or empty array
        columns,
        state: {
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
            pagination,
            globalFilter, // Use client-side global filter state
        },
        enableRowSelection: true,
        manualPagination: true, // Tell the table we're handling pagination manually
        manualSorting: true, // Tell the table we're handling sorting manually
        manualFiltering: true, // Tell the table we're handling filtering manually (if server-side)
        pageCount: data?.totalPages ?? -1, // Provide the total page count from API response
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onPaginationChange: setPagination, // Connect pagination state setter
        onGlobalFilterChange: setGlobalFilter, // Connect global filter state setter
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(), // Needed for client-side filtering
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    });

    // Loading and Error States
    const renderTableContent = () => {
        if (isLoading) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        <div className="space-y-2">
                            {Array.from({ length: 5 }).map((_, i) => (
                                <Skeleton key={i} className="h-8 w-full" />
                            ))}
                        </div>
                    </TableCell>
                </TableRow>
            );
        }

        if (isError) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center text-destructive">
                        {tPage("errorLoading", { error: error?.message || 'Unknown error' })} {/* Use tPage */}
                    </TableCell>
                </TableRow>
            );
        }

        if (!table.getRowModel().rows?.length) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        {tPage("noResults")} {/* Use tPage */}
                    </TableCell>
                </TableRow>
            );
        }

        return table.getRowModel().rows.map((row) => (
            <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
            >
                {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                ))}
            </TableRow>
        ));
    };


    return (
        <div className="space-y-4">
            <DataTableToolbar
                table={table}
                globalFilter={globalFilter}
                setGlobalFilter={setGlobalFilter}
                showActiveOnly={showActiveOnly}
                setShowActiveOnly={setShowActiveOnly}
            />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead key={header.id} colSpan={header.colSpan}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {renderTableContent()}
                    </TableBody>
                </Table>
            </div>
             {/* Pass translations scoped specifically for pagination */}
            <DataTablePagination table={table} t={useTranslations('Shared.dataTable.pagination')} />
        </div>
    );
}

// Debounce hook
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

    React.useEffect(() => {
        const timer = setTimeout(() => setDebouncedValue(value), delay);
        return () => clearTimeout(timer);
    }, [value, delay]);

    return debouncedValue;
}
