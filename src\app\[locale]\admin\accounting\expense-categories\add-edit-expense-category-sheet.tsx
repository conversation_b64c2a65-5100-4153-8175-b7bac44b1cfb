"use client";

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
    Sheet,
    Sheet<PERSON>ontent,
    SheetHeader,
    SheetTitle,
    SheetDescription,
    SheetFooter,
    SheetClose,
} from "@/components/ui/sheet";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea"; // Use Textarea for descriptions
import { Skeleton } from "@/components/ui/skeleton";
import { AccountCombobox } from "@/components/ui/combobox-coa"; // Use the CoA combobox
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"; // Use Popover
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"; // Use Command
import { Check, ChevronsUpDown, X as ClearIcon } from "lucide-react"; // Icons
import { cn } from "@/lib/utils"; // Utility for class names

import {
    createExpenseCategory,
    getExpenseCategoryById,
    updateExpenseCategory,
    getAllExpenseCategoriesList, // API to fetch list for parent dropdown
} from "@/lib/api/admin/accounting/expense-categories";
import {
    CreateExpenseCategoryInput,
    UpdateExpenseCategoryInput,
    createExpenseCategorySchema,
    updateExpenseCategorySchema,
} from "@/lib/schemas/admin/accounting/expense-categories";
import { ExpenseCategoryDto } from "@/lib/dto/admin/accounting/expense-categories.dto";
// Removed getChangedFields import

interface AddEditExpenseCategorySheetProps {
    categoryId?: string; // ID for editing, undefined for adding
    isOpen: boolean;
    onOpenChange: (isOpen: boolean) => void;
    onSuccess?: () => void; // Optional callback on successful add/edit
    children?: React.ReactNode; // To allow triggering the sheet with a button
}

export function AddEditExpenseCategorySheet({
    categoryId,
    isOpen,
    onOpenChange,
    onSuccess,
    children,
}: AddEditExpenseCategorySheetProps) {
    const t = useTranslations("AdminExpenseCategoriesPage");
    const isEditMode = !!categoryId;
    const queryClient = useQueryClient();

    // Fetch existing category data if in edit mode
    const { data: existingCategory, isLoading: isLoadingCategory, isError: isErrorCategory } = useQuery({
        queryKey: ["expense-categories", categoryId],
        queryFn: () => getExpenseCategoryById(categoryId!),
        enabled: isEditMode && isOpen, // Only fetch when the sheet is open in edit mode
    });

    // Fetch list of categories for parent selection
    const { data: parentCategories, isLoading: isLoadingParents } = useQuery({
        queryKey: ["expense-categories-list"],
        queryFn: getAllExpenseCategoriesList,
        enabled: isOpen, // Fetch when sheet opens
        select: (data) => data.map(cat => ({ value: cat.id, label: `${cat.nameEn} / ${cat.nameAr}` })),
        placeholderData: [],
    });

    // Get translations scoped to the specific dialog sections
    const tAddValidation = useTranslations("AdminExpenseCategoriesPage.AddDialog");
    const tEditValidation = useTranslations("AdminExpenseCategoriesPage.EditDialog");

    const validationSchema = isEditMode
        ? updateExpenseCategorySchema(tEditValidation) // Pass t scoped to EditDialog
        : createExpenseCategorySchema(tAddValidation); // Pass t scoped to AddDialog

    type FormValues = z.infer<typeof validationSchema>;

    const form = useForm<FormValues>({
        resolver: zodResolver(validationSchema),
        defaultValues: isEditMode
            ? undefined // Will be set by useEffect when data loads
            : {
                nameEn: "",
                nameAr: "",
                descriptionEn: "",
                descriptionAr: "",
                parentCategoryId: null,
                expenseAccountId: undefined, // Initialize as undefined
            },
    });

    // Effect to reset form when editing data loads or when switching modes
    useEffect(() => {
        if (isOpen) {
            if (isEditMode) {
                if (existingCategory) {
                    form.reset({
                        nameEn: existingCategory.nameEn,
                        nameAr: existingCategory.nameAr,
                        descriptionEn: existingCategory.descriptionEn ?? "",
                        descriptionAr: existingCategory.descriptionAr ?? "",
                        parentCategoryId: existingCategory.parentCategory?.id ?? null,
                        expenseAccountId: existingCategory.expenseAccount?.id,
                    });
                } else if (isErrorCategory) {
                    toast.error(t("EditDialog.errorLoadingCategory"));
                    onOpenChange(false); // Close sheet on error
                }
            } else {
                form.reset({ // Reset to default for add mode
                    nameEn: "",
                    nameAr: "",
                    descriptionEn: "",
                    descriptionAr: "",
                    parentCategoryId: null,
                    expenseAccountId: undefined,
                });
            }
        }
    }, [isOpen, isEditMode, existingCategory, isErrorCategory, form, onOpenChange, t]);


    const mutationFn = isEditMode ? updateExpenseCategory : createExpenseCategory;

    const mutation = useMutation({
        mutationFn: async (data: CreateExpenseCategoryInput | UpdateExpenseCategoryInput) => {
            if (isEditMode) {
                // Send the full data object for update as requested
                return mutationFn(categoryId!, data as UpdateExpenseCategoryInput);
            } else {
                // Send the full data object for create
                return mutationFn(data as CreateExpenseCategoryRequest);
            }
        },
        onSuccess: (data) => {
            const categoryName = data?.nameEn ?? 'Category';
            toast.success(
                isEditMode
                    ? t("EditDialog.successToast", { name: categoryName })
                    : t("AddDialog.successToast", { name: categoryName })
            );
            // Invalidate relevant queries to refresh data
            queryClient.invalidateQueries({ queryKey: ["expense-categories"] });
            queryClient.invalidateQueries({ queryKey: ["expense-categories-list"] });
            if (isEditMode) {
                queryClient.invalidateQueries({ queryKey: ["expense-categories", categoryId] });
            }
            if (onSuccess) {
                onSuccess(); // Call the success callback
            } else {
                onOpenChange(false); // Close sheet by default
            }
        },
        onError: (error: any) => {
            const errorMessage = error?.message || (isEditMode ? t("EditDialog.errorToast") : t("AddDialog.errorToast"));
            toast.error(errorMessage);
        },
    });

    const onSubmit = (values: FormValues) => {
        // Ensure nulls are sent correctly for optional fields if empty
        const dataToSubmit = {
            ...values,
            descriptionEn: values.descriptionEn || null,
            descriptionAr: values.descriptionAr || null,
            parentCategoryId: values.parentCategoryId || null,
        };
        mutation.mutate(dataToSubmit);
    };

    const renderFormContent = () => {
        if (isEditMode && isLoadingCategory) {
            return (
                <div className="space-y-4 px-4 py-6">
                    <Skeleton className="h-8 w-1/3" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-8 w-1/3" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-8 w-1/3" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-8 w-1/3" />
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-8 w-1/3" />
                    <Skeleton className="h-20 w-full" />
                </div>
            );
        }

        return (
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 px-4 py-6">
                    {/* Name EN */}
                    <FormField
                        control={form.control}
                        name="nameEn"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>{t("AddDialog.nameEnLabel")}</FormLabel>
                                <FormControl>
                                    <Input placeholder={t("AddDialog.nameEnPlaceholder")} {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Name AR */}
                    <FormField
                        control={form.control}
                        name="nameAr"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>{t("AddDialog.nameArLabel")}</FormLabel>
                                <FormControl>
                                    <Input dir="rtl" placeholder={t("AddDialog.nameArPlaceholder")} {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Expense Account */}
                    <FormField
                        control={form.control}
                        name="expenseAccountId"
                        render={({ field }) => (
                            <FormItem className="flex flex-col">
                                <FormLabel>{t("AddDialog.expenseAccountLabel")}</FormLabel>
                                <AccountCombobox
                                    value={field.value}
                                    onChange={field.onChange}
                                    placeholder={t("AddDialog.expenseAccountPlaceholder")}
                                    searchPlaceholder={t("AddDialog.searchAccountPlaceholder")}
                                    noResultsText={t("AddDialog.noAccountFound")}
                                    disabled={mutation.isPending}
                                    filterCategory="EXPENSE" // <-- Add this line
                                />
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Parent Category */}
                    <FormField
                        control={form.control}
                        name="parentCategoryId"
                        render={({ field }) => (
                            <FormItem className="flex flex-col">
                                <FormLabel>{t("AddDialog.parentCategoryLabel")}</FormLabel>
                                <Popover>
                                    <PopoverTrigger asChild>
                                        <FormControl>
                                            <Button
                                                variant="outline"
                                                role="combobox"
                                                className={cn(
                                                    "w-full justify-between",
                                                    !field.value && "text-muted-foreground"
                                                )}
                                                disabled={isLoadingParents || mutation.isPending}
                                            >
                                                {field.value
                                                    ? parentCategories?.find(
                                                        (cat) => cat.value === field.value
                                                    )?.label
                                                    : t("AddDialog.parentCategoryPlaceholder")}
                                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                            </Button>
                                        </FormControl>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
                                        <Command>
                                            <CommandInput placeholder={t("AddDialog.searchParentPlaceholder")} />
                                            <CommandList>
                                                <CommandEmpty>{t("AddDialog.noParentFound")}</CommandEmpty>
                                                <CommandGroup>
                                                    {/* Option to clear selection */}
                                                    <CommandItem
                                                        value="clear-selection" // Unique value for the clear option
                                                        onSelect={() => {
                                                            field.onChange(null); // Set field value to null
                                                            // Optionally close popover here if needed
                                                        }}
                                                        className="text-muted-foreground italic"
                                                    >
                                                        <Check
                                                            className={cn(
                                                                "mr-2 h-4 w-4",
                                                                !field.value ? "opacity-100" : "opacity-0"
                                                            )}
                                                        />
                                                        {t("AddDialog.noParentValue")}
                                                    </CommandItem>
                                                    {/* Category options */}
                                                    {(parentCategories ?? []).map((cat) => (
                                                        <CommandItem
                                                            value={cat.label} // Use label for search matching
                                                            key={cat.value}
                                                            onSelect={() => {
                                                                field.onChange(cat.value);
                                                                // Optionally close popover here if needed
                                                            }}
                                                        >
                                                            <Check
                                                                className={cn(
                                                                    "mr-2 h-4 w-4",
                                                                    cat.value === field.value
                                                                        ? "opacity-100"
                                                                        : "opacity-0"
                                                                )}
                                                            />
                                                            {cat.label}
                                                        </CommandItem>
                                                    ))}
                                                </CommandGroup>
                                            </CommandList>
                                        </Command>
                                    </PopoverContent>
                                </Popover>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Description EN */}
                    <FormField
                        control={form.control}
                        name="descriptionEn"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>{t("AddDialog.descriptionEnLabel")}</FormLabel>
                                <FormControl>
                                    <Textarea
                                        placeholder={t("AddDialog.descriptionEnPlaceholder")}
                                        {...field}
                                        value={field.value ?? ""} // Handle null value
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Description AR */}
                    <FormField
                        control={form.control}
                        name="descriptionAr"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>{t("AddDialog.descriptionArLabel")}</FormLabel>
                                <FormControl>
                                    <Textarea
                                        dir="rtl"
                                        placeholder={t("AddDialog.descriptionArPlaceholder")}
                                        {...field}
                                        value={field.value ?? ""} // Handle null value
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <SheetFooter className="pt-4">
                        <SheetClose asChild>
                            <Button type="button" variant="outline" disabled={mutation.isPending}>
                                {t("AddDialog.cancelButton")}
                            </Button>
                        </SheetClose>
                        {/* Removed !form.formState.isDirty check as we always submit full data */}
                        <Button type="submit" disabled={mutation.isPending}>
                            {mutation.isPending
                                ? t(isEditMode ? "EditDialog.savingButton" : "AddDialog.savingButton")
                                : t(isEditMode ? "EditDialog.saveButton" : "AddDialog.saveButton")}
                        </Button>
                    </SheetFooter>
                </form>
            </Form>
        );
    };

    return (
        <Sheet open={isOpen} onOpenChange={onOpenChange}>
            {children && <div onClick={() => onOpenChange(true)}>{children}</div>} {/* Optional trigger */}
            <SheetContent className="sm:max-w-lg">
                <SheetHeader>
                    <SheetTitle>
                        {isEditMode ? t("EditDialog.title", { name: existingCategory?.nameEn ?? '...' }) : t("AddDialog.title")}
                    </SheetTitle>
                    <SheetDescription>
                        {isEditMode ? t("EditDialog.description", { name: existingCategory?.nameEn ?? '...' }) : t("AddDialog.description")}
                    </SheetDescription>
                </SheetHeader>
                {renderFormContent()}
            </SheetContent>
        </Sheet>
    );
}
