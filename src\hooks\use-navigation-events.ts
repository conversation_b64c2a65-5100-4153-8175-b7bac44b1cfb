"use client";

import { usePathname, useRouter } from "@/i18n/navigation";
import { useCallback, useEffect, useState } from "react";

/**
 * Custom hook to track navigation events in Next.js App Router
 * @returns Object containing isNavigating state
 */
export function useNavigationEvents() {
  const [isNavigating, setIsNavigating] = useState(false);
  const pathname = usePathname();
  
  // Reset navigation state when pathname changes (navigation completed)
  useEffect(() => {
    setIsNavigating(false);
  }, [pathname]);
  
  // Set up event listeners for navigation start
  useEffect(() => {
    // Function to handle navigation start
    const handleNavigationStart = () => {
      setIsNavigating(true);
    };
    
    // Listen for link clicks that might trigger navigation
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const anchor = target.closest("a");
      
      // Only trigger for internal links that will cause navigation
      if (
        anchor && 
        anchor.href && 
        !anchor.target && 
        anchor.href.startsWith(window.location.origin) &&
        anchor.href !== window.location.href
      ) {
        handleNavigationStart();
      }
    };
    
    // Listen for form submissions that might trigger navigation
    const handleFormSubmit = () => {
      handleNavigationStart();
    };
    
    // Add event listeners
    document.addEventListener("click", handleLinkClick);
    document.addEventListener("submit", handleFormSubmit);
    
    return () => {
      // Clean up event listeners
      document.removeEventListener("click", handleLinkClick);
      document.removeEventListener("submit", handleFormSubmit);
    };
  }, []);
  
  return { isNavigating };
}
