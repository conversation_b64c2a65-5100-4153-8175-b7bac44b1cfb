import * as React from 'react';

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { Button } from '@/components/ui/button';
import { useTranslations } from 'next-intl';

export interface ConfirmOptions {
    title: string;
    message: React.ReactNode; // Allow JSX for richer messages
    confirmText?: string;
    cancelText?: string;
    confirmVariant?: React.ComponentProps<typeof Button>['variant'];
}

interface UseDeleteConfirmationReturn {
    confirm: (options: ConfirmOptions) => Promise<boolean>;
    ConfirmDialog: React.FC; // Component to render the dialog
}

/**
 * Hook to manage a confirmation dialog.
 * Provides a `confirm` function to open the dialog and returns a promise resolving to true/false.
 * Also provides the `ConfirmDialog` component that needs to be rendered somewhere in the tree.
 */
export function useDeleteConfirmation(): UseDeleteConfirmationReturn {
    const [isOpen, setIsOpen] = React.useState(false);
    const [options, setOptions] = React.useState<ConfirmOptions | null>(null);
    const [resolvePromise, setResolvePromise] = React.useState<((value: boolean) => void) | null>(null);
    const tShared = useTranslations('Shared.confirmationDialog'); // Default translations

    const confirm = (opts: ConfirmOptions): Promise<boolean> => {
        return new Promise((resolve) => {
            setOptions({
                title: opts.title,
                message: opts.message,
                confirmText: opts.confirmText ?? tShared('delete'), // Default confirm text
                cancelText: opts.cancelText ?? tShared('cancel'),   // Default cancel text
                confirmVariant: opts.confirmVariant ?? 'destructive', // Default variant
            });
            setResolvePromise(() => resolve); // Store the resolve function
            setIsOpen(true);
        });
    };

    const handleConfirm = () => {
        if (resolvePromise) {
            resolvePromise(true);
        }
        setIsOpen(false);
        setOptions(null);
        setResolvePromise(null);
    };

    const handleCancel = () => {
        if (resolvePromise) {
            resolvePromise(false);
        }
        setIsOpen(false);
        setOptions(null);
        setResolvePromise(null);
    };

    const ConfirmDialog: React.FC = () => (
        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{options?.title}</AlertDialogTitle>
                    <AlertDialogDescription>
                        {options?.message}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel onClick={handleCancel}>
                        {options?.cancelText}
                    </AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleConfirm}
                        // Apply variant dynamically, default to destructive
                        className={options?.confirmVariant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
                        // If you need other variants, you might need more complex logic or pass the variant prop directly
                        // variant={options?.confirmVariant} // This might not work directly depending on AlertDialogAction implementation
                    >
                        {options?.confirmText}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    return { confirm, ConfirmDialog };
}
