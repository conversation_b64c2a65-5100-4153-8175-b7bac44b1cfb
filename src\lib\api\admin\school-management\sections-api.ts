import { CreateSectionPayload, SectionDto, UpdateSectionPayload } from '@/lib/dto/admin/school-management/sections.dto';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { API_ENDPOINTS } from '@/lib/constants';
import { Page } from '@/lib/dto/common.dto';
import { fetchWithAuth } from '@/lib/fetch-with-auth';

const SECTIONS_API_URL = `${API_ENDPOINTS.ADMIN_SECTIONS}`;

// Query keys
export const sectionQueryKeys = {
  all: ['sections'] as const,
  lists: () => [...sectionQueryKeys.all, 'list'] as const,
  list: (filters: { academicYearId?: string, branchId?: string, gradeLevelId?: string, page?: number, size?: number }) => [...sectionQueryKeys.lists(), filters] as const,
  details: () => [...sectionQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...sectionQueryKeys.details(), id] as const,
};

// Hook to fetch all sections with optional filters and pagination
export const useGetSections = (
  filters: { academicYearId?: string, branchId?: string, gradeLevelId?: string, page?: number, size?: number } = {},
  enabled: boolean = true
) => {
  const queryParams = new URLSearchParams();
  if (filters.academicYearId) queryParams.append('academicYearId', filters.academicYearId);
  if (filters.branchId) queryParams.append('branchId', filters.branchId);
  if (filters.gradeLevelId) queryParams.append('gradeLevelId', filters.gradeLevelId);
  if (filters.page !== undefined) queryParams.append('page', filters.page.toString());
  if (filters.size !== undefined) queryParams.append('size', filters.size.toString());

  const endpoint = filters.page !== undefined ? `${SECTIONS_API_URL}/all?${queryParams.toString()}` : `${SECTIONS_API_URL}?${queryParams.toString()}`;

  return useQuery<Page<SectionDto> | SectionDto[], Error>({
    queryKey: sectionQueryKeys.list(filters),
    queryFn: async () => {
      const response = await fetchWithAuth(endpoint);
      if (!response.ok) {
        throw new Error('Failed to fetch sections');
      }
      return response.json();
    },
    enabled,
  });
};

// Hook to fetch a single section by ID
export const useGetSectionById = (id: string | undefined, enabled: boolean = true) => {
  return useQuery<SectionDto, Error>({
    queryKey: sectionQueryKeys.detail(id!),
    queryFn: async () => {
      if (!id) throw new Error('Section ID is required');
      const response = await fetchWithAuth(`${SECTIONS_API_URL}/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch section');
      }
      return response.json();
    },
    enabled: !!id && enabled, // Only run if id is provided and enabled is true
  });
};

// Hook to create a new section
export const useCreateSection = () => {
  const queryClient = useQueryClient();
  return useMutation<SectionDto, Error, CreateSectionPayload>({
    mutationFn: async (payload) => {
      const response = await fetchWithAuth(SECTIONS_API_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to create section' }));
        throw new Error(errorData.message || 'Failed to create section');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: sectionQueryKeys.lists() });
    },
  });
};

// Hook to update an existing section
export const useUpdateSection = () => {
  const queryClient = useQueryClient();
  return useMutation<SectionDto, Error, { id: string; payload: UpdateSectionPayload }>({
    mutationFn: async ({ id, payload }) => {
      const response = await fetchWithAuth(`${SECTIONS_API_URL}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to update section' }));
        throw new Error(errorData.message || 'Failed to update section');
      }
      return response.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: sectionQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: sectionQueryKeys.detail(variables.id) });
    },
  });
};

// Hook to delete a section
export const useDeleteSection = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      const response = await fetchWithAuth(`${SECTIONS_API_URL}/${id}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to delete section' }));
        throw new Error(errorData.message || 'Failed to delete section');
      }
    },
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({ queryKey: sectionQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: sectionQueryKeys.detail(id) });
      // Optimistically remove the item from lists if possible or refetch
    },
  });
};
