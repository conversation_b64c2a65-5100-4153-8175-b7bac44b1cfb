import { PageableObject, SortObject } from "@/lib/dto/common.dto";

// Corresponds to SimpleExpenseCategoryDto in API
export interface SimpleExpenseCategoryDto {
    id: string;
    nameEn: string;
    nameAr: string;
}

// Corresponds to ExpenseCategoryDto in API
export interface ExpenseCategoryDto {
    id: string;
    nameEn: string;
    nameAr: string;
    descriptionEn?: string;
    descriptionAr?: string;
    parentCategory?: SimpleExpenseCategoryDto; // Optional parent
    expenseAccount: SimpleChartOfAccountDto; // Added based on updated API
    createdDate: string; // ISO Date string
    lastModifiedDate: string; // ISO Date string
}

// Import SimpleChartOfAccountDto
import { SimpleChartOfAccountDto } from "@/lib/dto/admin/accounting/chart-of-accounts.dto";

// Corresponds to CreateExpenseCategoryRequest in API
export interface CreateExpenseCategoryRequest {
    nameEn: string;
    nameAr: string;
    descriptionEn?: string | null; // Allow null to clear
    descriptionAr?: string | null; // Allow null to clear
    parentCategoryId?: string | null; // Allow null for no parent
    expenseAccountId: string; // Required based on updated API
}

// Corresponds to UpdateExpenseCategoryRequest in API
export interface UpdateExpenseCategoryRequest {
    nameEn?: string;
    nameAr?: string;
    descriptionEn?: string | null; // Allow null to clear
    descriptionAr?: string | null; // Allow null to clear
    parentCategoryId?: string | null; // Allow null for no parent
    expenseAccountId?: string; // Required for update based on updated API
}

// For fetching paginated data via GET /api/v1/accounting/expense-categories
export interface GetExpenseCategoriesParams {
    page?: number;
    size?: number;
    sort?: string[];
    searchTerm?: string; // Based on API spec parameter name
}

// Corresponds to PageExpenseCategoryDto in API
export interface PageExpenseCategoryDto {
    totalElements: number;
    totalPages: number;
    size: number;
    content: ExpenseCategoryDto[];
    number: number;
    sort: SortObject[];
    numberOfElements: number;
    pageable: PageableObject;
    first: boolean;
    last: boolean;
    empty: boolean;
}
