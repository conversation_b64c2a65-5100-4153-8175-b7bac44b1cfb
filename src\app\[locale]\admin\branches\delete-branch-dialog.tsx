import React from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { deleteBranch } from '@/lib/api/admin/branches';
import { BranchDto } from '@/lib/dto/admin/branch.dto';
import { getErrorMessage } from '@/lib/utils'; // Corrected path

interface DeleteBranchDialogProps {
    branch: BranchDto;
    children: React.ReactNode; // To wrap the trigger button/icon
    onSuccess?: () => void; // Optional callback on successful deletion
}

export function DeleteBranchDialog({ branch, children, onSuccess }: DeleteBranchDialogProps) {
    const t = useTranslations('AdminBranchesPage');
    const tShared = useTranslations('Shared.confirmationDialog');
    const queryClient = useQueryClient();
    const [isOpen, setIsOpen] = React.useState(false);

    const mutation = useMutation({
        mutationFn: () => deleteBranch(branch.id),
        onSuccess: () => {
            toast.success(t('table.deleteSuccessToast', { name: branch.nameEn }));
            queryClient.invalidateQueries({ queryKey: ['branches'] }); // Invalidate cache
            setIsOpen(false); // Close dialog on success
            onSuccess?.(); // Call optional success callback
        },
        onError: (error) => {
            toast.error(t('table.deleteErrorToast', { error: getErrorMessage(error) }));
        },
    });

    const handleDelete = () => {
        mutation.mutate();
    };

    return (
        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
            <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{tShared('title')}</AlertDialogTitle>
                    <AlertDialogDescription>
                        {tShared('deleteMessage', { item: t('table.branchItem') })}
                        <br />
                        <strong>{branch.nameEn} / {branch.nameAr}</strong>
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel disabled={mutation.isPending}>{tShared('cancel')}</AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleDelete}
                        disabled={mutation.isPending}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                        {mutation.isPending ? tShared('deleting') : tShared('delete')}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
