import { Page } from "@/lib/dto/page";

/**
 * Data Transfer Object for Fee Category details.
 * Based on GET /api/v1/accounting/fee-categories/{id} response schema.
 */
export interface FeeCategoryDto {
    id: string;
    nameEn: string;
    nameAr: string;
    descriptionEn?: string; // Optional based on schema
    descriptionAr?: string; // Optional based on schema
    createdDate: string; // ISO Date string
    lastModifiedDate: string; // ISO Date string
}

/**
 * Simplified DTO for Fee Category, often used in lists or dropdowns.
 * Based on FeeCategoryDto structure.
 */
export interface SimpleFeeCategoryDto {
    id: string;
    nameEn: string;
    nameAr: string;
}


/**
 * Request body for creating a new Fee Category.
 * Based on POST /api/v1/accounting/fee-categories request schema.
 */
export interface CreateFeeCategoryRequest {
    nameEn: string;
    nameAr: string;
    descriptionEn?: string;
    descriptionAr?: string;
}

/**
 * Request body for updating an existing Fee Category.
 * Based on PUT /api/v1/accounting/fee-categories/{id} request schema.
 */
export interface UpdateFeeCategoryRequest {
    nameEn?: string;
    nameAr?: string;
    descriptionEn?: string;
    descriptionAr?: string;
}

/**
 * Represents a paginated response for Fee Categories.
 * Based on GET /api/v1/accounting/fee-categories response schema.
 */
export type PageFeeCategoryDto = Page<FeeCategoryDto>;

/**
 * Parameters for fetching Fee Categories.
 * Based on GET /api/v1/accounting/fee-categories query parameters.
 */
export interface GetFeeCategoriesParams {
    page?: number;
    size?: number;
    sort?: string[];
    // Add search parameters if supported by API (not explicitly listed, but common)
    search?: string; // Assuming a general search param might exist
}
