import { <PERSON><PERSON><PERSON><PERSON>, PageHeaderDescription, PageHeaderHeading } from "@/components/ui/page-header";
import { AiReportsDataTable } from "./data-table";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default async function AiReportsPage() {
  const t = await getTranslations("AiReportsPage");

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
        <CardDescription>{t("description")}</CardDescription>
      </CardHeader>
      <CardContent>
        <AiReportsDataTable />
      </CardContent>
    </Card>
  );
}
