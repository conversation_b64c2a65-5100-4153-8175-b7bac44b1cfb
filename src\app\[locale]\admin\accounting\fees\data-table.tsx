"use client";

import * as React from "react";
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
    PaginationState,
} from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar"; // Use the specific toolbar
import { getFees } from "@/lib/api/admin/accounting/fees"; // Use the specific API function
import { FeeDto, GetFeesParams } from "@/lib/dto/admin/accounting/fees.dto"; // Use the specific DTO
import { getColumns } from "./columns"; // Use the specific columns
import { Skeleton } from "@/components/ui/skeleton"; // For loading state

// Debounce hook (consider moving to a shared utils file if used elsewhere)
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

    React.useEffect(() => {
        const timer = setTimeout(() => setDebouncedValue(value), delay);
        return () => clearTimeout(timer);
    }, [value, delay]);

    return debouncedValue;
}


export function FeesDataTable() {
    const t = useTranslations("AdminFeesPage");
    const tShared = useTranslations("Shared");

    const [rowSelection, setRowSelection] = React.useState({});
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [sorting, setSorting] = React.useState<SortingState>([]);
     const [{ pageIndex, pageSize }, setPagination] = React.useState<PaginationState>({
        pageIndex: 0,
        pageSize: 10,
    });

    // Extract filter values for debouncing or direct use
    const academicYearFilter = columnFilters.find(f => f.id === 'academicYearId')?.value as string ?? '';
    const categoryFilter = columnFilters.find(f => f.id === 'feeCategory')?.value as string ?? '';
    // Add active filter if needed
    // const activeFilter = columnFilters.find(f => f.id === 'active')?.value as string[] | undefined;

    // Debounce the academic year filter if needed (optional, depends on API performance)
    const debouncedAcademicYear = useDebounce(academicYearFilter, 300);

    const pagination = React.useMemo(
        () => ({ pageIndex, pageSize }),
        [pageIndex, pageSize]
    );

    // Prepare query parameters
    const queryParams: GetFeesParams = {
        page: pageIndex,
        size: pageSize,
        sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
        academicYearId: debouncedAcademicYear || undefined, // Changed from academicYear to academicYearId
        categoryId: categoryFilter || undefined,
        // Add active filter param if API supports it
        // active: activeFilter ? activeFilter.includes('true') : undefined,
    };

    // Fetch data using TanStack Query
    const { data, isLoading, error, refetch } = useQuery({
        queryKey: ["fees", queryParams], // Include query params in the key
        queryFn: () => getFees(queryParams),
        keepPreviousData: true, // Keep previous data while loading new page
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    // Get translations specifically for the table section
    const tTable = useTranslations("AdminFeesPage.table");
    // Pass the tShared function directly, not tShared.rich('')
    const columns = React.useMemo(() => getColumns({ t: tTable, tShared: tShared }), [tTable, tShared]);

    const table = useReactTable({
        data: data?.content ?? [], // Use fetched data or empty array
        columns,
        pageCount: data?.totalPages ?? 0, // Use totalPages from API response
        state: {
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
            pagination,
        },
        enableRowSelection: true,
        manualPagination: true, // We handle pagination server-side
        manualSorting: true, // We handle sorting server-side
        manualFiltering: true, // We handle filtering server-side
        onPaginationChange: setPagination,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    });

    // Handle loading and error states
    const renderTableContent = () => {
        if (isLoading) {
            // Show skeleton loaders based on page size
            return Array.from({ length: pageSize }).map((_, rowIndex) => (
                <TableRow key={`skeleton-row-${rowIndex}`}>
                    {columns.map((column, colIndex) => {
                        // Generate a unique key using row index and column index/id
                        const key = `skeleton-cell-${rowIndex}-${column.id ?? colIndex}`;
                        return (
                            <TableCell key={key}>
                                <Skeleton className="h-6 w-full" />
                            </TableCell>
                        );
                    })}
                </TableRow>
            ));
        }

        if (error) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center text-destructive">
                        {t("errorLoading", { error: (error as Error).message })}
                    </TableCell>
                </TableRow>
            );
        }

        if (!table.getRowModel().rows?.length) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        {t("noResults")}
                    </TableCell>
                </TableRow>
            );
        }

        // Render actual rows
        return table.getRowModel().rows.map((row) => (
            <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
            >
                {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                ))}
            </TableRow>
        ));
    };


    return (
        <div className="space-y-4">
            <DataTableToolbar table={table} onFeeAdded={refetch} />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id} colSpan={header.colSpan}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {renderTableContent()}
                    </TableBody>
                </Table>
            </div>
            {/* Pass translations scoped specifically for pagination */}
            <DataTablePagination table={table} t={useTranslations('Shared.dataTable.pagination')} />
        </div>
    );
}
