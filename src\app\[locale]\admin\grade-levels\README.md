# Grade Levels Management

This module provides a comprehensive interface for managing grade levels within educational stages in the school management system.

## Features

### Core Functionality
- **CRUD Operations**: Create, Read, Update, and Delete grade levels
- **Educational Stage Integration**: Grade levels are organized within educational stages
- **Bilingual Support**: Full English and Arabic language support
- **Sorting & Filtering**: Filter by educational stage and sort by various criteria
- **Search**: Global search across all grade level fields
- **Pagination**: Efficient data loading with pagination support

### User Interface
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Data Table**: Advanced table with sorting, filtering, and pagination
- **Modal Dialogs**: User-friendly forms for adding and editing
- **Confirmation Dialogs**: Safe deletion with confirmation prompts
- **Loading States**: Proper loading indicators and error handling
- **Toast Notifications**: Success and error feedback

## File Structure

```
src/app/[locale]/admin/grade-levels/
├── page.tsx                    # Main page component
├── data-table.tsx             # Data table component
├── columns.tsx                # Table column definitions
├── add-dialog.tsx             # Add grade level dialog
├── edit-dialog.tsx            # Edit grade level dialog
├── delete-dialog.tsx          # Delete confirmation dialog
├── data-table-row-actions.tsx # Row action menu
├── __tests__/                 # Test files
│   └── grade-levels.test.tsx
└── README.md                  # This file
```

## API Integration

The module integrates with the following API endpoints:

- `GET /admin/grade-levels?stageId={id}` - Get grade levels by educational stage
- `GET /admin/grade-levels/{id}` - Get single grade level
- `POST /admin/grade-levels` - Create new grade level
- `PUT /admin/grade-levels/{id}` - Update grade level
- `DELETE /admin/grade-levels/{id}` - Delete grade level

## Data Schema

### Grade Level DTO
```typescript
interface GradeLevelDto {
  id: string;
  nameEn: string;
  nameAr: string;
  levelOrder: number;
  educationalStageId: string;
}
```

### Create/Update Requests
```typescript
interface CreateGradeLevelRequest {
  nameEn: string;
  nameAr: string;
  levelOrder: number;
  educationalStageId: string;
}

interface UpdateGradeLevelRequest {
  nameEn?: string;
  nameAr?: string;
  levelOrder?: number;
}
```

## Validation Rules

- **English Name**: Required, max 100 characters
- **Arabic Name**: Required, max 100 characters
- **Level Order**: Required integer, 1-999
- **Educational Stage**: Required UUID

## Navigation

The grade levels page is accessible through:
- Admin Sidebar → School Management → Configurations → Grade Levels
- Direct URL: `/admin/grade-levels`

## Translations

All text is internationalized with support for:
- English (`en`)
- Arabic (`ar`)

Translation keys are organized under `AdminGradeLevelsPage` namespace.

## Testing

Basic tests are included to verify:
- Component rendering
- Loading states
- Error handling
- API integration

Run tests with:
```bash
npm test src/app/[locale]/admin/grade-levels/__tests__/
```

## Dependencies

- **React Query**: Data fetching and caching
- **React Hook Form**: Form management
- **Zod**: Schema validation
- **Radix UI**: UI components
- **Lucide React**: Icons
- **Next Intl**: Internationalization
- **Sonner**: Toast notifications

## Usage Examples

### Adding a New Grade Level
1. Click "Add New Grade Level" button
2. Select educational stage
3. Enter English and Arabic names
4. Set level order (1, 2, 3, etc.)
5. Click "Add Grade Level"

### Filtering by Educational Stage
1. Use the dropdown filter above the table
2. Select specific educational stage or "All Educational Stages"
3. Table updates automatically

### Editing a Grade Level
1. Click the action menu (⋯) in the table row
2. Select "Edit"
3. Modify the fields in the dialog
4. Click "Update Grade Level"

### Deleting a Grade Level
1. Click the action menu (⋯) in the table row
2. Select "Delete"
3. Confirm deletion in the dialog
4. Grade level is permanently removed

## Error Handling

The module handles various error scenarios:
- Network connectivity issues
- API server errors
- Validation errors
- Permission errors

All errors are displayed with user-friendly messages and appropriate fallback UI.
