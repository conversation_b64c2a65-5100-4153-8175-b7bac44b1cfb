import { getTranslations } from "next-intl/server";
import { JournalEntryForm } from "../journal-entry-form"; // Import the form
import { PageHeader, PageHeaderHeading, PageHeaderDescription } from "@/components/ui/page-header";
import { Metadata } from "next";

// Optional: Add specific metadata for the add page
export const metadata: Metadata = {
    // TODO: Add translations if needed for title/description
    title: "Add Journal Entry",
    description: "Create a new journal entry record.",
};

export default async function AddJournalEntryPage() {
    const t = await getTranslations("AdminJournalEntriesPage.form"); // Use form translations

    return (
        <div className="container mx-auto py-10">
            <PageHeader>
                <div> {/* Wrap heading and description */}
                    <PageHeaderHeading>{t("addTitle")}</PageHeaderHeading>
                    {/* Optional: Add a description */}
                    {/* <PageHeaderDescription>
                        Fill in the details below to create a new journal entry.
                    </PageHeaderDescription> */}
                </div>
            </PageHeader>
            <JournalEntryForm />
        </div>
    );
}
