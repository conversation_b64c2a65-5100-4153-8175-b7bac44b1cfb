"use client"; // This page requires client-side hooks for state and data fetching

import * as React from "react";
import {
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusCircle, Search } from "lucide-react";

import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table"; // Import reusable data table
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton"; // For loading state
import { columns } from "./columns"; // Import column definitions
import { getUsers } from "@/lib/api/admin/users";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export default function UsersPage() {
  const t = useTranslations("AdminUsersPage");

  // State for pagination
  const [pagination, setPagination] = React.useState({
    pageIndex: 0, // Initial page index
    pageSize: 10, // Default page size
  });

  // State for client-side search term
  const [searchTerm, setSearchTerm] = React.useState('');

  // Table state
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  // Fetch users data using TanStack Query
  const { data: pageData, isLoading, isError, error } = useQuery({
    queryKey: ["adminUsers", pagination.pageIndex, pagination.pageSize, sorting, searchTerm], // Include sorting/search if server-side
    queryFn: () =>
      getUsers({
        page: pagination.pageIndex,
        size: pagination.pageSize,
        sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`), // Format for API
        // search: searchTerm // Pass search term if API supports it
      }),
    placeholderData: (previousData) => previousData, // Keep previous data while loading new page
    // Refetch options can be configured here if needed
  });

  // Memoize columns to prevent unnecessary re-renders
  const tableColumns = React.useMemo(() => columns, []);

  // Handle data for the table, default to empty array if loading or error
  const tableData = React.useMemo(() => pageData?.content ?? [], [pageData]);
  const pageCount = React.useMemo(() => pageData?.totalPages ?? 0, [pageData]);

  // Create table instance
  const table = useReactTable({
    data: tableData,
    columns: tableColumns,
    pageCount,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    manualSorting: true,
  });


  // Loading state UI
  const renderSkeletons = () => (
    <div className="space-y-4">
       <div className="flex items-center justify-between">
         <Skeleton className="h-8 w-[250px]" />
         <Skeleton className="h-8 w-[120px]" />
       </div>
       <div className="rounded-md border">
         <Skeleton className="h-[400px] w-full" /> {/* Adjust height based on page size */}
       </div>
       <div className="flex items-center justify-end space-x-2 py-4">
         <Skeleton className="h-8 w-[100px]" />
         <Skeleton className="h-8 w-8" />
         <Skeleton className="h-8 w-8" />
       </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
        <CardDescription>{t("description")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          {/* Toolbar: Search and Add Button */}
          <div className="flex items-center justify-between gap-4">
            <div className="relative flex-1 md:grow-0">
               <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
               <Input
                 type="search"
                 placeholder={t("searchPlaceholder")}
                 value={searchTerm}
                 onChange={(e) => setSearchTerm(e.target.value)}
                 className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[300px]"
               />
            </div>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" /> {t("addUserButton")}
            </Button>
          </div>

          {/* Data Table or Loading/Error State */}
          {isLoading ? (
             renderSkeletons()
          ) : isError ? (
            <div className="text-center text-red-600 py-10">
              {t("errorLoading", { error: error?.message || "Unknown error" })}
            </div>
          ) : (
            <div className="space-y-4">
              <DataTable
                table={table}
                columns={tableColumns}
              />
              <DataTablePagination table={table} />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
