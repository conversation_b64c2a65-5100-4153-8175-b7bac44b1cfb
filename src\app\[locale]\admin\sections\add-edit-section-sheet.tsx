'use client';

import { CreateSectionPayload, CreateSectionSchema, SectionDto, UpdateSectionPayload, UpdateSectionSchema } from '@/lib/dto/admin/school-management/sections.dto';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; // Assuming Shadcn Select
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetFooter,
  SheetHeader,
  SheetTitle
} from '@/components/ui/sheet';
import { useCreateSection, useUpdateSection } from '@/lib/api/admin/school-management/sections-api';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useGetAcademicYears } from '@/lib/api/admin/school-management/academic-years-api';
import { useGetBranches } from '@/lib/api/admin/school-management/branches-api';
import { getAllGradeLevelsList } from '@/lib/api/admin/grade-levels';
import { useQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { zodResolver } from '@hookform/resolvers/zod';

interface AddEditSectionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  section?: SectionDto | undefined; // Current section for editing, undefined for new
  defaultAcademicYearId?: string;
  defaultBranchId?: string;
  defaultGradeLevelId?: string;
}

export function AddEditSectionSheet({ isOpen, onClose, section, defaultAcademicYearId, defaultBranchId, defaultGradeLevelId }: AddEditSectionSheetProps) {
  const t = useTranslations('AdminSectionsPage.form'); // Updated translation key
  const tShared = useTranslations('Shared'); // Using the new Shared translations
  // const { toast } = useToast(); // Correctly get toast function from the hook
  const isEditing = !!section;

  // Fetch data for dropdowns
  const { data: academicYearsData, isLoading: isLoadingAcademicYears } = useGetAcademicYears({ size: 100 });
  const { data: branchesData, isLoading: isLoadingBranches } = useGetBranches({ size: 100 });
  const { data: gradeLevelsData, isLoading: isLoadingGradeLevels } = useQuery({
    queryKey: ['grade-levels-all'],
    queryFn: getAllGradeLevelsList,
  });

  const academicYears = (() => {
    if (!academicYearsData) return [];
    if (Array.isArray(academicYearsData)) return academicYearsData;
    if ('content' in academicYearsData) return academicYearsData.content;
    return [];
  })();

  const branches = (() => {
    if (!branchesData) return [];
    if (Array.isArray(branchesData)) return branchesData;
    if ('content' in branchesData) return branchesData.content;
    return [];
  })();

  const gradeLevels = gradeLevelsData || [];

  const formSchema = isEditing ? UpdateSectionSchema : CreateSectionSchema;
  type FormValues = CreateSectionPayload | UpdateSectionPayload;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: isEditing
      ? { name: section?.name || '', capacity: section?.capacity ?? undefined }
      : {
          name: '',
          capacity: undefined,
          academicYearId: defaultAcademicYearId || '',
          branchId: defaultBranchId || '',
          gradeLevelId: defaultGradeLevelId || '',
        },
  });

  const createSectionMutation = useCreateSection();
  const updateSectionMutation = useUpdateSection();

  useEffect(() => {
    if (isOpen) {
      form.reset(
        isEditing
          ? { name: section?.name || '', capacity: section?.capacity ?? undefined }
          : {
              name: '',
              capacity: undefined,
              academicYearId: defaultAcademicYearId || (academicYears.length > 0 ? academicYears[0].id : ''),
              branchId: defaultBranchId || (branches.length > 0 ? branches[0].id : ''),
              gradeLevelId: defaultGradeLevelId || (gradeLevels.length > 0 ? gradeLevels[0].id : ''),
            }
      );
    }
  }, [isOpen, isEditing, section?.id, section?.name, section?.capacity, defaultAcademicYearId, defaultBranchId, defaultGradeLevelId, academicYears.length, branches.length, gradeLevels.length, form]);

  const onSubmit = async (values: FormValues) => {
    try {
      if (isEditing && section) {
        await updateSectionMutation.mutateAsync({ id: section.id, payload: values as UpdateSectionPayload });
        toast.success(t('updateSuccess'));
      } else {
        await createSectionMutation.mutateAsync(values as CreateSectionPayload);
        toast.success(t('addSuccess'));
      }
      onClose();
    } catch (error) {
      toast.error(
        isEditing ? t('updateError') : t('addError'),
        {
          description: (error as Error).message || tShared('Messages.error', { errorDetails: 'Unknown error' }),
        }
      );
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="sm:max-w-lg">
        <SheetHeader>
          <SheetTitle>{isEditing ? t('editTitle') : t('addTitle')}</SheetTitle>
          {/* <SheetDescription>{isEditing ? t('editDescription') : t('addDescription')}</SheetDescription> */}
          {/* Description can be added to translation files if needed */}
        </SheetHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 py-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('nameLabel')}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('namePlaceholder')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="capacity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('capacityLabel')}</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder={t('capacityPlaceholder')} {...field} onChange={e => field.onChange(e.target.value === '' ? null : Number(e.target.value))} value={field.value ?? ''} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {!isEditing && (
              <>
                <FormField
                  control={form.control}
                  name="academicYearId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('academicYearLabel')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('selectAcademicYearPlaceholder')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoadingAcademicYears ? (
                            <SelectItem value="loading" disabled>{tShared('Messages.loading')}</SelectItem>
                          ) : (
                            academicYears.map(ay => (
                              <SelectItem key={ay.id} value={ay.id}>{ay.name}</SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="branchId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('branchLabel')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('selectBranchPlaceholder')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoadingBranches ? (
                            <SelectItem value="loading" disabled>{tShared('Messages.loading')}</SelectItem>
                          ) : (
                            branches.map(b => (
                              <SelectItem key={b.id} value={b.id}>{`${b.nameEn} ${b.nameAr ? `(${b.nameAr})` : ''}`}</SelectItem> // Handle optional nameAr
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="gradeLevelId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('gradeLevelLabel')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('selectGradeLevelPlaceholder')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoadingGradeLevels ? (
                            <SelectItem value="loading" disabled>{tShared('Messages.loading')}</SelectItem>
                          ) : (
                            gradeLevels.map(gl => (
                              <SelectItem key={gl.id} value={gl.id}>{`${gl.nameEn} ${gl.nameAr ? `(${gl.nameAr})` : ''}`}</SelectItem> // Handle optional nameAr
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <SheetFooter className="mt-8">
              <SheetClose asChild>
                <Button type="button" variant="outline">{tShared('Actions.cancel')}</Button>
              </SheetClose>
              <Button type="submit" disabled={form.formState.isSubmitting || (isEditing ? updateSectionMutation.isPending : createSectionMutation.isPending)}>
                {form.formState.isSubmitting || (isEditing ? updateSectionMutation.isPending : createSectionMutation.isPending) ? tShared('Actions.saving') : (isEditing ? tShared('Actions.saveChanges') : tShared('Actions.create'))}
              </Button>
            </SheetFooter>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
}
