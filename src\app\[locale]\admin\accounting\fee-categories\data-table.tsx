"use client";

import * as React from "react";
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar"; // Adjust path
import { getFeeCategories } from "@/lib/api/admin/accounting/fee-categories"; // Adjust API import
import { FeeCategoryDto } from "@/lib/dto/admin/accounting/fee-categories.dto"; // Adjust DTO import
import { Skeleton } from "@/components/ui/skeleton";
import { getColumns } from "./columns"; // Adjust path

interface DataTableProps<TData, TValue> {
    // No columns prop needed as it's derived internally
}

// Debounce hook (assuming it's available or defined elsewhere, e.g., in utils)
function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

    React.useEffect(() => {
        const timer = setTimeout(() => setDebouncedValue(value), delay);
        return () => clearTimeout(timer);
    }, [value, delay]);

    return debouncedValue;
}


export function FeeCategoriesDataTable<TData extends FeeCategoryDto, TValue>({}: DataTableProps<TData, TValue>) {
    const tPage = useTranslations("AdminFeeCategoriesPage"); // Adjust namespace
    const tTable = useTranslations("AdminFeeCategoriesPage.table");
    const tShared = useTranslations("Shared");

    const columns = React.useMemo(() => getColumns(tTable, tShared), [tTable, tShared]);

    const [rowSelection, setRowSelection] = React.useState({});
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [pagination, setPagination] = React.useState({
        pageIndex: 0,
        pageSize: 10,
    });
    const [globalFilter, setGlobalFilter] = React.useState('');
    // No activeOnly state needed for Fee Categories

    const debouncedGlobalFilter = useDebounce(globalFilter, 300);

    const queryKey = [
        "fee-categories", // Adjust query key
        {
            page: pagination.pageIndex,
            size: pagination.pageSize,
            sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
            search: debouncedGlobalFilter, // Use 'search' based on API function
            // No activeOnly filter
        }
    ];

    const { data, isLoading, isError, error } = useQuery({
        queryKey,
        queryFn: () => getFeeCategories({ // Use correct API function
            page: pagination.pageIndex,
            size: pagination.pageSize,
            sort: sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`),
            search: debouncedGlobalFilter,
            // No activeOnly filter
        }),
        placeholderData: (previousData) => previousData,
    });

    const table = useReactTable({
        data: data?.content ?? [],
        columns,
        state: {
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
            pagination,
            globalFilter,
        },
        enableRowSelection: true,
        manualPagination: true,
        manualSorting: true,
        manualFiltering: true, // Server-side filtering via 'search' param
        pageCount: data?.totalPages ?? -1,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onPaginationChange: setPagination,
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    });

    const renderTableContent = () => {
        if (isLoading) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        <div className="space-y-2">
                            {Array.from({ length: 5 }).map((_, i) => (
                                <Skeleton key={i} className="h-8 w-full" />
                            ))}
                        </div>
                    </TableCell>
                </TableRow>
            );
        }

        if (isError) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center text-destructive">
                        {tPage("errorLoading", { error: error?.message || 'Unknown error' })}
                    </TableCell>
                </TableRow>
            );
        }

        if (!table.getRowModel().rows?.length) {
            return (
                <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                        {tPage("noResults")}
                    </TableCell>
                </TableRow>
            );
        }

        return table.getRowModel().rows.map((row) => (
            <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
            >
                {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                ))}
            </TableRow>
        ));
    };

    return (
        <div className="space-y-4">
            <DataTableToolbar
                table={table}
                globalFilter={globalFilter}
                setGlobalFilter={setGlobalFilter}
                // No activeOnly props needed
            />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead key={header.id} colSpan={header.colSpan}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {renderTableContent()}
                    </TableBody>
                </Table>
            </div>
             {/* Pass translations scoped specifically for pagination */}
            <DataTablePagination table={table} t={useTranslations('Shared.dataTable.pagination')} />
        </div>
    );
}
