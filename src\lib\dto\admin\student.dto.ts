import { Page } from "@/lib/dto/page";
import { UserDto } from "@/lib/dto/admin/user.dto";
import { SimpleGuardianDto } from "@/lib/dto/admin/guardian.dto";

/**
 * Data Transfer Object for Student details.
 * Based on GET /api/v1/students/{studentId} response schema.
 */
export interface StudentDto {
    id: string;
    userAccount: UserDto;
    admissionNumber: string;
    nationalId: string;
    idType: IdType;
    guardians: SimpleGuardianDto[];
    gradeLevel?: SimpleGradeLevelDto;
    branch?: SimpleBranchDto;
    createdAt: string; // ISO DateTime string
    updatedAt: string; // ISO DateTime string
}

/**
 * Simplified DTO for Student, often used in lists or dropdowns.
 */
export interface SimpleStudentDto {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
    admissionNumber: string;
    nationalId: string;
    idType: IdType;
}

/**
 * Type for ID document types
 */
export type IdType = "NATIONAL_ID" | "PASSPORT";

/**
 * Request body for creating a new Student profile.
 * Based on POST /api/v1/students/profiles endpoint.
 */
export interface CreateStudentProfileRequest {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phoneNumber?: string;
    dateOfBirth: string; // ISO Date format (YYYY-MM-DD)
    gender: "MALE" | "FEMALE";
    address?: string;
    nationalId: string;
    idType: IdType;
    admissionNumber?: string; // Optional as it might be auto-generated by the backend
    gradeLevelId?: string;
    branchId?: string;
}

/**
 * Request body for updating an existing Student profile.
 */
export interface UpdateStudentProfileRequest {
    admissionNumber?: string;
    nationalId?: string;
    idType?: IdType;
    gradeLevelId?: string;
    branchId?: string;
}

/**
 * Parameters for fetching Students with pagination and filtering
 */
export interface GetStudentsParams {
    page?: number;
    size?: number;
    sort?: string[];
    search?: string;
}

/**
 * Represents a paginated response for Students.
 */
export type PageStudentDto = Page<StudentDto>;

// Import these from their respective DTOs
import { SimpleGradeLevelDto } from "@/lib/dto/admin/grade-level.dto";
import { SimpleBranchDto } from "@/lib/dto/admin/branch.dto";
