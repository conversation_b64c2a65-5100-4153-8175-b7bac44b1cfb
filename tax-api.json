{"tags": [{"name": "Tax Management", "description": "APIs for managing taxes"}], "paths": {"/api/v1/accounting/taxes/{id}": {"get": {"tags": ["Tax Management"], "summary": "Get a tax by ID", "operationId": "getTaxById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Tax found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaxDto"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Tax not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Tax Management"], "summary": "Update an existing tax", "operationId": "updateTax", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTaxRequest"}}}, "required": true}, "responses": {"200": {"description": "Tax updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaxDto"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Tax not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Tax Management"], "summary": "Delete a tax by ID", "operationId": "deleteTax", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Tax deleted successfully"}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Tax not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"TaxDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nameAr": {"type": "string"}, "nameEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "percent": {"type": "number"}, "chartOfAccount": {"$ref": "#/components/schemas/SimpleChartOfAccountDto"}}}, "UpdateTaxRequest": {"required": ["chartOfAccountId", "nameAr", "nameEn", "percent"], "type": "object", "properties": {"nameAr": {"type": "string"}, "nameEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "percent": {"maximum": 100.0, "exclusiveMaximum": false, "minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "chartOfAccountId": {"type": "string", "format": "uuid"}}}, "CreateTaxRequest": {"required": ["chartOfAccountId", "nameAr", "nameEn", "percent"], "type": "object", "properties": {"nameAr": {"type": "string"}, "nameEn": {"type": "string"}, "descriptionAr": {"type": "string"}, "descriptionEn": {"type": "string"}, "percent": {"maximum": 100.0, "exclusiveMaximum": false, "minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "chartOfAccountId": {"type": "string", "format": "uuid"}}}, "SimpleChartOfAccountDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "accountNumber": {"type": "string"}, "nameEn": {"type": "string"}, "nameAr": {"type": "string"}}}, "PageTaxDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/TaxDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "SortObject": {"type": "object", "properties": {"direction": {"type": "string"}, "nullHandling": {"type": "string"}, "ascending": {"type": "boolean"}, "property": {"type": "string"}, "ignoreCase": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "unpaged": {"type": "boolean"}, "paged": {"type": "boolean"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}}, "ErrorResponse": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}