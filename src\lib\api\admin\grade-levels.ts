import { fetchWithAuth } from '@/lib/fetch-with-auth';
import { API_BASE_URL } from "@/lib/constants";
import {
    GradeLevelDto,
    CreateGradeLevelRequest,
    UpdateGradeLevelRequest,
    GetGradeLevelsParams,
    PageGradeLevelDto // Import Page type even if not directly used by /all
} from '@/lib/dto/admin/grade-level.dto';
import { handleApiError } from '@/lib/api-error-handler';

const ADMIN_GRADE_LEVELS_ENDPOINT = `${API_BASE_URL}/admin/grade-levels`;

/**
 * Fetches grade levels for a specific stage.
 * Note: API spec indicates this returns an array, not paginated.
 * @param params - Contains the required stageId.
 * @returns A promise that resolves to an array of GradeLevelDto.
 */
export async function getGradeLevelsByStage(params: GetGradeLevelsParams): Promise<GradeLevelDto[]> {
    const queryParams = new URLSearchParams({ stageId: params.stageId });
    // Add pagination/sort params if API supports them later
    // if (params.page !== undefined) queryParams.append("page", params.page.toString());
    // if (params.size !== undefined) queryParams.append("size", params.size.toString());
    // if (params.sort) params.sort.forEach(s => queryParams.append("sort", s));

    const url = `${ADMIN_GRADE_LEVELS_ENDPOINT}?${queryParams.toString()}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            await handleApiError(response, `Failed to fetch grade levels for stage ${params.stageId}`);
        }
        const data: unknown = await response.json();
        // TODO: Add runtime validation if necessary
        return data as GradeLevelDto[];
    } catch (error) {
        console.error(`Error fetching grade levels for stage ${params.stageId}:`, error);
        throw error;
    }
}

/**
 * Fetches all grade levels across all educational stages.
 * This function fetches all educational stages first, then fetches grade levels for each stage.
 * @returns A promise that resolves to an array of all GradeLevelDto.
 */
export async function getAllGradeLevelsList(): Promise<GradeLevelDto[]> {
    try {
        // First, fetch all educational stages
        const stagesResponse = await fetchWithAuth(`${API_BASE_URL}/admin/educational-stages`);
        if (!stagesResponse.ok) {
            await handleApiError(stagesResponse, 'Failed to fetch educational stages');
        }
        const stages: { id: string }[] = await stagesResponse.json();

        // Then fetch grade levels for each stage
        const allGradeLevels: GradeLevelDto[] = [];
        for (const stage of stages) {
            try {
                const gradeLevels = await getGradeLevelsByStage({ stageId: stage.id });
                allGradeLevels.push(...gradeLevels);
            } catch (error) {
                console.warn(`Failed to fetch grade levels for stage ${stage.id}:`, error);
                // Continue with other stages even if one fails
            }
        }

        // Sort by educational stage and then by level order
        return allGradeLevels.sort((a, b) => {
            if (a.educationalStageId !== b.educationalStageId) {
                return a.educationalStageId.localeCompare(b.educationalStageId);
            }
            return a.levelOrder - b.levelOrder;
        });
    } catch (error) {
        console.error('Error fetching all grade levels:', error);
        throw error;
    }
}


/**
 * Fetches a single grade level by its ID.
 * @param id - The UUID of the grade level.
 * @returns A promise that resolves to a GradeLevelDto.
 */
export async function getGradeLevelById(id: string): Promise<GradeLevelDto> {
    const url = `${ADMIN_GRADE_LEVELS_ENDPOINT}/${id}`;
    try {
        const response = await fetchWithAuth(url);
        if (!response.ok) {
            await handleApiError(response, `Failed to fetch grade level with id ${id}`);
        }
        const data: unknown = await response.json();
        // TODO: Add runtime validation if necessary
        return data as GradeLevelDto;
    } catch (error) {
        console.error(`Error fetching grade level ${id}:`, error);
        throw error;
    }
}

/**
 * Creates a new grade level.
 * @param request - The data for the new grade level.
 * @returns A promise that resolves to the created GradeLevelDto.
 */
export async function createGradeLevel(request: CreateGradeLevelRequest): Promise<GradeLevelDto> {
    const url = ADMIN_GRADE_LEVELS_ENDPOINT;
    try {
        const response = await fetchWithAuth(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request),
        });
        if (!response.ok) { // Check for 200 OK based on API spec
             await handleApiError(response, 'Failed to create grade level');
        }
        const data: unknown = await response.json();
         // TODO: Add runtime validation if necessary
        return data as GradeLevelDto;
    } catch (error) {
        console.error("Error creating grade level:", error);
        throw error;
    }
}

/**
 * Updates an existing grade level.
 * @param id - The UUID of the grade level to update.
 * @param request - The updated data for the grade level.
 * @returns A promise that resolves to the updated GradeLevelDto.
 */
export async function updateGradeLevel(id: string, request: UpdateGradeLevelRequest): Promise<GradeLevelDto> {
    const url = `${ADMIN_GRADE_LEVELS_ENDPOINT}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request),
        });
        if (!response.ok) {
             await handleApiError(response, `Failed to update grade level ${id}`);
        }
        const data: unknown = await response.json();
         // TODO: Add runtime validation if necessary
        return data as GradeLevelDto;
    } catch (error) {
        console.error(`Error updating grade level ${id}:`, error);
        throw error;
    }
}

/**
 * Deletes a grade level.
 * @param id - The UUID of the grade level to delete.
 * @returns A promise that resolves when the deletion is successful.
 */
export async function deleteGradeLevel(id: string): Promise<void> {
    const url = `${ADMIN_GRADE_LEVELS_ENDPOINT}/${id}`;
    try {
        const response = await fetchWithAuth(url, {
            method: 'DELETE',
        });
        if (!response.ok) { // Check for 200 OK based on API spec
             await handleApiError(response, `Failed to delete grade level ${id}`);
        }
        // No content expected on successful delete based on API spec returning 200 OK
    } catch (error) {
        console.error(`Error deleting grade level ${id}:`, error);
        throw error;
    }
}
