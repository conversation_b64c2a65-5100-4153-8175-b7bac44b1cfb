"use client";

import { ColumnDef } from "@tanstack/react-table";
import { UseTranslations } from "next-intl";
import { FeeCategoryDto } from "@/lib/dto/admin/accounting/fee-categories.dto";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions"; // Adjust path

// Accept translation functions as arguments
export const getColumns = (
    t: UseTranslations<"AdminFeeCategoriesPage.table">, // Use specific translation namespace
    tShared: UseTranslations<"Shared"> // Keep shared if needed for status etc. (though FeeCategory doesn't have status)
): ColumnDef<FeeCategoryDto>[] => {

    return [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                    className="translate-y-[2px]"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                    className="translate-y-[2px]"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "nameEn",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("nameEn")} />
            ),
            cell: ({ row }) => row.getValue("nameEn"),
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "nameAr",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("nameAr")} />
            ),
            cell: ({ row }) => row.getValue("nameAr"),
            enableSorting: true,
            enableHiding: true,
        },
        {
            accessorKey: "descriptionEn",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("descriptionEn")} />
            ),
            cell: ({ row }) => (
                <div className="truncate max-w-[200px]"> {/* Limit width and truncate */}
                    {row.getValue("descriptionEn") || '---'}
                </div>
            ),
            enableSorting: false, // Descriptions are usually not sorted
            enableHiding: true,
        },
        {
            accessorKey: "descriptionAr",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("descriptionAr")} />
            ),
            cell: ({ row }) => (
                 <div className="truncate max-w-[200px]" dir="rtl"> {/* Limit width and truncate */}
                    {row.getValue("descriptionAr") || '---'}
                </div>
            ),
            enableSorting: false,
            enableHiding: true,
        },
        // No 'status' or 'parent' column for Fee Categories based on DTO/API
        {
            id: "actions",
            cell: ({ row }) => <DataTableRowActions row={row} />,
            enableSorting: false,
            enableHiding: false,
        },
    ];
};
