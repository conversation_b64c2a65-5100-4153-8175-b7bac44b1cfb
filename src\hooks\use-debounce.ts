import * as React from 'react';

/**
 * Custom hook to debounce a value.
 * @param value The value to debounce.
 * @param delay The debounce delay in milliseconds.
 * @returns The debounced value.
 */
export function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

    React.useEffect(() => {
        // Set timeout to update debounced value after delay
        const timer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        // Clear timeout if value changes (or delay changes) or component unmounts
        return () => {
            clearTimeout(timer);
        };
    }, [value, delay]); // Only re-call effect if value or delay changes

    return debouncedValue;
}
