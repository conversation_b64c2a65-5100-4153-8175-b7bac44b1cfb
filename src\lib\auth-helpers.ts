/**
 * Helper functions for authentication
 */

import { AUTH_COOKIE_NAME } from "@/stores/auth";
import Cookies from "js-cookie";

/**
 * Check if the user is authenticated by examining the auth cookie
 * @returns boolean indicating if the user is authenticated
 */
export function isAuthenticated(): boolean {
  try {
    const authCookie = Cookies.get(AUTH_COOKIE_NAME);
    if (!authCookie) return false;
    
    const parsedCookie = JSON.parse(authCookie);
    return (
      !!parsedCookie?.state?.accessToken && 
      !!parsedCookie?.state?.isAuthenticated
    );
  } catch (error) {
    console.error("[AuthHelpers] Error checking authentication:", error);
    return false;
  }
}

/**
 * Force a hard redirect to the specified path
 * @param path The path to redirect to (should include locale)
 */
export function hardRedirect(path: string): void {
  window.location.href = path;
}

/**
 * Redirect to the admin dashboard with the current locale
 * @param locale The current locale
 */
export function redirectToDashboard(locale: string): void {
  hardRedirect(`/${locale}/admin/dashboard`);
}

/**
 * Redirect to the login page with the current locale
 * @param locale The current locale
 */
export function redirectToLogin(locale: string): void {
  hardRedirect(`/${locale}/auth/login`);
}
