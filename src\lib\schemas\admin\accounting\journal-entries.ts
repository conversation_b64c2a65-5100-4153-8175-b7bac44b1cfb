import { z } from "zod";
import { useTranslations } from "next-intl";

// Schema for a single line in the journal entry
export const createJournalEntryLineSchema = (t: ReturnType<typeof useTranslations<"AdminJournalEntriesPage.validation">>) => z.object({
    chartOfAccountId: z.string().uuid({ message: t("accountIdRequired") }),
    type: z.enum(["DEBIT", "CREDIT"], { required_error: t("typeRequired") }),
    amount: z.coerce.number().positive({ message: t("amountPositive") }),
    description: z.string().max(500, { message: t("lineDescriptionTooLong") }).optional(),
});

// Schema for the main journal entry creation form
export const createJournalEntrySchema = (t: ReturnType<typeof useTranslations<"AdminJournalEntriesPage.validation">>) => z.object({
    entryDate: z.date({ required_error: t("entryDateRequired") }),
    description: z.string().min(1, { message: t("descriptionRequired") }).max(500, { message: t("descriptionTooLong") }),
    referenceNumber: z.string().max(100, { message: t("referenceTooLong") }).optional(),
    lines: z.array(createJournalEntryLineSchema(t))
        .min(2, { message: t("atLeastTwoLines") })
        .refine(lines => {
            const totalDebits = lines.filter(l => l.type === 'DEBIT').reduce((sum, l) => sum + l.amount, 0);
            const totalCredits = lines.filter(l => l.type === 'CREDIT').reduce((sum, l) => sum + l.amount, 0);
            // Use a tolerance for floating point comparisons
            return Math.abs(totalDebits - totalCredits) < 0.001;
        }, { message: t("debitsMustEqualCredits") })
        .refine(lines => lines.some(l => l.type === 'DEBIT'), { message: t("atLeastOneDebit") })
        .refine(lines => lines.some(l => l.type === 'CREDIT'), { message: t("atLeastOneCredit") }),
});

// Schema for updating (similar to create, but might have different constraints if needed)
// For now, it's the same as create, assuming lines can be fully replaced.
export const updateJournalEntrySchema = createJournalEntrySchema;

// Infer types from schemas
export type CreateJournalEntryLineInput = z.infer<ReturnType<typeof createJournalEntryLineSchema>>;
export type CreateJournalEntryInput = z.infer<ReturnType<typeof createJournalEntrySchema>>;
export type UpdateJournalEntryInput = z.infer<ReturnType<typeof updateJournalEntrySchema>>;
