"use client";

import { ExpenseReportDto, ExpenseReportSummaryDto } from "@/lib/dto/admin/accounting/expense-reports.dto";
import { formatCurrency } from "@/lib/utils";
import { useTranslations } from "next-intl";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";

interface ExpensesReportDisplayProps {
  data: ExpenseReportDto;
}

export function ExpensesReportDisplay({ data }: ExpensesReportDisplayProps) {
  const t = useTranslations("ExpenseReportPage");

  // Function to render the category summary section
  const renderCategorySummary = (categorySummaries: ExpenseReportSummaryDto[]) => (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle>{t("categorySummary")}</CardTitle>
        <CardDescription>{t("categorySummaryDescription")}</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("category")}</TableHead>
              <TableHead className="text-right">{t("expenseCount")}</TableHead>
              <TableHead className="text-right">{t("amountBeforeTax")}</TableHead>
              <TableHead className="text-right">{t("taxAmount")}</TableHead>
              <TableHead className="text-right">{t("totalAmount")}</TableHead>
              <TableHead className="text-right">{t("percentage")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categorySummaries.map((summary) => (
              <TableRow key={summary.categoryId}>
                <TableCell>{summary.categoryNameEn} / {summary.categoryNameAr}</TableCell>
                <TableCell className="text-right">{summary.expenseCount}</TableCell>
                <TableCell className="text-right">{formatCurrency(summary.totalAmount)}</TableCell>
                <TableCell className="text-right">{formatCurrency(summary.totalTaxAmount)}</TableCell>
                <TableCell className="text-right">{formatCurrency(summary.totalAmountWithTax)}</TableCell>
                <TableCell className="text-right">{(summary.percentageOfTotal * 100).toFixed(2)}%</TableCell>
              </TableRow>
            ))}
            <TableRow className="font-bold">
              <TableCell>{t("total")}</TableCell>
              <TableCell className="text-right">{data.totalExpenseCount}</TableCell>
              <TableCell className="text-right">{formatCurrency(data.totalAmount)}</TableCell>
              <TableCell className="text-right">{formatCurrency(data.totalTaxAmount)}</TableCell>
              <TableCell className="text-right">{formatCurrency(data.totalAmountWithTax)}</TableCell>
              <TableCell className="text-right">100%</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );

  // Function to render the detailed expenses section
  const renderExpenseDetails = () => (
    <Card>
      <CardHeader>
        <CardTitle>{t("expenseDetails")}</CardTitle>
        <CardDescription>{t("expenseDetailsDescription")}</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("date")}</TableHead>
              <TableHead>{t("description")}</TableHead>
              <TableHead>{t("category")}</TableHead>
              <TableHead>{t("vendor")}</TableHead>
              <TableHead className="text-right">{t("amountBeforeTax")}</TableHead>
              <TableHead className="text-right">{t("taxAmount")}</TableHead>
              <TableHead className="text-right">{t("totalAmount")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.expenses.map((expense) => (
              <TableRow key={expense.id}>
                <TableCell>{format(new Date(expense.expenseDate), "yyyy-MM-dd")}</TableCell>
                <TableCell>{expense.descriptionEn} / {expense.descriptionAr}</TableCell>
                <TableCell>{expense.category.nameEn} / {expense.category.nameAr}</TableCell>
                <TableCell>{expense.vendor || "-"}</TableCell>
                <TableCell className="text-right">{formatCurrency(expense.amountBeforeTax)}</TableCell>
                <TableCell className="text-right">{formatCurrency(expense.taxAmount)}</TableCell>
                <TableCell className="text-right">{formatCurrency(expense.totalAmount)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-8">
      <div className="text-right text-sm">
        {t("reportPeriod")}: {data.startDate} - {data.endDate}
      </div>
      
      {renderCategorySummary(data.categorySummaries)}
      {renderExpenseDetails()}
    </div>
  );
}
