import { SimpleEducationalStageDto } from './educational-stage.dto';

// Based on GET /api/v1/admin/branches/{id} and GET /api/v1/admin/branches responses
export interface BranchDto {
    id: string;
    nameEn: string;
    nameAr: string;
    address?: string; // Optional based on schema
    educationalStages?: SimpleEducationalStageDto[]; // Optional based on schema
    createdAt: string; // ISO DateTime string
    updatedAt: string; // ISO DateTime string
}

// Based on POST /api/v1/admin/branches requestBody
export interface CreateBranchRequest {
    nameEn: string;
    nameAr: string;
    address?: string; // Optional
}

// Based on PUT /api/v1/admin/branches/{id} requestBody
export interface UpdateBranchRequest {
    nameEn?: string; // Optional for partial updates
    nameAr?: string; // Optional for partial updates
    address?: string; // Optional
}

// Simplified DTO for selection lists if needed later
export interface SimpleBranchDto {
    id: string;
    nameEn: string;
    nameAr: string;
}
