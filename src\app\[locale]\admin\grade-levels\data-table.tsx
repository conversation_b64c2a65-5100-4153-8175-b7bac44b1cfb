"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useTranslations } from 'next-intl';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DataTablePagination } from "@/components/ui/data-table-pagination";

import { EnhancedGradeLevelDto, createColumns } from './columns';
import { EducationalStageDto } from '@/lib/dto/admin/educational-stage.dto';

interface DataTableProps {
  data: EnhancedGradeLevelDto[];
  educationalStages: EducationalStageDto[];
  selectedStageId?: string;
  onStageFilterChange: (stageId: string | undefined) => void;
  onRefetch: () => void;
}

export function DataTable({
  data,
  educationalStages,
  selectedStageId,
  onStageFilterChange,
  onRefetch,
}: DataTableProps) {
  const t = useTranslations('AdminGradeLevelsPage');
  const tShared = useTranslations('Shared');

  const [sorting, setSorting] = React.useState<SortingState>([
    { id: "educationalStage", desc: false },
    { id: "levelOrder", desc: false }
  ]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [globalFilter, setGlobalFilter] = React.useState("");

  // Create columns with refetch callback
  const columns = React.useMemo(() => createColumns(onRefetch), [onRefetch]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: (row, columnId, filterValue) => {
      const searchValue = filterValue.toLowerCase();
      const gradeLevel = row.original;

      return (
        gradeLevel.nameEn.toLowerCase().includes(searchValue) ||
        gradeLevel.nameAr.toLowerCase().includes(searchValue) ||
        gradeLevel.educationalStage?.nameEn.toLowerCase().includes(searchValue) ||
        gradeLevel.educationalStage?.nameAr.toLowerCase().includes(searchValue) ||
        gradeLevel.levelOrder.toString().includes(searchValue)
      );
    },
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <div className="flex items-center space-x-4">
          <Input
            placeholder={tShared('DataTable.searchPlaceholder')}
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="max-w-sm"
          />

          <Select
            value={selectedStageId || 'all'}
            onValueChange={(value) => onStageFilterChange(value === 'all' ? undefined : value)}
          >
            <SelectTrigger className="w-[250px]">
              <SelectValue placeholder={t('filterByStage')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('allStages')}</SelectItem>
              {educationalStages.map((stage) => (
                <SelectItem key={stage.id} value={stage.id}>
                  {stage.nameEn}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          variant="outline"
          onClick={onRefetch}
          size="sm"
        >
          {tShared('Actions.refresh')}
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {tShared('DataTable.noResults')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="mt-4">
        <DataTablePagination table={table} />
      </div>
    </div>
  );
}
