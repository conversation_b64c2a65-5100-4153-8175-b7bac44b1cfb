"use client";

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    // No DialogTrigger needed here, controlled externally
} from '@/components/ui/dialog';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { updateEducationalStage } from '@/lib/api/admin/educational-stages';
import { EducationalStageDto } from '@/lib/dto/admin/educational-stage.dto';
import { UpdateEducationalStageInput, updateEducationalStageSchema } from '@/lib/schemas/admin/educational-stage';
import { handleApiError } from '@/lib/api-error-handler'; // Corrected import path

interface EditStageDialogProps {
    stage: EducationalStageDto;
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    onStageUpdated: () => void; // Callback to refresh data
}

export function EditStageDialog({ stage, isOpen, onOpenChange, onStageUpdated }: EditStageDialogProps) {
    const t = useTranslations('AdminEducationalStagesPage.EditDialog');
    const tValidation = useTranslations('AdminEducationalStagesPage.EditDialog.validation');

    const formSchema = updateEducationalStageSchema(tValidation);
    const form = useForm<UpdateEducationalStageInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            nameEn: stage.nameEn || '',
            nameAr: stage.nameAr || '',
            sortOrder: stage.sortOrder || undefined,
        },
    });

     // Reset form when stage changes or dialog opens/closes
    useEffect(() => {
        if (isOpen) {
            form.reset({
                nameEn: stage.nameEn || '',
                nameAr: stage.nameAr || '',
                sortOrder: stage.sortOrder || undefined,
            });
        }
    }, [isOpen, stage, form]);


    const mutation = useMutation({
        mutationFn: (data: UpdateEducationalStageInput) => updateEducationalStage(stage.id, data),
        onSuccess: (data) => {
            toast.success(t('successToast', { name: data.nameEn }));
            onOpenChange(false); // Close dialog
            onStageUpdated(); // Trigger data refresh
        },
        onError: (error) => {
            // Check if the error is due to no changes
             if (error instanceof Error && error.message.includes("No changes detected")) {
                 toast.info(t('noChanges')); // Use info toast for no changes
                 onOpenChange(false); // Close dialog
             } else {
                handleApiError(error, t('errorToast'));
             }
        },
    });

    const onSubmit = (values: UpdateEducationalStageInput) => {
         // Filter out unchanged values before submitting
        const changedValues: UpdateEducationalStageInput = {};
        if (values.nameEn !== stage.nameEn) changedValues.nameEn = values.nameEn;
        if (values.nameAr !== stage.nameAr) changedValues.nameAr = values.nameAr;
        if (values.sortOrder !== stage.sortOrder) changedValues.sortOrder = values.sortOrder;

        if (Object.keys(changedValues).length === 0) {
            toast.info(t('noChanges'));
            onOpenChange(false);
            return;
        }

        mutation.mutate(changedValues);
    };

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>{t('title')}</DialogTitle>
                    <DialogDescription>{t('description', { name: stage.nameEn })}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="nameEn"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('nameEnLabel')}</FormLabel>
                                    <FormControl>
                                        <Input placeholder={t('nameEnPlaceholder')} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="nameAr"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('nameArLabel')}</FormLabel>
                                    <FormControl>
                                        <Input dir="rtl" placeholder={t('nameArPlaceholder')} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                         <FormField
                            control={form.control}
                            name="sortOrder"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t('sortOrderLabel')}</FormLabel>
                                    <FormControl>
                                        <Input type="number" placeholder={t('sortOrderPlaceholder')} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                                {t('cancelButton')}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending || !form.formState.isDirty}>
                                {mutation.isPending ? t('savingButton') : t('saveButton')}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
