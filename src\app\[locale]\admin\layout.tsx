"use client"; // Make this a client component to use hooks

import React, { useEffect, useState } from 'react';

import { Footer } from "@/components/layout/admin/footer";
import { Navbar } from '@/components/layout/admin/navbar';
import { Sidebar } from '@/components/layout/admin/sidebar';
import { Skeleton } from "@/components/ui/skeleton"; // For loading state
import { redirectToLogin } from '@/lib/auth-helpers'; // Import auth helpers
import { useAuthStore } from '@/stores/auth'; // Import your auth store
import { useParams } from 'next/navigation'; // Import hooks
// import { useRouter } from '@/i18n/navigation'; // Use i18n router for proper locale handling - Removed as unused
import { useUiStore } from '@/stores/ui'; // Import the UI store

// const REQUIRED_PERMISSION = "admin_access"; // Removed as unused

// This layout wraps all pages under /admin/*
export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // const router = useRouter(); // Removed as unused
  const params = useParams();
  const locale = params.locale as string; // Get locale from params
  // Get auth state from store
  const { roles, permissions, accessToken, isHydrated, isAuthenticated, checkAuth } = useAuthStore((state) => ({ // Added roles
    roles: state.roles, // Added roles
    permissions: state.permissions,
    accessToken: state.accessToken,
    isHydrated: state.isHydrated,
    isAuthenticated: state.isAuthenticated,
    checkAuth: state.checkAuth,
  }));

  // Get UI state
  const { isSidebarOpen } = useUiStore((state) => ({
    isSidebarOpen: state.isSidebarOpen,
  }));

  // State to track if user is authorized to view admin pages
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    console.log("[AdminLayout Effect] Running auth check...");
    console.log("[AdminLayout Effect] Auth state:", {
      isHydrated,
      isAuthenticated,
      hasToken: !!accessToken
    });

    // First, check if the store is hydrated
    if (!isHydrated) {
      console.log("[AdminLayout Effect] Store not hydrated yet. Waiting...");
      return; // Exit early and wait for hydration
    }

    // Use the checkAuth helper to verify authentication
    const isAuth = checkAuth();
    console.log("[AdminLayout Effect] checkAuth result:", isAuth);

    // Check for admin role (instead of specific permission for now)
    const hasAdminRole = roles.includes("ROLE_ADMIN");
    console.log(`[AdminLayout Effect] Has 'ROLE_ADMIN':`, hasAdminRole);

    // Determine if user is fully authorized (authenticated + has admin role)
    // If specific permissions are needed later, this logic can be expanded:
    // const hasSpecificPermission = permissions.includes(REQUIRED_PERMISSION);
    // const isFullyAuthorized = isAuth && (hasAdminRole || hasSpecificPermission);
    const isFullyAuthorized = isAuth && hasAdminRole;


    if (!isFullyAuthorized) {
      // Not authorized - either not authenticated or missing admin role
      console.warn(
        `[AdminLayout Effect] Authorization check FAILED. Authenticated: ${isAuth}, Has Admin Role: ${hasAdminRole}`
      );

      // Set a small timeout to ensure any state updates complete before redirect
      setTimeout(() => {
        console.log("[AdminLayout Effect] Redirecting to login page...");
        // Use our helper for a consistent redirect
        redirectToLogin(locale);
      }, 100);
    } else {
      // User is authenticated and has permission
      console.log("[AdminLayout Effect] Authorization check PASSED. Setting isAuthorized=true");
      setIsAuthorized(true);
    }
  }, [isHydrated, isAuthenticated, accessToken, roles, permissions, checkAuth, locale]); // Added roles to dependency array

  // Show a full-page loading skeleton while waiting for hydration
  if (!isHydrated) {
    console.log("[AdminLayout Render] Store not hydrated, rendering full page skeleton.");
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="flex flex-col items-center gap-4">
          <Skeleton className="h-12 w-12 rounded-full bg-muted" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-[250px] bg-muted" />
            <Skeleton className="h-4 w-[200px] bg-muted" />
          </div>
        </div>
      </div>
    );
  }

  // If hydrated but not authorized, render null (or minimal indicator)
  // The useEffect handles the redirect logic.
  if (!isAuthorized) {
      console.log("[AdminLayout Render] Hydrated but not authorized, rendering null (redirect pending).");
      // Returning null prevents rendering the layout while redirecting
      return null;
      // Alternatively, a minimal loading indicator could be shown:
      // return <div className="flex min-h-screen items-center justify-center">Redirecting...</div>;
  }

   console.log("[AdminLayout Render] Hydrated and authorized, rendering full layout.");
  // Render the actual layout only if hydrated and authorized
  return (
    // Adjusted sidebar width based on isSidebarOpen state
    <div
      className={`grid min-h-screen w-full ${
        isSidebarOpen ? 'lg:grid-cols-[240px_1fr]' : 'lg:grid-cols-[0px_1fr]'
      } transition-all duration-300 ease-in-out`}
    >
      {/* Sidebar is conditionally rendered or styled in its own component */}
      <Sidebar className="border-r" />
      <div className="flex flex-col overflow-hidden"> {/* Added overflow-hidden */}
        <Navbar />
        <main className="flex flex-1 flex-col gap-4 p-4 sm:px-6 sm:py-6 md:gap-8 bg-background">
          {children}
        </main>
        <Footer />
      </div>
    </div>
  );
}
