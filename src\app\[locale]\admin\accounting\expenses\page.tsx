import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

import { ExpensesDataTable } from "./data-table"; // Import the client component
import { getTranslations } from "next-intl/server";

export default async function ExpensesPage() {
    const t = await getTranslations("AdminExpensesPage");

    return (
        <Card>
            <CardHeader>
                <CardTitle>{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent>
                 {/* Data table is a client component handling its own data fetching */}
                <ExpensesDataTable />
            </CardContent>
        </Card>
    );
}
