import { API_BASE_URL } from "@/lib/constants";
import {
    GuardianDto,
    CreateGuardianProfileRequest,
    CreateGuardianWithUserRequest,
    UpdateGuardianProfileRequest,
    GetGuardiansParams
} from "@/lib/dto/admin/guardian.dto";
import { ErrorResponse } from "@/lib/dto/error-response.dto";
import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { buildQueryString } from "@/lib/utils";

const GUARDIANS_API_PATH = `${API_BASE_URL}/guardians`;

/**
 * Fetches all guardians with optional pagination and filtering
 * GET /api/v1/guardians
 */
export async function getGuardians(params: GetGuardiansParams = {}): Promise<GuardianDto[]> {
    const queryString = buildQueryString({
        page: params.page,
        size: params.size,
        sort: params.sort,
        search: params.search
    });

    const url = `${GUARDIANS_API_PATH}${queryString ? `?${queryString}` : ""}`;
    console.log("Fetching guardians from URL:", url);

    try {
        const response = await fetchWithAuth(url);

        if (!response.ok) {
            let errorMessage = `Failed to fetch guardians: ${response.statusText}`;
            try {
                const errorData = await response.json();
                if (errorData?.message) {
                    errorMessage = errorData.message;
                } else if (errorData?.error) {
                    errorMessage = errorData.error;
                }
                console.error("API error response:", errorData);
            } catch (e) {
                console.error("Failed to parse error response:", e);
            }
            throw new Error(errorMessage);
        }

        const data: unknown = await response.json();
        return data as GuardianDto[];
    } catch (error) {
        console.error("Error fetching guardians:", error);
        if (error instanceof TypeError && error.message.includes("Failed to fetch")) {
            throw new Error("Network error. Please check your connection and try again.");
        }
        throw error instanceof Error ? error : new Error("An unknown error occurred while fetching guardians.");
    }
}

/**
 * Fetches a guardian by ID
 * GET /api/v1/guardians/{guardianId}
 */
export async function getGuardianById(guardianId: string): Promise<GuardianDto> {
    const url = `${GUARDIANS_API_PATH}/${guardianId}`;

    try {
        const response = await fetchWithAuth(url);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch guardian ${guardianId}: ${response.statusText}`);
        }

        const data: unknown = await response.json();
        return data as GuardianDto;
    } catch (error) {
        console.error(`Error fetching guardian ${guardianId}:`, error);
        throw error instanceof Error ? error : new Error(`An unknown error occurred while fetching guardian ${guardianId}.`);
    }
}

/**
 * Fetches a guardian by user ID
 * GET /api/v1/guardians/user/{userId}
 */
export async function getGuardianByUserId(userId: string): Promise<GuardianDto> {
    const url = `${GUARDIANS_API_PATH}/user/${userId}`;

    try {
        const response = await fetchWithAuth(url);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to fetch guardian for user ${userId}: ${response.statusText}`);
        }

        const data: unknown = await response.json();
        return data as GuardianDto;
    } catch (error) {
        console.error(`Error fetching guardian for user ${userId}:`, error);
        throw error instanceof Error ? error : new Error(`An unknown error occurred while fetching guardian for user ${userId}.`);
    }
}

/**
 * Creates a new guardian profile with an existing user (legacy method)
 * POST /api/v1/guardians/profiles
 */
export async function createGuardianProfile(request: CreateGuardianProfileRequest): Promise<GuardianDto> {
    const url = `${GUARDIANS_API_PATH}/profiles`;

    try {
        const response = await fetchWithAuth(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(request)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to create guardian profile: ${response.statusText}`);
        }

        const data: unknown = await response.json();
        return data as GuardianDto;
    } catch (error) {
        console.error("Error creating guardian profile:", error);
        throw error instanceof Error ? error : new Error("An unknown error occurred while creating guardian profile.");
    }
}

/**
 * Creates a new guardian with a new user account
 * POST /api/v1/guardians/register
 */
export async function createGuardianWithUser(request: CreateGuardianWithUserRequest): Promise<GuardianDto> {
    const url = `${GUARDIANS_API_PATH}/register`;

    try {
        console.log("Creating guardian with user:", request);
        console.log("API URL:", url);

        const response = await fetchWithAuth(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(request)
        });

        if (!response.ok) {
            let errorMessage = `Failed to create guardian with user: ${response.statusText}`;
            try {
                const errorData = await response.json();
                if (errorData?.message) {
                    errorMessage = errorData.message;
                } else if (errorData?.error) {
                    errorMessage = errorData.error;
                }

                // Check for specific error types
                if (response.status === 409) {
                    errorMessage = "A user with this email already exists.";
                } else if (response.status === 400) {
                    errorMessage = "Invalid data provided. Please check all fields and try again.";
                }

                console.error("API error response:", errorData);
            } catch (e) {
                console.error("Failed to parse error response:", e);
            }

            throw new Error(errorMessage);
        }

        const data: unknown = await response.json();
        return data as GuardianDto;
    } catch (error) {
        console.error("Error creating guardian with user:", error);
        if (error instanceof TypeError && error.message.includes("Failed to fetch")) {
            throw new Error("Network error. Please check your connection and try again.");
        }
        throw error instanceof Error ? error : new Error("An unknown error occurred while creating guardian with user.");
    }
}

/**
 * Updates an existing guardian profile
 * PUT /api/v1/guardians/{guardianId}
 */
export async function updateGuardianProfile(guardianId: string, request: UpdateGuardianProfileRequest): Promise<GuardianDto> {
    const url = `${GUARDIANS_API_PATH}/${guardianId}`;

    try {
        const response = await fetchWithAuth(url, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(request)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData?.message || `Failed to update guardian profile ${guardianId}: ${response.statusText}`);
        }

        const data: unknown = await response.json();
        return data as GuardianDto;
    } catch (error) {
        console.error(`Error updating guardian profile ${guardianId}:`, error);
        throw error instanceof Error ? error : new Error(`An unknown error occurred while updating guardian profile ${guardianId}.`);
    }
}
