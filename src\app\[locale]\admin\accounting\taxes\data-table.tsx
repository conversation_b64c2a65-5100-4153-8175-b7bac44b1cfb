"use client";

import * as React from "react";
import {
    ColumnDef,
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/ui/data-table/data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar"; // We will create this
import { getColumns } from "./columns"; // We created this
import { getTaxes } from "@/lib/api/admin/accounting/taxes"; // API function
import { TaxDto } from "@/lib/dto/admin/accounting/taxes.dto";
import { Skeleton } from "@/components/ui/skeleton"; // For loading state
import { AddEditTaxDialog } from "./add-edit-tax-dialog";

// Remove t and tShared from props, they will be fetched internally
interface TaxesDataTableProps {
    // No props needed for translations anymore
}

import { useLocale } from "next-intl"; // Import useLocale
import { useDeleteConfirmation } from "@/hooks/use-delete-confirmation"; // Import useDeleteConfirmation

// Remove t and tShared from props, they will be fetched internally
interface TaxesDataTableProps {
    // No props needed for translations anymore
}

export function TaxesDataTable({}: TaxesDataTableProps) {
    // --- Call Hooks at the Top Level ---
    const t = useTranslations("AdminTaxesPage");
    const tTable = useTranslations("AdminTaxesPage.table"); // Specific table translations
    const tShared = useTranslations("Shared");
    const tSharedDataTable = useTranslations("Shared.dataTable"); // Specific dataTable translations
    const locale = useLocale();
    const { confirm, ConfirmDialog } = useDeleteConfirmation(); // Call delete hook here
    const queryClient = useQueryClient();
    // --- End Hook Calls ---

    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});
    const [pagination, setPagination] = React.useState({
        pageIndex: 0, // Initial page index
        pageSize: 10, // Default page size
    });

    // State for controlling the Add/Edit Dialog
    const [isAddEditOpen, setIsAddEditOpen] = React.useState(false);
    const [editingTax, setEditingTax] = React.useState<TaxDto | null>(null);

    // Fetching data using TanStack Query
    const { data, isLoading, error, isFetching } = useQuery({
        queryKey: ['taxes', pagination.pageIndex, pagination.pageSize, sorting, columnFilters],
        queryFn: async () => {
            const sortParams = sorting.map(s => `${s.id},${s.desc ? 'desc' : 'asc'}`);
            // Find the name filter value if it exists
            const nameFilter = columnFilters.find(f => f.id === 'nameEn' || f.id === 'nameAr');
            const nameValue = nameFilter ? nameFilter.value as string : undefined;

            return getTaxes({
                page: pagination.pageIndex,
                size: pagination.pageSize,
                sort: sortParams.length > 0 ? sortParams : undefined, // Only send sort if defined
                name: nameValue, // Pass the name filter
            });
        },
        placeholderData: (previousData) => previousData, // Keep previous data while fetching new
        // Consider adding staleTime or gcTime if needed
    });

    const pageData = data?.content ?? [];
    const totalElements = data?.totalElements ?? 0;
    const totalPages = data?.totalPages ?? 0;

    // Function to handle successful add/edit
    const handleSuccess = () => {
        setIsAddEditOpen(false); // Close dialog
        setEditingTax(null); // Reset editing state
        // Invalidate the query to refetch data
        queryClient.invalidateQueries({ queryKey: ['taxes'] });
    };

    // Function to open the dialog for adding
    const openAddDialog = () => {
        setEditingTax(null); // Ensure no tax is being edited
        setIsAddEditOpen(true);
    };

    // Function to open the dialog for editing
    const openEditDialog = (tax: TaxDto) => {
        setEditingTax(tax);
        setIsAddEditOpen(true);
    };

    // Define columns using the getColumns function
    // Pass necessary values from hooks down to getColumns
    const columns = React.useMemo(
        () => getColumns({
            locale, // Pass locale
            tTable, // Pass specific table translations
            tShared, // Pass general shared translations (for confirm dialog)
            tSharedDataTable, // Pass specific dataTable translations (for actions)
            confirm, // Pass confirm function from hook
            onTaxDeleted: (taxId) => {
                queryClient.invalidateQueries({ queryKey: ['taxes'] });
            },
            openEditDialog: openEditDialog,
        }),
        // Add hook values to dependency array
        [locale, tTable, tShared, tSharedDataTable, confirm, queryClient, openEditDialog]
    );


    const table = useReactTable({
        data: pageData,
        columns,
        pageCount: totalPages, // Set page count for pagination
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
            pagination,
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination: true, // We handle pagination server-side
        manualSorting: true, // We handle sorting server-side
        manualFiltering: true, // We handle filtering server-side
        debugTable: process.env.NODE_ENV === 'development', // Enable debug logs in dev
    });

    return (
        <div className="space-y-4">
            {/* Remove t and tShared props from DataTableToolbar */}
            <DataTableToolbar
                table={table}
                // t={t} // Removed
                // tShared={tShared} // Removed
                onAddTax={openAddDialog} // Pass function to open add dialog
            />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            // Loading Skeleton
                            Array.from({ length: pagination.pageSize }).map((_, i) => (
                                <TableRow key={`skeleton-${i}`}>
                                    {columns.map((column, j) => (
                                        <TableCell key={`skeleton-${i}-${j}`}>
                                            <Skeleton className="h-6 w-full" />
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && "selected"}
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className="h-24 text-center"
                                >
                                    {error ? tSharedDataTable("errorLoadingData", { error: (error as Error).message }) : t("noResults")}
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            {/* DataTablePagination should fetch its own translations */}
            <DataTablePagination table={table} />

            {/* Render the ConfirmDialog instance from the hook */}
            <ConfirmDialog />

            {/* Add/Edit Dialog - Remove t and tShared props */}
            <AddEditTaxDialog
                isOpen={isAddEditOpen}
                onOpenChange={setIsAddEditOpen}
                tax={editingTax} // Pass the tax to edit, or null for adding
                onSuccess={handleSuccess} // Callback on success
                // t={t} // Removed
                // tShared={tShared} // Removed
            />
        </div>
    );
}
