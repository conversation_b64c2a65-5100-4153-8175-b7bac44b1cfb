"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { updateAcademicYear } from "@/lib/api/admin/academic-year";
import { UpdateAcademicYearInput, updateAcademicYearSchema } from "@/lib/schemas/admin/academic-year";
import { AcademicYearDto } from "@/lib/dto/admin/academic-year.dto";
import { DatePicker } from "@/components/ui/date-picker"; // Assuming DatePicker component exists

interface EditAcademicYearDialogProps {
    academicYear: AcademicYearDto;
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
}

export function EditAcademicYearDialog({ academicYear, isOpen, onOpenChange }: EditAcademicYearDialogProps) {
    const t = useTranslations("AdminAcademicYearsPage.EditDialog");
    const tValidation = useTranslations("AdminAcademicYearsPage.EditDialog.validation");
    const queryClient = useQueryClient();

    const formSchema = updateAcademicYearSchema(tValidation);
    const form = useForm<UpdateAcademicYearInput>({
        resolver: zodResolver(formSchema),
        // Initialize with current values, converting strings to Dates
        defaultValues: {
            name: academicYear.name || "",
            // Safely create Date objects, defaulting to undefined if invalid
            startDate: academicYear.startDate ? new Date(academicYear.startDate + 'T00:00:00') : undefined,
            endDate: academicYear.endDate ? new Date(academicYear.endDate + 'T00:00:00') : undefined,
        },
    });

    // Reset form when the dialog opens or the academicYear prop changes
    // Reset form when the dialog opens or the academicYear prop changes
    useEffect(() => {
        if (isOpen) {
            form.reset({
                name: academicYear.name || "",
                // Safely create Date objects for reset
                startDate: academicYear.startDate ? new Date(academicYear.startDate + 'T00:00:00') : undefined,
                endDate: academicYear.endDate ? new Date(academicYear.endDate + 'T00:00:00') : undefined,
            });
        }
    }, [academicYear, isOpen, form]);

    const mutation = useMutation({
        mutationFn: (data: UpdateAcademicYearInput) => updateAcademicYear(academicYear.id, data),
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.name }));
            queryClient.invalidateQueries({ queryKey: ["academic-years"] });
            onOpenChange(false); // Close dialog
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message }));
        },
    });

    // Helper to format Date to YYYY-MM-DD string, handling potential undefined
    const formatDate = (date: Date | undefined): string | undefined => {
        if (!date) return undefined;
        // Adjust for timezone offset to get correct YYYY-MM-DD in local time
        const adjustedDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
        return adjustedDate.toISOString().split('T')[0];
    };

    const onSubmit = (values: UpdateAcademicYearInput) => {
        // Format dates before sending to API
        const payload: UpdateAcademicYearRequest = {
            name: values.name !== academicYear.name ? values.name : undefined,
            startDate: values.startDate && formatDate(values.startDate) !== academicYear.startDate ? formatDate(values.startDate) : undefined,
            endDate: values.endDate && formatDate(values.endDate) !== academicYear.endDate ? formatDate(values.endDate) : undefined,
        };

        // Filter out undefined values (fields that weren't changed)
        const changedValues = Object.fromEntries(
            Object.entries(payload).filter(([_, v]) => v !== undefined)
        );


        if (Object.keys(changedValues).length > 0) {
            console.log("Updating Academic Year:", academicYear.id, changedValues);
            mutation.mutate(changedValues as UpdateAcademicYearRequest); // Assert type after filtering
        } else {
            toast.info(t("noChanges"));
            onOpenChange(false); // Close if no changes
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description", { name: academicYear.name })}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="space-y-4 p-1">
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("nameLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("namePlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <FormField
                                    control={form.control}
                                    name="startDate"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-col">
                                            <FormLabel>{t("startDateLabel")}</FormLabel>
                                            <DatePicker
                                                date={field.value} // Pass Date object directly
                                                setDate={field.onChange} // Pass onChange directly
                                                placeholder={t("startDatePlaceholder")}
                                            />
                                            <FormMessage /> {/* RHF will handle error display */}
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="endDate"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-col">
                                            <FormLabel>{t("endDateLabel")}</FormLabel>
                                             <DatePicker
                                                date={field.value} // Pass Date object directly
                                                setDate={field.onChange} // Pass onChange directly
                                                placeholder={t("endDatePlaceholder")}
                                                fromDate={form.watch("startDate")} // Pass Date object directly
                                            />
                                            <FormMessage /> {/* RHF will handle error display */}
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={mutation.isPending}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
