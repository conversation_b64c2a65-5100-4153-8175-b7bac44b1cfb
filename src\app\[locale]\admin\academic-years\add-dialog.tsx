"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PlusCircle } from "lucide-react";
import { createAcademicYear } from "@/lib/api/admin/academic-year";
import { CreateAcademicYearInput, createAcademicYearSchema } from "@/lib/schemas/admin/academic-year";
import { DatePicker } from "@/components/ui/date-picker"; // Assuming DatePicker component exists

export function AddAcademicYearDialog() {
    const [isOpen, setIsOpen] = useState(false);
    const [hasMounted, setHasMounted] = useState(false);

    useEffect(() => {
        setHasMounted(true);
    }, []);

    const t = useTranslations("AdminAcademicYearsPage.AddDialog");
    const tValidation = useTranslations("AdminAcademicYearsPage.AddDialog.validation");
    const queryClient = useQueryClient();

    const formSchema = createAcademicYearSchema(tValidation);
    const form = useForm<CreateAcademicYearInput>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: "",
            startDate: undefined, // Initialize dates as undefined
            endDate: undefined,   // Initialize dates as undefined
        },
    });

    const mutation = useMutation({
        mutationFn: createAcademicYear,
        onSuccess: (data) => {
            toast.success(t("successToast", { name: data.name }));
            queryClient.invalidateQueries({ queryKey: ["academic-years"] });
            setIsOpen(false);
            form.reset();
        },
        onError: (error) => {
            toast.error(t("errorToast", { error: error.message }));
        },
    });

    // Helper to format Date to YYYY-MM-DD string, handling potential undefined
    const formatDate = (date: Date | undefined): string | undefined => {
        if (!date) return undefined;
        // Adjust for timezone offset to get correct YYYY-MM-DD in local time
        const adjustedDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
        return adjustedDate.toISOString().split('T')[0];
    };

    const onSubmit = (values: CreateAcademicYearInput) => {
        // Format dates before sending to API
        const payload = {
            ...values,
            startDate: formatDate(values.startDate)!, // Assert non-null as schema requires it
            endDate: formatDate(values.endDate)!,   // Assert non-null as schema requires it
        };
        console.log("Submitting Academic Year:", payload);
        mutation.mutate(payload);
    };

    if (!hasMounted) {
        return (
            <Button onClick={() => setIsOpen(true)}>
                <PlusCircle className="mr-2 h-4 w-4" />
                {t("triggerButton")}
            </Button>
        );
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t("triggerButton")}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description")}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="space-y-4 p-1">
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("nameLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("namePlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <FormField
                                    control={form.control}
                                    name="startDate"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-col">
                                            <FormLabel>{t("startDateLabel")}</FormLabel>
                                            <DatePicker
                                                date={field.value} // Pass Date object directly
                                                setDate={field.onChange} // Pass onChange directly
                                                placeholder={t("startDatePlaceholder")}
                                            />
                                            <FormMessage /> {/* RHF will handle error display */}
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="endDate"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-col">
                                            <FormLabel>{t("endDateLabel")}</FormLabel>
                                             <DatePicker
                                                date={field.value} // Pass Date object directly
                                                setDate={field.onChange} // Pass onChange directly
                                                placeholder={t("endDatePlaceholder")}
                                                // Disable dates before start date
                                                fromDate={form.watch("startDate")} // Pass Date object directly
                                            />
                                            <FormMessage /> {/* RHF will handle error display */}
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={mutation.isPending}>
                                {t("cancelButton")}
                            </Button>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("savingButton") : t("saveButton")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
