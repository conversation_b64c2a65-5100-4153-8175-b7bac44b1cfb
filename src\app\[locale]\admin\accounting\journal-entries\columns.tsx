"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useRouter } from "@/i18n/navigation"; // Import useRouter
import { JournalEntryDto } from "@/lib/dto/admin/accounting/journal-entries.dto";
import { ColumnDef } from "@tanstack/react-table";
import { format } from 'date-fns';
import { MoreHorizontal } from "lucide-react";
import { useTranslations } from "next-intl";
// TODO: Import necessary actions/modals (Edit, Delete, Post)

// Helper function to format currency
const formatCurrency = (amount: number) => {
    // Consistent with project context: SAR
    return new Intl.NumberFormat('en-SA', { style: 'currency', currency: 'SAR' }).format(amount);
};

export const getColumns = (
    // Pass necessary functions for actions if needed
    // openEditModal: (entry: JournalEntryDto) => void,
    // openDeleteConfirm: (entry: JournalEntryDto) => void,
    openPostConfirm: (entryId: string) => void, // Accept post handler
    router: ReturnType<typeof useRouter>, // Accept router instance
    t: ReturnType<typeof useTranslations<"AdminJournalEntriesPage.table">>, // Accept t function
    tShared: ReturnType<typeof useTranslations<"Shared.dataTable">> // Accept tShared function
): ColumnDef<JournalEntryDto>[] => {
    // Router and handlers are now passed as arguments

    return [
        {
            accessorKey: "journalEntryNumber",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("journalEntryNumber")} />
            ),
            cell: ({ row }) => row.getValue("journalEntryNumber"),
        },
        {
            accessorKey: "entryDate",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("entryDate")} />
            ),
            cell: ({ row }) => format(new Date(row.getValue("entryDate")), 'yyyy-MM-dd'),
        },
        {
            accessorKey: "referenceNumber",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("referenceNumber")} />
            ),
            cell: ({ row }) => row.getValue("referenceNumber") || 'N/A',
        },
        {
            accessorKey: "description",
            header: t("description"),
            cell: ({ row }) => (
                <div className="truncate max-w-xs">{row.getValue("description")}</div>
            ),
        },
        {
            id: "totalAmount", // Custom ID for calculated column
            header: t("totalAmount"),
            cell: ({ row }) => {
                const lines = row.original.lines;
                const totalDebits = lines
                    .filter(line => line.type === 'DEBIT')
                    .reduce((sum, line) => sum + line.amount, 0);
                // Debits should equal credits, so just show one side
                return formatCurrency(totalDebits);
            },
            enableSorting: false,
        },
        {
            accessorKey: "postedDate",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("status")} />
            ),
            cell: ({ row }) => {
                const isPosted = !!row.getValue("postedDate");
                return (
                    <Badge variant={isPosted ? "success" : "outline"}>
                        {isPosted ? t("posted") : t("unposted")}
                    </Badge>
                );
            },
            filterFn: (row, id, value) => {
                const isPosted = !!row.getValue("postedDate");
                // value will be 'true', 'false', or 'all' (or undefined)
                if (value === 'all' || value === undefined) return true;
                return String(isPosted) === value;
            },
        },
        {
            id: "actions",
            header: tShared("actions"),
            cell: ({ row }) => {
                const entry = row.original;
                const isPosted = !!entry.postedDate;

                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">{tShared("openMenu")}</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>{tShared("actions")}</DropdownMenuLabel>
                            <DropdownMenuItem
                                onClick={() => router.push(`/admin/accounting/journal-entries/${entry.id}`)}
                            >
                                {tShared("view")}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                // onClick={() => openEditModal(entry)} // TODO: Implement Edit
                                disabled={isPosted} // Disable edit if posted
                            >
                                {tShared("edit")}
                            </DropdownMenuItem>
                            {!isPosted && ( // Show Post only if not posted
                                <DropdownMenuItem
                                    onClick={() => openPostConfirm(entry.id)} // Call the passed handler
                                >
                                    {t("postAction")}
                                </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                                className="text-destructive focus:text-destructive focus:bg-destructive/10"
                                // onClick={() => openDeleteConfirm(entry)} // TODO: Implement Delete
                                disabled={isPosted} // Disable delete if posted
                            >
                                {tShared("delete")}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
            enableSorting: false,
            enableHiding: false,
        },
    ];
};
