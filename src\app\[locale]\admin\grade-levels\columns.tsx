"use client";

import { ColumnDef } from "@tanstack/react-table";
import { useTranslations } from 'next-intl';
import { ArrowUpDown } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { GradeLevelDto } from "@/lib/dto/admin/grade-level.dto";
import { EducationalStageDto } from "@/lib/dto/admin/educational-stage.dto";
import { DataTableRowActions } from './data-table-row-actions';

// Enhanced type that includes educational stage information
export type EnhancedGradeLevelDto = GradeLevelDto & {
  educationalStage?: EducationalStageDto;
};

export const createColumns = (onRefetch?: () => void): ColumnDef<EnhancedGradeLevelDto>[] => [
  {
    accessorKey: "nameEn",
    header: ({ column }) => {
      const t = useTranslations('AdminGradeLevelsPage.table');
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          {t('nameEn')}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="font-medium">
          {row.getValue("nameEn")}
        </div>
      );
    },
  },
  {
    accessorKey: "nameAr",
    header: ({ column }) => {
      const t = useTranslations('AdminGradeLevelsPage.table');
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          {t('nameAr')}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="font-medium" dir="rtl">
          {row.getValue("nameAr")}
        </div>
      );
    },
  },
  {
    accessorKey: "levelOrder",
    header: ({ column }) => {
      const t = useTranslations('AdminGradeLevelsPage.table');
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          {t('levelOrder')}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <Badge variant="secondary" className="font-mono">
          {row.getValue("levelOrder")}
        </Badge>
      );
    },
  },
  {
    accessorKey: "educationalStage",
    header: ({ column }) => {
      const t = useTranslations('AdminGradeLevelsPage.table');
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          {t('educationalStage')}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const educationalStage = row.getValue("educationalStage") as EducationalStageDto | undefined;
      return (
        <div className="flex flex-col space-y-1">
          <span className="font-medium">{educationalStage?.nameEn || 'N/A'}</span>
          {educationalStage?.nameAr && (
            <span className="text-sm text-muted-foreground" dir="rtl">
              {educationalStage.nameAr}
            </span>
          )}
        </div>
      );
    },
    sortingFn: (rowA, rowB) => {
      const stageA = rowA.getValue("educationalStage") as EducationalStageDto | undefined;
      const stageB = rowB.getValue("educationalStage") as EducationalStageDto | undefined;

      if (!stageA && !stageB) return 0;
      if (!stageA) return 1;
      if (!stageB) return -1;

      return stageA.nameEn.localeCompare(stageB.nameEn);
    },
  },
  {
    id: "actions",
    header: () => {
      const t = useTranslations('AdminGradeLevelsPage.table');
      return <span className="font-semibold">{t('actions')}</span>;
    },
    cell: ({ row }) => <DataTableRowActions row={row} onRefetch={onRefetch} />,
  },
];
