// Common DTOs used across multiple API responses, particularly for pagination.

/**
 * Represents the sorting criteria used in paginated requests.
 */
export interface SortObject {
  direction?: string; // e.g., "ASC", "DESC"
  nullHandling?: string; // e.g., "NATIVE", "NULLS_FIRST", "NULLS_LAST"
  ascending?: boolean;
  property?: string; // The field name to sort by
  ignoreCase?: boolean;
}

/**
 * Represents the pagination information returned by the API.
 */
export interface PageableObject {
  offset?: number; // The offset of the current page
  sort?: SortObject[]; // Array of sorting criteria
  unpaged?: boolean; // Whether the request was unpaged
  paged?: boolean; // Whether the request was paged
  pageNumber?: number; // The current page number (zero-based)
  pageSize?: number; // The number of items per page
}

/**
 * Generic interface for paginated API responses.
 */
export interface Page<T> {
  totalPages: number;
  totalElements: number;
  size: number;
  content: T[];
  number: number; // Current page number (zero-based)
  sort: SortObject[];
  pageable: PageableObject;
  numberOfElements: number; // Number of elements in the current page
  first: boolean; // Is this the first page?
  last: boolean; // Is this the last page?
  empty: boolean; // Is the current page empty?
}
