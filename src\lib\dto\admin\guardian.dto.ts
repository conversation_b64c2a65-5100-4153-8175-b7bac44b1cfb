import { Page } from "@/lib/dto/page";
import { UserDto } from "@/lib/dto/admin/user.dto";
import { SimpleStudentDto } from "@/lib/dto/admin/student.dto";

/**
 * Data Transfer Object for Guardian details.
 * Based on GET /api/v1/guardians/{guardianId} response schema.
 */
export interface GuardianDto {
    id: string;
    userAccount: UserDto;
    occupation: string;
    address?: string;
    nationalId: string;
    idType: IdType;
    students: SimpleStudentDto[];
    createdAt: string; // ISO DateTime string
    updatedAt: string; // ISO DateTime string
}

/**
 * Simplified DTO for Guardian, often used in lists or dropdowns.
 */
export interface SimpleGuardianDto {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    occupation: string;
    nationalId: string;
    idType: IdType;
}

/**
 * Type for ID document types
 */
export type IdType = "NATIONAL_ID" | "PASSPORT";

/**
 * Request body for creating a new Guardian profile with an existing user.
 * Based on the old POST /api/v1/guardians/profiles endpoint.
 */
export interface CreateGuardianProfileRequest {
    userId: string;
    occupation: string;
    address?: string;
    nationalId: string;
    idType: IdType;
}

/**
 * Request body for creating a new Guardian with a new user account.
 * Based on POST /api/v1/guardians/register endpoint.
 */
export interface CreateGuardianWithUserRequest {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phoneNumber?: string;
    occupation: string;
    address?: string;
    nationalId: string;
    idType: IdType;
}

/**
 * Request body for updating an existing Guardian profile.
 */
export interface UpdateGuardianProfileRequest {
    occupation?: string;
    address?: string;
    nationalId?: string;
    idType?: IdType;
}

/**
 * Paginated response for Guardian list.
 */
export type PageGuardianDto = Page<GuardianDto>;

/**
 * Parameters for fetching Guardians with pagination and filtering.
 */
export interface GetGuardiansParams {
    page?: number;
    size?: number;
    sort?: string[];
    search?: string;
}
