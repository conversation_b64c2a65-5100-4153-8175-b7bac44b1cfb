import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { GuardiansDataTable } from "./data-table"; // Import the client component
import { getTranslations } from "next-intl/server";

export default async function GuardiansPage() {
    const t = await getTranslations("AdminGuardiansPage");

    return (
        <Card>
            <CardHeader>
                <CardTitle>{t("title")}</CardTitle>
                <CardDescription>{t("description")}</CardDescription>
            </CardHeader>
            <CardContent>
                {/* Data table is a client component handling its own data fetching */}
                <GuardiansDataTable />
            </CardContent>
        </Card>
    );
}
