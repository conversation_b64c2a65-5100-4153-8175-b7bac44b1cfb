"use client";

import { Table } from "@tanstack/react-table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { DataTableViewOptions } from "@/components/ui/data-table/data-table-view-options";
import { AddFeeDialog } from "./add-fee-dialog"; // Import the Add dialog
import { useTranslations } from "next-intl";
import { FeeCategoryDto } from "@/lib/dto/admin/accounting/fee-categories.dto";
import { AcademicYearDto } from "@/lib/dto/admin/academic-year.dto"; // Add academic year DTO
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { getAllFeeCategoriesList } from "@/lib/api/admin/accounting/fee-categories"; // Assuming this API function exists
import { getAllAcademicYearsList } from "@/lib/api/admin/academic-year"; // Add academic years API

interface DataTableToolbarProps<TData> {
    table: Table<TData>;
    onFeeAdded?: () => void; // Callback for when a fee is added
}

// Helper function to fetch all fee categories (assuming it returns a simple list)
async function fetchAllFeeCategories(): Promise<FeeCategoryDto[]> {
    // You might need a dedicated API endpoint that returns all categories without pagination
    // For now, let's assume `getAllFeeCategoriesList` exists or adapt `getFeeCategories`
    // This is a placeholder implementation - replace with your actual API call
    try {
        // If getAllFeeCategoriesList exists and returns FeeCategoryDto[]
         return await getAllFeeCategoriesList();

        // Or adapt getFeeCategories if it can fetch all with large size
        // const response = await getFeeCategories({ page: 0, size: 1000 }); // Fetch a large number
        // return response.content;
    } catch (error) {
        console.error("Failed to fetch fee categories for filter:", error);
        return []; // Return empty array on error
    }
}

// Helper function to fetch all academic years
async function fetchAllAcademicYears(): Promise<AcademicYearDto[]> {
    try {
        return await getAllAcademicYearsList();
    } catch (error) {
        console.error("Failed to fetch academic years:", error);
        return [];
    }
}


export function DataTableToolbar<TData>({
    table,
    onFeeAdded,
}: DataTableToolbarProps<TData>) {
    const t = useTranslations("AdminFeesPage");
    const tShared = useTranslations("Shared.dataTable");

    const isFiltered = table.getState().columnFilters.length > 0;

    // Fetch fee categories for the filter dropdown
    const { data: feeCategories, isLoading: isLoadingCategories } = useQuery({
        queryKey: ["feeCategoriesList"],
        queryFn: fetchAllFeeCategories,
        staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    });

    // Fetch academic years for the filter dropdown
    const { data: academicYears, isLoading: isLoadingAcademicYears } = useQuery({
        queryKey: ["academicYearsList"],
        queryFn: fetchAllAcademicYears,
        staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    });

    const academicYearFilterValue = table.getColumn("academicYearId")?.getFilterValue() as string ?? "";
    const categoryFilterValue = table.getColumn("feeCategory")?.getFilterValue() as string ?? "";

    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
                {/* Academic Year Filter Select */}
                <Select
                    value={academicYearFilterValue}
                    onValueChange={(value) => {
                        table.getColumn("academicYearId")?.setFilterValue(value === "all" ? "" : value);
                    }}
                    disabled={isLoadingAcademicYears}
                >
                    <SelectTrigger className="h-8 w-full sm:w-[150px] lg:w-[200px]">
                        <SelectValue placeholder={t("filterByAcademicYearPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">{t("allAcademicYears")}</SelectItem>
                        {academicYears?.map((academicYear) => (
                            <SelectItem key={academicYear.id} value={academicYear.id}>
                                {academicYear.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                {/* Fee Category Filter Select */}
                <Select
                    value={categoryFilterValue}
                    onValueChange={(value) => {
                        table.getColumn("feeCategory")?.setFilterValue(value === "all" ? "" : value);
                    }}
                    disabled={isLoadingCategories}
                >
                    <SelectTrigger className="h-8 w-full sm:w-[180px] lg:w-[220px]">
                        <SelectValue placeholder={t("filterByCategoryPlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">{t("allCategories")}</SelectItem>
                        {feeCategories?.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                                {/* Display name based on locale preference if available */}
                                {category.nameEn} / {category.nameAr}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                {/* Reset Button */}
                {isFiltered && (
                    <Button
                        variant="ghost"
                        onClick={() => table.resetColumnFilters()}
                        className="h-8 px-2 lg:px-3"
                    >
                        {t("resetFilters")}
                        <X className="ml-2 h-4 w-4" />
                    </Button>
                )}
            </div>
            <div className="flex items-center space-x-2">
                <DataTableViewOptions table={table} t={tShared}/>
                <AddFeeDialog onFeeAdded={onFeeAdded} />
            </div>
        </div>
    );
}
