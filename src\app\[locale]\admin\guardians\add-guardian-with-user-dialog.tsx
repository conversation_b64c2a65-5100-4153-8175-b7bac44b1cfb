"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { PlusCircle } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { createGuardianWithUser } from "@/lib/api/admin/guardians";
import { CreateGuardianWithUserInput, createGuardianWithUserSchema } from "@/lib/schemas/admin/guardian";
import { getErrorMessage } from "@/lib/utils";

interface AddGuardianWithUserDialogProps {
    onSuccess?: () => void; // Optional callback
}

export function AddGuardianWithUserDialog({ onSuccess }: AddGuardianWithUserDialogProps) {
    const t = useTranslations("AdminGuardiansPage.AddWithUserDialog");
    const queryClient = useQueryClient();
    const [isOpen, setIsOpen] = React.useState(false);

    const form = useForm<CreateGuardianWithUserInput>({
        resolver: zodResolver(createGuardianWithUserSchema),
        defaultValues: {
            firstName: "",
            lastName: "",
            email: "",
            password: "",
            phoneNumber: "",
            occupation: "",
            address: "",
            nationalId: "",
            idType: "NATIONAL_ID",
        },
    });

    const mutation = useMutation({
        mutationFn: createGuardianWithUser,
        onSuccess: (data) => {
            const name = data.userAccount ?
                `${data.userAccount.firstName || ''} ${data.userAccount.lastName || ''}`.trim() :
                'Guardian';
            toast.success(t("successToast", { name }));
            queryClient.invalidateQueries({ queryKey: ["guardians"] });
            setIsOpen(false); // Close dialog
            form.reset(); // Reset form
            onSuccess?.();
        },
        onError: (error) => {
            console.error("Guardian creation error:", error);
            const errorMessage = getErrorMessage(error);

            // Handle specific error cases
            if (errorMessage.includes("email already exists")) {
                form.setError("email", {
                    type: "manual",
                    message: "This email is already registered. Please use a different email."
                });
            }

            toast.error(t("errorToast", { error: errorMessage }));
        },
    });

    const onSubmit = (data: CreateGuardianWithUserInput) => {
        mutation.mutate(data);
    };

    // Reset form when dialog opens/closes
    React.useEffect(() => {
        if (!isOpen) {
            form.reset();
        }
    }, [isOpen, form]);

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button size="sm" className="ml-auto h-8">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {t("triggerButton")}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>{t("title")}</DialogTitle>
                    <DialogDescription>{t("description")}</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4 py-4">
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="firstName"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("firstNameLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("firstNamePlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="lastName"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("lastNameLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("lastNamePlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("emailLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("emailPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="phoneNumber"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("phoneNumberLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("phoneNumberPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <FormField
                            control={form.control}
                            name="password"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("passwordLabel")}</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="password"
                                            placeholder={t("passwordPlaceholder")}
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormDescription>{t("passwordDescription")}</FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="occupation"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("occupationLabel")}</FormLabel>
                                    <FormControl>
                                        <Input placeholder={t("occupationPlaceholder")} {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="address"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("addressLabel")}</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder={t("addressPlaceholder")}
                                            className="resize-none"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="nationalId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("nationalIdLabel")}</FormLabel>
                                        <FormControl>
                                            <Input placeholder={t("nationalIdPlaceholder")} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="idType"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>{t("idTypeLabel")}</FormLabel>
                                        <Select
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder={t("idTypePlaceholder")} />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem value="NATIONAL_ID">{t("idTypeNationalId")}</SelectItem>
                                                <SelectItem value="PASSPORT">{t("idTypePassport")}</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <DialogFooter>
                            <Button type="submit" disabled={mutation.isPending}>
                                {mutation.isPending ? t("submitting") : t("submit")}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
