import { Column } from "@tanstack/react-table";
import { ArrowDown, ArrowU<PERSON>, ChevronsUpDown, EyeOff } from "lucide-react";
import { useTranslations } from "next-intl";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface DataTableColumnHeaderProps<TData, TValue>
    extends React.HTMLAttributes<HTMLDivElement> {
    column: Column<TData, TValue>;
    title: string;
}

export function DataTableColumnHeader<TData, TValue>({
    column,
    title,
    className,
}: DataTableColumnHeaderProps<TData, TValue>) {
    const t = useTranslations("Shared.dataTable"); // Assuming shared translations

    if (!column.getCanSort() && !column.getCanHide()) {
        return <div className={cn(className)}>{title}</div>;
    }

    return (
        <div className={cn("flex items-center space-x-2", className)}>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        aria-label={
                            column.getIsSorted() === "desc"
                                ? t("sortedDescending")
                                : column.getIsSorted() === "asc"
                                  ? t("sortedAscending")
                                  : t("notSorted")
                        }
                        variant="ghost"
                        size="sm"
                        className="-ml-3 h-8 data-[state=open]:bg-accent"
                    >
                        <span>{title}</span>
                        {column.getCanSort() && column.getIsSorted() === "desc" ? (
                            <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                        ) : column.getIsSorted() === "asc" ? (
                            <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                        ) : (
                            <ChevronsUpDown className="ml-2 h-4 w-4" aria-hidden="true" />
                        )}
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                    {column.getCanSort() && (
                        <>
                            <DropdownMenuItem
                                aria-label={t("sortAscending")}
                                onClick={() => column.toggleSorting(false)}
                            >
                                <ArrowUp
                                    className="mr-2 h-3.5 w-3.5 text-muted-foreground/70"
                                    aria-hidden="true"
                                />
                                {t("asc")}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                aria-label={t("sortDescending")}
                                onClick={() => column.toggleSorting(true)}
                            >
                                <ArrowDown
                                    className="mr-2 h-3.5 w-3.5 text-muted-foreground/70"
                                    aria-hidden="true"
                                />
                                {t("desc")}
                            </DropdownMenuItem>
                        </>
                    )}
                    {column.getCanSort() && column.getCanHide() && (
                        <DropdownMenuSeparator />
                    )}
                    {column.getCanHide() && (
                        <DropdownMenuItem
                            aria-label={t("hideColumn")}
                            onClick={() => column.toggleVisibility(false)}
                        >
                            <EyeOff
                                className="mr-2 h-3.5 w-3.5 text-muted-foreground/70"
                                aria-hidden="true"
                            />
                            {t("hide")}
                        </DropdownMenuItem>
                    )}
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
}
