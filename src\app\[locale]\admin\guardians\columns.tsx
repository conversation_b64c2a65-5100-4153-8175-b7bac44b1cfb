"use client";

import { ColumnDef } from "@tanstack/react-table";
import { UseTranslations } from "next-intl";
import { GuardianDto } from "@/lib/dto/admin/guardian.dto";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/ui/data-table/data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions";
import { ClientDateFormatter } from "./client-date-formatter";

interface GetColumnsProps {
    t: UseTranslations<"AdminGuardiansPage.table">;
    tShared: UseTranslations<"Shared.dataTable">;
}

export function getColumns({ t, tShared }: GetColumnsProps): ColumnDef<GuardianDto>[] {
    return [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label={tShared("selectAll")}
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label={tShared("selectRow")}
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "userAccount.firstName",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("firstName")} />
            ),
            cell: ({ row }) => <div>{row.original.userAccount?.firstName || "-"}</div>,
        },
        {
            accessorKey: "userAccount.lastName",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("lastName")} />
            ),
            cell: ({ row }) => <div>{row.original.userAccount?.lastName || "-"}</div>,
        },
        {
            accessorKey: "email",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("email")} />
            ),
            cell: ({ row }) => <div>{row.original.userAccount?.email || "-"}</div>,
            filterFn: (row, id, value) => {
                return row.original.userAccount?.email?.toLowerCase().includes(value.toLowerCase()) || false;
            },
        },
        {
            accessorKey: "userAccount.phoneNumber",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("phoneNumber")} />
            ),
            cell: ({ row }) => <div>{row.original.userAccount?.phoneNumber || "-"}</div>,
        },
        {
            accessorKey: "occupation",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("occupation")} />
            ),
            cell: ({ row }) => <div>{row.original.occupation}</div>,
        },
        {
            accessorKey: "nationalId",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("nationalId")} />
            ),
            cell: ({ row }) => (
                <div className="flex items-center">
                    <span>{row.original.nationalId}</span>
                    <Badge variant="outline" className="ml-2">
                        {row.original.idType}
                    </Badge>
                </div>
            ),
        },
        {
            accessorKey: "students",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("students")} />
            ),
            cell: ({ row }) => (
                <div>
                    {row.original.students && row.original.students.length > 0 ? (
                        <Badge variant="secondary">
                            {t("studentsCount", { count: row.original.students.length })}
                        </Badge>
                    ) : (
                        <span className="text-muted-foreground">{t("noStudents")}</span>
                    )}
                </div>
            ),
            enableSorting: false,
        },
        {
            accessorKey: "createdAt",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t("createdAt")} />
            ),
            cell: ({ row }) => (
                <div><ClientDateFormatter date={row.original.createdAt} /></div>
            ),
        },
        {
            id: "actions",
            cell: ({ row }) => <DataTableRowActions row={row} />,
        },
    ];
}
