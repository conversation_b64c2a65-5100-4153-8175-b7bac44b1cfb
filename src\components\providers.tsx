"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import React, { useState } from "react";

import { RouteChangeProvider } from "./providers/route-change-provider";
import { Toaster } from "sonner"; // Import directly from sonner

export function Providers({ children }: { children: React.ReactNode }) {
  // Use useState to ensure QueryClient is only created once per component instance
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      <RouteChangeProvider>
        {children}
        <Toaster />
      </RouteChangeProvider>
    </QueryClientProvider>
  );
}
