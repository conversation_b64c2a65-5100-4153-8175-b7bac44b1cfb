import { PageGeneralLedgerEntryDto, TrialBalanceDto } from "@/lib/dto/admin/accounting/general-ledger.dto";

import { API_BASE_URL } from "@/lib/constants";
import { fetchWithAuth } from "@/lib/fetch-with-auth";
import { useQuery } from "@tanstack/react-query";

interface GetGeneralLedgerParams {
  startDate?: string;
  endDate?: string;
  accountIds?: string[];
  page?: number;
  size?: number;
  sort?: string[];
  isPosted?: boolean | null;
}

export const useGetGeneralLedger = (params: GetGeneralLedgerParams) => {
  const queryParams = new URLSearchParams();
  if (params.startDate) queryParams.append("startDate", params.startDate);
  if (params.endDate) queryParams.append("endDate", params.endDate);
  if (params.accountIds && params.accountIds.length > 0) {
    params.accountIds.forEach(id => queryParams.append("accountIds", id));
  }
  if (params.page !== undefined) queryParams.append("page", params.page.toString());
  if (params.size !== undefined) queryParams.append("size", params.size.toString());
  if (params.sort && params.sort.length > 0) {
    params.sort.forEach(s => queryParams.append("sort", s));
  }
  if (params.isPosted !== undefined && params.isPosted !== null) {
    queryParams.append("isPosted", params.isPosted.toString());
  } else if (params.isPosted === null) {
    queryParams.append("isPosted", "null"); // Or whatever the API expects for null
  }

  const queryString = queryParams.toString();
  const url = `${API_BASE_URL}/accounting/general-ledger${queryString ? `?${queryString}` : ""}`;

  return useQuery<PageGeneralLedgerEntryDto>({
    queryKey: ["generalLedger", params],
    queryFn: async () => {
      const res = await fetchWithAuth(url);
      return res.json();
    },
    placeholderData: (previousData) => previousData, // Keep previous data while fetching next page/filters
  });
};

interface GetTrialBalanceParams {
  startDate: string;
  endDate: string;
  enabled?: boolean;
}

export const useGetTrialBalance = (params: GetTrialBalanceParams) => {
  const queryParams = new URLSearchParams();
  queryParams.append("startDate", params.startDate);
  queryParams.append("endDate", params.endDate);

  const queryString = queryParams.toString();
  const url = `${API_BASE_URL}/accounting/general-ledger/trial-balance${queryString ? `?${queryString}` : ""}`;

  return useQuery<TrialBalanceDto>({
    queryKey: ["trialBalance", params.startDate, params.endDate],
    queryFn: async () => {
      const res = await fetchWithAuth(url);
      return res.json();
    },
    enabled: params.enabled,
  });
};
